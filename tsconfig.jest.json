{"extends": "./tsconfig.json", "compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020", "DOM"], "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "downlevelIteration": true, "importHelpers": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "baseUrl": ".", "paths": {"@shared/*": ["shared/src/*"], "@server/*": ["server/src/*"], "@client/*": ["client/src/*"]}}, "include": ["**/*.test.ts", "**/*.spec.ts", "**/__tests__/**/*.ts", "jest.setup.js", "shared/**/*", "server/**/*", "client/**/*"], "exclude": ["node_modules", "dist", "build", "coverage"]}