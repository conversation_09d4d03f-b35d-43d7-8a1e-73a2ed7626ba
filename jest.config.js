/**
 * @file Simplified Jest Configuration
 * @filepath jest.config.js
 * @description Minimal Jest configuration to eliminate configuration errors
 */

module.exports = {
    // Test environment
    testEnvironment: 'node',
    
    // File extensions to handle
    moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
  
    // Transform TypeScript files - SIMPLIFIED
    preset: 'ts-jest',

    // ✅ ES6+ COMPATIBILITY: Use Jest-specific TypeScript configuration (modern syntax)
    transform: {
      '^.+\\.ts$': ['ts-jest', {
        tsconfig: 'tsconfig.jest.json'
      }]
    },
    
    // Test file patterns
    testMatch: [
      '**/__tests__/**/*.test.ts',
      '**/?(*.)+(spec|test).ts'
    ],
  
    // Setup files
    setupFilesAfterEnv: [
      '<rootDir>/jest.setup.js'
    ],
  
    // Module path mapping
    moduleNameMapper: {
      '^@/(.*)$': '<rootDir>/$1',
      '^shared/(.*)$': '<rootDir>/shared/$1'
    },
  
    // Ignore patterns
    testPathIgnorePatterns: [
      '/node_modules/',
      '/dist/',
      '/build/',
      '/coverage/',
    ],
  
    // CRITICAL: Disable coverage to prevent memory leaks
    collectCoverage: false,
  
    // Test timeout
    testTimeout: 30000,
  
    // Memory management
    maxWorkers: 1,
    logHeapUsage: true,
    detectOpenHandles: true,
    forceExit: true,
  
    // CRITICAL: Temporarily disable leak detection for debugging
    detectLeaks: false,
  
    // Clear mocks between tests
    clearMocks: true,
    resetModules: false,
  
    // Error handling
    errorOnDeprecated: false,
    testFailureExitCode: 1,
  
    // Cache directory
    cacheDirectory: '<rootDir>/.jest-cache',
  
    // Verbose output for debugging
    verbose: true,
  
    // Module loading optimizations
    modulePathIgnorePatterns: [
      '<rootDir>/dist/',
      '<rootDir>/build/',
      '<rootDir>/coverage/',
      '<rootDir>/.jest-cache/'
    ],
  
    // Transform ignore patterns
    transformIgnorePatterns: [
      'node_modules/(?!(.*\\.mjs$))'
    ],
  
    // Watchman for file watching
    watchman: true,
  
    // Don't bail on first failure
    bail: 0
  };
  