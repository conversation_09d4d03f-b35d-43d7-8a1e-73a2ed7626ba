// Jest setup file for test environment configuration

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.LOG_LEVEL = 'error';

// CRITICAL FIX: Global timer mocking to prevent ANY real timer creation (Lesson 04 pattern)
const mockSetInterval = jest.fn(() => 'mock-global-interval-id');
const mockClearInterval = jest.fn();
const mockSetTimeout = jest.fn(() => 'mock-global-timeout-id');
const mockClearTimeout = jest.fn();

// Override global timer functions BEFORE any modules load
global.setInterval = mockSetInterval;
global.clearInterval = mockClearInterval;
global.setTimeout = mockSetTimeout;
global.clearTimeout = mockClearTimeout;

console.log('[JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED');

// CRITICAL FIX: Let individual test files handle their own module mocking
// Global module mocking from setup files is problematic with relative paths
console.log('[JEST SETUP] Module mocking delegated to individual test files');

// CRITICAL FIX: Remove global fake timers to prevent memory leaks
// Individual tests should control their own timer mocking
// jest.useFakeTimers(); // REMOVED - causes massive memory retention

// Ensure a reliable performance.now in Node/Jest
try {
  const nodePerf = require('perf_hooks').performance;
  if (!global.performance || typeof global.performance.now !== 'function') {
    // Provide Node perf_hooks performance if missing
    global.performance = nodePerf;
  }
  // Wrap now() to guarantee finite numeric return; fallback to Date.now()
  const originalNow = global.performance.now.bind(global.performance);
  global.performance.now = () => {
    try {
      const t = originalNow();
      return Number.isFinite(t) ? t : Date.now();
    } catch {
      return Date.now();
    }
  };
} catch (e) {
  // As a last resort, define a minimal performance.now
  global.performance = global.performance || {};
  global.performance.now = () => Date.now();
}

// Global test timeout
jest.setTimeout(30000);

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Setup and teardown for each test
beforeEach(() => {
  jest.clearAllMocks();
});

afterEach(() => {
  jest.clearAllTimers();
});

// Global test utilities
global.testUtils = {
  delay: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
  createMockDate: (dateString) => new Date(dateString),
  generateUUID: () => 'test-uuid-' + Math.random().toString(36).substring(2, 11),
};
