npx jest server/src/platform/tracking/core-managers/__tests__/TrackingManager.test.ts --coverage --verbose --collectCoverageFrom="server/src/platform/tracking/core-managers/TrackingManager.ts"
  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  server/src/platform/tracking/core-managers/__tests__/TrackingManager.test.ts (71 MB heap size)
  Enterprise TrackingManager Test Suite
    Enterprise Instantiation & Configuration
      ✓ should instantiate with enterprise-grade configuration (6 ms)
      ✓ should validate configuration on instantiation (4 ms)
      ✓ should initialize with default configuration when no config provided (3 ms)
      ✓ should merge custom configuration with defaults (3 ms)
      ✓ should handle invalid configuration gracefully (7 ms)
    Enterprise Initialization & Lifecycle Management
      ✓ should initialize successfully with valid configuration (6 ms)
      ✓ should emit initialized event on successful initialization (4 ms)
      ✓ should start tracking operations successfully (3 ms)
      ✓ should stop tracking operations successfully (3 ms)
      ✓ should handle initialization without explicit start call (4 ms)
      ✓ should not allow double initialization (3 ms)
      ✓ should handle shutdown gracefully (3 ms)
      ✓ should handle multiple shutdown calls (9 ms)
      ✓ should handle lifecycle state transitions (4 ms)
      ✓ should handle initialization errors gracefully (4 ms)
    Core Tracking Operations & Data Processing
      ✓ should track data successfully with detailed tracking data (10 ms)
      ✓ should process tracking data through processTracking method (4 ms)
      ✓ should handle multiple tracking operations concurrently (6 ms)
      ✓ should handle batch processing when queue reaches threshold (10 ms)
      ✓ should validate tracking data structure (3 ms)
      ✓ should handle null and undefined tracking data (2 ms)
      ✓ should handle very large tracking data payloads (3 ms)
      ✓ should handle rapid tracking operations (6 ms)
      ✓ should maintain tracking data integrity during processing (3 ms)
      ✓ should handle tracking operations after stop/start cycle (3 ms)
    Enterprise Metrics & Performance Monitoring
      ✓ should get comprehensive manager metrics (4 ms)
      ✓ should get manager-specific metrics (5 ms)
      ✓ should get health status (3 ms)
      ✓ should get current manager status (2 ms)
      ✓ should track operation counters accurately (3 ms)
      ✓ should monitor memory usage (2 ms)
      ✓ should calculate error rates (2 ms)
      ✓ should track throughput metrics (4 ms)
      ✓ should handle performance under load (8 ms)
      ✓ should maintain metrics accuracy during concurrent operations (8 ms)
      ✓ should handle memory usage within enterprise bounds (2 ms)
    Enterprise Error Handling & Recovery
      ✓ should handle invalid tracking data gracefully (2 ms)
      ✓ should handle tracking operations when manager is not initialized (2 ms)
      ✓ should handle tracking operations after shutdown (2 ms)
      ✓ should handle malformed tracking data (2 ms)
      ✓ should handle circular reference in tracking data (3 ms)
      ✓ should handle extremely large tracking payloads (4 ms)
      ✓ should recover from processing errors (2 ms)
      ✓ should handle concurrent error scenarios (5 ms)
      ✓ should maintain system integrity during failures (3 ms)
      ✓ should handle memory pressure gracefully (9 ms)
      ✓ should handle rapid error recovery cycles (4 ms)
    Enterprise Integration & Interface Compliance
      ✓ should implement ITrackingManager interface correctly (2 ms)
      ✓ should implement IManagementService interface correctly (2 ms)
      ✓ should extend BaseTrackingService correctly (5 ms)
      ✓ should handle BaseTrackingService lifecycle correctly (2 ms)
      ✓ should integrate with timer coordination service (2 ms)
      ✓ should integrate with resilient timing infrastructure (2 ms)
      ✓ should handle cross-component integration (3 ms)
      ✓ should maintain enterprise compliance standards (2 ms)
      ✓ should handle enterprise scalability requirements (13 ms)
      ✓ should integrate with EventEmitter properly (2 ms)
      ✓ should handle configuration updates dynamically (2 ms)
      ✓ should maintain thread safety in concurrent scenarios (10 ms)
      ✓ should handle enterprise security requirements (2 ms)
      ✓ should maintain audit trail for enterprise compliance (3 ms)
    Private Method Coverage & Edge Cases
      ✓ should trigger _processBatch through queue threshold (8 ms)
      ✓ should handle _processBatch with empty queue (2 ms)
      ✓ should trigger _updateManagerMetrics error handling (2 ms)
      ✓ should handle performance threshold violations (3 ms)
      ✓ should handle _checkPerformanceThresholds with missing timer (3 ms)
      ✓ should handle memory pressure in _updateManagerMetrics (2 ms)
      ✓ should handle timer coordination service errors in shutdown (2 ms)
      ✓ should handle resilient timing initialization failures (3 ms)
      ✓ should handle _startMonitoring with timer coordination (2 ms)
    Configuration Validation & Boundary Conditions
      ✓ should handle extreme configuration values (2 ms)
      ✓ should handle zero and negative configuration values (2 ms)
      ✓ should handle missing configuration sections (2 ms)
      ✓ should handle boundary queue operations (3 ms)
    Error Recovery & Resilience Patterns
      ✓ should handle metrics collection failures (2 ms)
      ✓ should handle active operations capacity limits (8 ms)
      ✓ should handle performance data overflow (3 ms)
      ✓ should handle shutdown with active operations (6 ms)
      ✓ should handle doTrack error scenarios (3 ms)
    Surgical Precision Branch Testing
      ✓ should test all conditional branches in doTrack (2 ms)
      ✓ should test queue capacity eviction branch (4 ms)
      ✓ should test active operations eviction branch (5 ms)
      ✓ should test processTracking null data branch (3 ms)
      ✓ should test configuration validation branches (4 ms)
      ✓ should cover remaining uncovered branches and lines (7 ms)
      ✓ should test conditional branches for maximum coverage (3 ms)
      ✓ should test error handling branches in critical paths (3 ms)
      ✓ should achieve maximum branch coverage for all conditional paths (4 ms)
      ✓ should cover resilient timing initialization error path (2 ms)
      ✓ should cover doShutdown error handling path (44 ms)
      ✓ should cover timer coordination clearAllTimers fallback path (2 ms)
      ✓ should cover direct interval cleanup paths (2 ms)
      ✓ should cover performance data array slicing path (2 ms)
      ✓ should cover queue capacity eviction with exact boundary (3 ms)
      ✓ should cover active operations capacity eviction with exact boundary (5 ms)
      ✕ should cover doTrack error handling path with operation tracking (2 ms)
      ✕ should cover processTracking error handling path (4 ms)
      ✕ should cover configuration validation edge cases (5 ms)
      ✓ should cover monitoring configuration edge cases (3 ms)
      ✓ should cover security configuration edge cases (2 ms)
      ✓ should cover cache configuration edge cases (2 ms)
      ✓ should cover performance threshold alert paths (2 ms)
      ✓ should cover all remaining uncovered conditional branches (3 ms)
      ✕ should cover doTrack error handling with active operation status updates (lines 332, 334-348) (2 ms)
      ✕ should cover processTracking error return path (lines 437, 495) (3 ms)
      ✓ should cover resilient timing initialization fallback scenarios (lines 848-850, 871-872, 892-893) (2 ms)
      ✓ should cover performance threshold violations and cleanup edge cases (lines 911, 929, 941) (2 ms)
      ✓ should cover shutdown procedure error handling and monitoring cleanup (lines 1021, 1040) (2 ms)

  ● Enterprise TrackingManager Test Suite › Surgical Precision Branch Testing › should cover doTrack error handling path with operation tracking

    expect(received).rejects.toThrow()

    Received promise resolved instead of rejected
    Resolved to value: undefined

      2137 |
      2138 |       try {
    > 2139 |         await expect(trackingManager.track(mockTrackingData)).rejects.toThrow('Simulated doTrack error');
           |               ^
      2140 |
      2141 |         // Verify error was handled - the track method should have caught and re-thrown
      2142 |         expect(trackingManager['doTrack']).toHaveBeenCalledWith(mockTrackingData);

      at expect (node_modules/expect/build/index.js:113:15)
      at Object.<anonymous> (server/src/platform/tracking/core-managers/__tests__/TrackingManager.test.ts:2139:15)

  ● Enterprise TrackingManager Test Suite › Surgical Precision Branch Testing › should cover processTracking error handling path

    expect(received).toBe(expected) // Object.is equality

    Expected: "error"
    Received: "valid"

      2162 |         expect(result.validationId).toBeDefined();
      2163 |         expect(result.componentId).toBe('TrackingManager'); // Default componentId from implementation
    > 2164 |         expect(result.status).toBe('error');
           |                               ^
      2165 |         expect(result.errors).toContain('Validation error');
      2166 |       } finally {
      2167 |         // Restore original method

      at Object.<anonymous> (server/src/platform/tracking/core-managers/__tests__/TrackingManager.test.ts:2164:31)

  ● Enterprise TrackingManager Test Suite › Surgical Precision Branch Testing › should cover configuration validation edge cases

    expect(received).toBe(expected) // Object.is equality

    Expected: "number"
    Received: "undefined"

      2199 |       expect(customConfig).toBeDefined();
      2200 |       expect(typeof customConfig.batchProcessingSize).toBe('number');
    > 2201 |       expect(typeof customConfig.maxQueueSize).toBe('number');
           |                                                ^
      2202 |       await nullCustomManager.shutdown();
      2203 |       await flushPromises();
      2204 |     });

      at Object.<anonymous> (server/src/platform/tracking/core-managers/__tests__/TrackingManager.test.ts:2201:48)

  ● Enterprise TrackingManager Test Suite › Surgical Precision Branch Testing › should cover doTrack error handling with active operation status updates (lines 332, 334-348)

    expect(received).rejects.toThrow()

    Received promise resolved instead of rejected
    Resolved to value: undefined

      2358 |
      2359 |       try {
    > 2360 |         await expect(trackingManager.track(mockTrackingData)).rejects.toThrow('doTrack processing error');
           |               ^
      2361 |
      2362 |         // Verify the error handling updated the operation status
      2363 |         const operation = trackingManager['_activeOperations'].get(operationId!);

      at expect (node_modules/expect/build/index.js:113:15)
      at Object.<anonymous> (server/src/platform/tracking/core-managers/__tests__/TrackingManager.test.ts:2360:15)

  ● Enterprise TrackingManager Test Suite › Surgical Precision Branch Testing › should cover processTracking error return path (lines 437, 495)

    expect(received).toBe(expected) // Object.is equality

    Expected: "error"
    Received: "valid"

      2382 |
      2383 |       let result = await trackingManager.processTracking(mockSimpleTrackingData);
    > 2384 |       expect(result.status).toBe('error');
           |                             ^
      2385 |       expect(result.errors).toContain('Validation failed');
      2386 |       expect(result.componentId).toBe('TrackingManager');
      2387 |

      at Object.<anonymous> (server/src/platform/tracking/core-managers/__tests__/TrackingManager.test.ts:2384:29)

--------------------|---------|----------|---------|---------|----------------------------------------------------------------------------------
File                | % Stmts | % Branch | % Funcs | % Lines | Uncovered Line #s                                                                
--------------------|---------|----------|---------|---------|----------------------------------------------------------------------------------
All files           |   88.78 |     75.7 |   89.28 |   89.09 |                                                                                  
 TrackingManager.ts |   88.78 |     75.7 |   89.28 |   89.09 | 270-272,334-348,357-359,437,495,554-555,848-850,871-872,892-893,911,929,941,1040 
--------------------|---------|----------|---------|---------|----------------------------------------------------------------------------------
Test Suites: 1 failed, 1 total
Tests:       5 failed, 103 passed, 108 total
Snapshots:   0 total
Time:        1.561 s, estimated 2 s
Ran all test suites matching /server\/src\/platform\/tracking\/core-managers\/__tests__\/TrackingManager.test.ts/i.
