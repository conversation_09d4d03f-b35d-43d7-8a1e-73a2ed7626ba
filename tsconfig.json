{
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "lib": ["ES2020"],
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "downlevelIteration": true,
    "importHelpers": true,
    "allowSyntheticDefaultImports": true,
    "outDir": "./dist",                    // 🎯 KEY FIX
    "rootDir": "./",                       // 🎯 KEY FIX
    "declaration": true,                   // 🎯 OPTIONAL: Generate .d.ts files
    "sourceMap": true,                     // 🎯 OPTIONAL: Generate source maps
    "baseUrl": ".",
    "paths": {
      "@shared/*": ["shared/src/*"],
      "@server/*": ["server/src/*"],
      "@client/*": ["client/src/*"]
    }
  },
  "include": [
    "server/**/*",
    "shared/**/*",
    "client/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "**/*.test.ts",                        // 🎯 OPTIONAL: Exclude tests from build
    "**/*.spec.ts"
  ]
}