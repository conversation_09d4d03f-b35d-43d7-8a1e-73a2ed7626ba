# OA Framework – Base Infrastructure Coverage Improvement Plan

Date: 2025-08-23
Owner: QA/Platform Engineering
Status: Approved for execution

## Executive Summary
- Current coverage (shared/src/base overall):
  - Statements: 72.30%
  - Branches: 61.92%
  - Functions: 71.70%
  - Lines: 72.86%
- Target: ≥ 90% across Statements, Branches, Functions, and Lines for critical infrastructure; ≥ 90% for enhanced services over subsequent iterations.
- Strategy: Apply surgical precision testing, environment gating, error-type branching, configuration toggles, and fake timers to raise coverage rapidly with production value, without reducing functionality (Anti‑Simplification compliant).

## Prioritization Model
1) Infrastructure criticality (foundation components first)
2) Severity of coverage gaps (0% and low metrics first)
3) Framework dependency impact (used across modules)
4) Complexity vs. coverage gain ratio (quick wins in parallel)

---

## Tier 1 – Critical Infrastructure Priority
Focus: Framework stability; immediate attention.

### 1. timer-coordination/TimerCoordinationService.ts
- Coverage: S 74.37 | B 71.73 | F 67.74 | L 74.68
- Uncovered lines: …326, 359, 368, 379-383, 407-411, 477, 509, 517, 522-530, 638-664
- Gap to 90: S +15.63 | B +18.27 | F +22.26 | L +15.32
- Recommended approach:
  - Fake timers to cover schedule/reschedule/cancel/backpressure and shutdown cleanup branches.
  - Force pool exhaustion and backoff paths.
  - Error instanceof Error true/false paths during scheduling failures.
  - Environment gating to simulate production/test paths if applicable.
- Effort: High
- Business value: Ensures timing backbone reliability under load and failure scenarios.

### 2. event-handler-registry/EventHandlerRegistry.ts
- Coverage: S 88.96 | B 94.28 | F 86.66 | L 88.81
- Uncovered lines: 206, 213, 469-490
- Gap to 90: S +1.04 | F +3.34 | L +1.19
- Recommended approach:
  - Trigger duplicate registrations, client limits, and stale-handler pruning (lastUsed time checks).
  - Exercise overflow cleanup and boundary counts.
  - Verify metrics updates and defensive branches.
- Effort: Low
- Business value: Guarantees consistent event delivery and registry health.

### 3. memory-safety-manager/modules/SystemCoordinationManager.ts
- Coverage: S 97.26 | B 87.69 | F 91.66 | L 97.24
- Uncovered lines: 606-611
- Gap to 90: B +2.31
- Recommended approach:
  - Flip coordination states to cover else‑path; inject one recoverable error to hit fallback branch.
- Effort: Low
- Business value: Validates system coordination during degraded states.

### 4. MemorySafeResourceManager.ts
- Coverage: S 98.96 | B 84.72 | F 98.11 | L 100
- Uncovered lines: 222, 291, 447, 490, 550, 586, 763, 884
- Gap to 90: B +5.28
- Recommended approach:
  - Force capacity=0 and empty history to hit fallback metrics paths.
  - Trigger Error and non‑Error in factory/cleanup for instanceof branches.
  - Use throwing getter for _limits to exercise defensive reads.
- Effort: Medium
- Business value: Core memory‑safe guarantees across the platform.

---

## Tier 2 – Enhanced Services Priority
Focus: Significant gaps with broad usage; raise to ≥ 90% in staged passes.

### 5. timer-coordination/TimerCoordinationServiceEnhanced.ts
- Coverage: S 59.43 | B 63.63 | F 65.90 | L 59.43
- Uncovered lines: 216-291, 433-439, 447-448, 457-461, 470-478, 491, 505-524
- Gap to 90: S +30.57 | B +26.37 | F +24.10 | L +30.57
- Approach: Exercise enhanced phased execution, circuit breakers, and timing metrics. Mock ResilientTimer and metrics; cover both success/failure and cooldown branches.
- Effort: High
- Business value: Enterprise‑grade timing orchestration.

### 6. event-handler-registry/EventHandlerRegistryEnhanced.ts
- Coverage: S 68.68 | B 60.76 | F 70.90 | L 69.85
- Uncovered: …678, 692-693, 723, 756, 805-840, 868-871, 893-897, 902, 942-949
- Gap to 90: S +21.32 | B +29.24 | F +19.10 | L +20.15
- Approach: Cover middleware chains, dedup gates, buffer thresholds; test priority conflicts and metadata branches.
- Effort: High
- Business value: Scalable, noise‑resistant event processing.

### 7. memory-safety-manager/MemorySafetyManager.ts
- Coverage: S 59.05 | B 64.77 | F 73.80 | L 62.60
- Uncovered: …712-722, 734-740, 748, 759, 772, 784, 810, 820, 830, 834, 844, 854
- Gap to 90: S +30.95 | B +25.23 | F +16.20 | L +27.40
- Approach: Drive lifecycle paths, memory pressure triggers, and emergency cleanup; cover warnings vs. errors.
- Effort: High
- Business value: Platform‑wide memory safety enforcement.

### 8. cleanup-coordinator-enhanced/modules/CleanupConfiguration.ts
- Coverage: S 23.23 | B 1.49 | F 27.77 | L 23.15
- Uncovered: 209-284, 298-320, 345-357, 362, 378, 386-436
- Gap to 90: S +66.77 | B +88.51 | F +62.23 | L +66.85
- Approach: Validate all config dimensions; invalid ranges, disabled flags, early returns; test instanceof Error branches and throwing getters on config reads.
- Effort: High
- Business value: Safe, predictable cleanup operations.

### 9. cleanup-coordinator-enhanced/modules/OperationExecutionManager.ts
- Coverage: S 55.20 | B 44.64 | F 51.85 | L 56.54
- Uncovered: …317, 322-324, 343-419, 429, 563, 593, 616, 625, 665-694, 723-790
- Gap to 90: S +34.80 | B +45.36 | F +38.15 | L +33.46
- Approach: Execute batches with intermittent failures; test backoff, retries, partial rollbacks; emit both Error and string failures.
- Effort: High
- Business value: Reliable execution under failure.

### 10. cleanup-coordinator-enhanced/modules/RollbackManager.ts
- Coverage: S 56.50 | B 33.73 | F 50.00 | L 58.33
- Uncovered: …623, 641-654, 698-700, 706-708, 714-716, 721, 731-781, 797-828
- Gap to 90: S +33.50 | B +56.27 | F +40.00 | L +31.67
- Approach: Simulate multi‑step failures; verify short‑circuit and compensation strategies across branches.
- Effort: High
- Business value: Safe rollback guarantees.

### 11. cleanup-coordinator-enhanced/modules/RollbackSnapshots.ts
- Coverage: S 41.89 | B 9.37 | F 22.22 | L 43.66
- Uncovered: 129-131, 202-204, 218-274, 281-292
- Gap to 90: S +48.11 | B +80.63 | F +67.78 | L +46.34
- Approach: Create/restore snapshots including corrupted or missing files; cover error fallbacks and cleanup branches.
- Effort: High
- Business value: Data integrity during cleanup.

### 12. cleanup-coordinator-enhanced/modules/RollbackUtilities.ts
- Coverage: S 38.83 | B 8.57 | F 17.39 | L 42.69
- Uncovered: …198, 213-217, 229-232, 239, 247-272, 280-292, 303-307, 314-318
- Gap to 90: S +51.17 | B +81.43 | F +72.61 | L +47.31
- Approach: Cover idempotency checks, selection logic, and error aggregation with Error/non‑Error inputs.
- Effort: High
- Business value: Deterministic rollback behavior.

### 13. cleanup-coordinator-enhanced/modules/TemplateValidation.ts
- Coverage: S 68.43 | B 43.24 | F 57.44 | L 68.61
- Uncovered: …801, 812, 821-834, 843, 867, 890-895, 912-913, 923-950, 963-964
- Gap to 90: S +21.57 | B +46.76 | F +32.56 | L +21.39
- Approach: Validate templates with missing sections, invalid types, oversize; cover warning vs error paths.
- Effort: High
- Business value: Template quality enforcement.

### 14. cleanup-coordinator-enhanced/modules/TemplateWorkflows.ts
- Coverage: S 69.95 | B 44.44 | F 76.92 | L 68.30
- Uncovered: …662-689, 768-769, 794-812, 894-906, 947, 990-1027, 1056-1057
- Gap to 90: S +20.05 | B +45.56 | F +13.08 | L +21.70
- Approach: Execute full lifecycle with optional step skips and retries; assert side effects and compensations.
- Effort: High
- Business value: End‑to‑end cleanup orchestration confidence.

### 15. cleanup-coordinator-enhanced/modules/TemplateDependencies.ts
- Coverage: S 59.36 | B 27.27 | F 52.94 | L 59.36
- Uncovered: …381, 406-478, 515-538, 568, 580-582, 604-605, 611, 617, 638-640
- Gap to 90: S +30.64 | B +62.73 | F +37.06 | L +30.64
- Approach: Build graphs including cycles/missing deps; assert error handling and skip branches.
- Effort: High
- Business value: Deterministic dependency resolution.

### 16. cleanup-coordinator-enhanced/modules/InitializationManager.ts
- Coverage: S 79.59 | B 57.14 | F 100 | L 79.59
- Uncovered: 164-172, 202-204, 222-224, 282-283
- Gap to 90: S +10.41 | B +32.86 | L +10.41
- Approach: Toggle init flags for early returns; simulate partial init failures and retries.
- Effort: Medium
- Business value: Reliable startup under partial failures.

### 17. cleanup-coordinator-enhanced/modules/HealthStatusManager.ts
- Coverage: S 63.79 | B 47.61 | F 87.50 | L 63.79
- Uncovered: 237, 248-269, 309, 334-349, 365-366, 382-383
- Gap to 90: S +26.21 | B +42.39 | L +26.21
- Approach: Simulate health degradation and recovery; exercise thresholds and stale metrics paths.
- Effort: Medium
- Business value: Operational visibility and safeguards.

### 18–21. 0% Enhanced Modules
- atomic-circular-buffer-enhanced/performance-validation.ts (0% all) – Effort: Medium
- cleanup-coordinator-enhanced/modules/CleanupUtilities.ts (0% all) – Effort: Medium
- cleanup-coordinator-enhanced/modules/UtilityAnalysis.ts (0% all) – Effort: Medium
- cleanup-coordinator-enhanced/modules/UtilityExecution.ts (0% all) – Effort: Medium
- cleanup-coordinator-enhanced/modules/UtilityPerformance.ts (0% all) – Effort: Medium/High
- cleanup-coordinator-enhanced/modules/UtilityValidation.ts (0% all) – Effort: Medium/High
- Approach: Add smoke tests per exported unit; cover happy-path and defensive branches; use throwing getters, invalid inputs, and configuration gates.
- Business value: Completes coverage for enhanced cleanup/perf subsystems.

---

## Tier 3 – Utilities and Supporting Modules

### utils/EnterpriseErrorHandling.ts
- Coverage: S 95.63 | B 81.81 | F 100 | L 95.58
- Uncovered: 270-272, 608-611, 708, 799, 883
- Gap to 90: B +8.19
- Approach: Inject non‑Error throws, JSON.stringify failures (first call), nested cause chains; assert fallback messages.
- Effort: Medium

### utils/JestCompatibilityUtils.ts
- Coverage: S 86.00 | B 52.94 | F 80.00 | L 89.13
- Uncovered: 184, 202, 239, 260-261
- Gap to 90: S +4.00 | B +37.06 | F +10.00 | L +0.87
- Approach: Cover environment detection (Jest vs Node), timer mocking guards, and edge toggles.
- Effort: Medium

### utils/ResilientTiming.ts
- Coverage: S 86.25 | B 87.23 | F 100 | L 86.25
- Uncovered: 153-155, 174-176, 201-214
- Gap to 90: S +3.75 | B +2.77 | L +3.75
- Approach: Start/stop with DEBUG flags, jitter bounds, timeout/cancel races; fake timers.
- Effort: Low

### event-handler-registry/modules/MiddlewareManager.ts
- Coverage: S 97.08 | B 81.57 | F 100 | L 97.95
- Uncovered: 359-360
- Gap to 90: B +8.43
- Approach: Force middleware to short‑circuit vs continue; ensure both branches covered.
- Effort: Low

### event-handler-registry/modules/EventBuffering.ts
- Coverage: S 100 | B 85.71 | F 100 | L 100
- Uncovered: 87-99, 102, 295
- Gap to 90: B +4.29
- Approach: Create buffer overflow/underflow and dedup toggles; hit drop vs enqueue paths.
- Effort: Low

### base/__tests__/JestTestingUtils.ts (test utility)
- Coverage: S 61.53 | B 54.76 | F 88.88 | L 65.30
- Uncovered: 15-17, 49-52, 72-85, 114, 130-135
- Gap to 90: S +28.47 | B +35.24 | F +1.12 | L +24.70
- Approach: Add tests for the utility functions themselves (mock setup/teardown guards, timer wrappers, env toggles) or explicitly exclude test‑only support files from coverage.
- Effort: Low/Policy

### types/CleanupTypes.ts (pure types)
- Coverage: S 0 | B 100 | F 0 | L 0
- Uncovered: 76-88
- Gap to 90: S +90 | F +90 | L +90
- Approach: As a TypeScript type‑only module, prefer excluding from coverage or introduce minimal runtime helpers that consume these types with business value.
- Effort: Policy

---

## Quick Wins (Parallel Workstream)
- EventHandlerRegistry.ts: +3.34% Functions, +1.04% Statements, +1.19% Lines
- MiddlewareManager.ts: +8.43% Branches
- EventBuffering.ts: +4.29% Branches
- ResilientTiming.ts: +3.75% Statements/Lines, +2.77% Branches
- SystemCoordinationManager.ts: +2.31% Branches
- MemorySafeResourceManager.ts: +5.28% Branches

These can be completed in parallel within 1–2 days to raise the baseline while Tier 1 High‑effort work proceeds.

---

## Implementation Strategy
- Surgical precision testing techniques:
  - Map uncovered lines to exact conditions and side‑effects; craft minimal deterministic scenarios per branch.
  - Use error instanceof Error true/false branches by throwing Error vs string/object.
  - Configuration toggles to drive early returns and feature gates.
  - Environment gating with NODE_ENV and JEST_WORKER_ID to flip production/test code paths.
  - Fake timers (jest.useFakeTimers()) and spies on createSafeInterval to avoid real timers.
  - Throwing property getters (Object.defineProperty) to simulate config read failures.
  - Boundary forcing (capacity=0, empty histories) to trigger fallback calculations.
- Documentation and governance:
  - Record techniques and line targets per component in docs/lessons/.
  - Update docs/test-plan.md with ✅ entries and timestamps after each milestone.

---

## Timeline & Success Metrics
- Tier 1 (Critical Infrastructure): 3–5 working days
  - Success: Each file ≥ 90% across S/B/F/L; all tests passing; no timing leaks; performance stable.
- Quick Wins: 1–2 working days in parallel
  - Success: All quick‑win files at ≥ 90% in weakest metric.
- Tier 2 (Enhanced Services): 1–2 weeks (staged by domain)
  - Success: Raise each enhanced module to ≥ 90%; maintain runtime performance.
- Tier 3 (Utilities/Modules): 3–4 days
  - Success: Utilities ≥ 90% (or policy‑driven exclusions for types/test‑only files).

Validation checkpoints:
- Per‑component PRs with coverage gates targeting source files via --collectCoverageFrom
- Jest runs with --verbose, fake timers where applicable
- CI gate: no regressions; coverage diff reports; stability on repeated runs

---

## Notes & Decisions Required
- Confirm policy on pure type files (e.g., types/CleanupTypes.ts) and test‑only utilities for coverage calculation.


## Scoped Base-Only Runs (avoid server tests)

- Rationale: When improving coverage for shared/src/base, restrict Jest discovery to the shared base tests to avoid running server-side suites (e.g., GovernanceTrackingSystem.security.test.ts).

- Preferred commands
  - Base-only full suite (path filter):
    - npx jest shared/src/base/__tests__/ --verbose --coverage --collectCoverageFrom="shared/src/base/**/*.ts" --testTimeout=180000
  - Base-only using testPathPattern (regex filter):
    - npx jest -- --testPathPattern="^shared/src/base/__tests__/" --verbose --coverage --collectCoverageFrom="shared/src/base/**/*.ts" --testTimeout=180000

- Component-targeted examples
  - EventHandlerRegistry:
    - npx jest -- --testPathPattern="shared/src/base/__tests__/EventHandlerRegistry\.(branches|functions|additional-coverage)\.test\.ts" --coverage --collectCoverageFrom="shared/src/base/EventHandlerRegistry.ts"
  - TimerCoordinationService:
    - npx jest -- --testPathPattern="shared/src/base/__tests__/TimerCoordinationService\.(branches|functions|more-functions|additional-coverage)\.test\.ts" --coverage --collectCoverageFrom="shared/src/base/TimerCoordinationService.ts"
  - MemorySafeResourceManager:
    - npx jest -- --testPathPattern="shared/src/base/__tests__/MemorySafeResourceManager\.(branches|more-branches|additional-branches)\.test\.ts" --coverage --collectCoverageFrom="shared/src/base/MemorySafeResourceManager.ts"
  - SystemCoordinationManager (production-path isolation example):
    - npx jest shared/src/base/__tests__/SystemCoordinationManager.production-path.test.ts --collectCoverageFrom="shared/src/base/memory-safety-manager/modules/SystemCoordinationManager.ts"

- Policy note: collectCoverageFrom controls which SOURCE files are measured, but it does not limit which TESTS run. Always pass a shared path or testPathPattern to scope execution to base-only tests during these coverage tasks.

## Appendices
- Source command for baseline run:
  - npm test -- shared/src/base/__tests__/ --verbose --coverage --collectCoverageFrom="shared/src/base/**/*.ts" --testTimeout=180000
- Reference: Detailed file metrics were taken from the latest coverage table output after the above command.

