# Coverage Workflow - Step by Step Process

## FUNCTION COVERAGE WORKFLOW - Re-Export Modules

### PHASE 1: Function Coverage Diagnosis (10 minutes)
```bash
# 1. Run coverage to identify function coverage gaps
npm test -- --coverage

# 2. Identify re-export module patterns
echo "Analyzing function coverage..."
echo "Function Coverage <50%: Re-export coordination module"
echo "High statements/branches/lines but low functions: TypeScript re-export issue"

# 3. Generate LCOV data for detailed function analysis
npx jest --coverage --coverageReporters=text-lcov | grep -A 20 -B 5 "FN:"
```

### PHASE 2: Jest Function Detection Analysis (15 minutes)
**AI Prompt:**
"Analyze LCOV function data for [MODULE_NAME]:
- FNF (Functions Found): [TOTAL_FUNCTIONS]
- FNH (Functions Hit): [TESTED_FUNCTIONS]
- Coverage: [PERCENTAGE]%

Identify function types:
1. Native functions (actual class methods)
2. Individual re-export getters (export { functionName })
3. Collection re-export getters (export { UtilityCollection })

Provide breakdown and testing strategy."

### PHASE 3: Direct Re-Export Testing Implementation (60-90 minutes)
**AI Prompt:**
"Use Direct Re-Export Function Testing pattern from ./docs/lessons/testing-patterns/jest-mocking-patterns.md

For module [MODULE_NAME] with [X] untested re-export functions:
1. Create tests for individual function re-exports using const { functionName } = require()
2. Create tests for collection re-exports using const { CollectionName } = require()
3. Ensure proper mock objects match TypeScript interfaces

Provide complete test implementation."

### PHASE 4: Function Coverage Verification (10 minutes)
```bash
# Run enhanced test to verify function coverage improvement
npm test -- --testPathPattern="[MODULE_NAME].test.ts" --coverage

# Check function coverage improvement
echo "Verify function coverage improved from [BEFORE]% to [TARGET]%"
echo "Expected: 95%+ function coverage for re-export modules"
```

### PHASE 5: Documentation (5 minutes)
Record result in ./docs/lessons/feedback/pattern-effectiveness.md with:
- Function coverage improvement percentage
- Number of new tests added
- Implementation time
- Pattern effectiveness validation

## TOTAL TIME TARGET: 2-4 hours for comprehensive function coverage enhancement

---

## LINE COVERAGE WORKFLOW - Traditional Catch Blocks

### PHASE 1: Diagnosis (5 minutes)
```bash
# 1. Run coverage to identify gaps
npm test -- --coverage

# 2. Identify uncovered line patterns
echo "Analyzing uncovered lines..."
echo "Lines 200-230: Constructor catch blocks"
echo "Lines 290-300: Setup catch blocks"  
echo "Lines 550-600: Processing catch blocks"
echo "Lines 900+: Runtime catch blocks"
```

## PHASE 2: Pattern Selection (2 minutes)
**AI Prompt:** 
"Uncovered lines: [PASTE_LINE_NUMBERS]
Match to pattern from ./docs/lessons/testing-patterns/jest-mocking-patterns.md
Provide pattern name and template to use."

## PHASE 3: Implementation (10-15 minutes)
**AI Prompt:**
"Use ./docs/lessons/templates/catch-block-coverage.template.ts
Replace placeholders for:
- Target lines: [LINE_NUMBERS]
- Target method: [METHOD_NAME]  
- Dependency path: [DEPENDENCY_PATH]
Provide complete working test code."

## PHASE 4: Verification (5 minutes)
```bash
# Run single test to verify
npm test -- --testNamePattern="TARGET_TEST_NAME" --coverage

# Check specific lines are now covered
echo "Verify lines [TARGET_LINES] are now covered"
```

## PHASE 5: Documentation (2 minutes)
Record result in ./docs/lessons/feedback/pattern-effectiveness.md

## TOTAL TIME TARGET: 15-30 minutes (not hours)

---

## BRANCH COVERAGE WORKFLOW - After Line Coverage Success

## PHASE 6: Branch Coverage Analysis (3 minutes)
```bash
# 1. Verify line coverage is 100%
npm test -- --coverage | grep "Lines"

# 2. Check branch coverage percentage
npm test -- --coverage | grep "Branch"

# 3. Identify branch coverage gap
echo "Target: Increase branch coverage from X% to 100%"
```

## PHASE 7: Branch Pattern Analysis (2 minutes)
**AI Prompt:**
"Line coverage: 100% ✅
Branch coverage: [X%] ❌
Uncovered lines showing: [LINE_NUMBERS]
Apply dual path branch coverage patterns from ./docs/lessons/testing-patterns/jest-mocking-patterns.md
Focus on testing both SUCCESS and FAILURE branches for each line."

## PHASE 8: Dual Path Implementation (10-15 minutes)
**AI Prompt:**
"Create branch coverage tests using ./docs/lessons/templates/catch-block-coverage.template.ts
For lines [LINE_NUMBERS]:
- If failure test exists, add success test
- If success test exists, add failure test
- Test all conditional combinations
Provide complete dual path test code."

## PHASE 9: Branch Verification (3 minutes)
```bash
# Run tests and verify branch coverage increases
npm test -- --coverage

# Check all coverage dimensions
echo "Statement: 100% ✅"
echo "Branch: 100% ✅"
echo "Function: 100% ✅"
echo "Line: 100% ✅"
```

## PHASE 10: Complete Coverage Documentation (2 minutes)
Record final success in ./docs/lessons/feedback/pattern-effectiveness.md

## TOTAL TIME TARGET: 20-25 minutes additional (after line coverage success)
## FINAL GOAL: 100% coverage across all dimensions
