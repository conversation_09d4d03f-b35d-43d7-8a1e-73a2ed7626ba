# 🎯 100% Coverage Quick Reference Guide

## 🚀 **The Breakthrough Method** (Proven on BufferPersistenceManager.ts)

### **Step 1: Identify the Problem**
```bash
npm test -- --coverage
# Look for: 99%+ coverage with specific uncovered lines
```

### **Step 2: Apply the "Spy, Don't Replace" Fix**
```typescript
// ❌ WRONG (kills coverage)
(service as any).method = jest.fn();

// ✅ RIGHT (maintains coverage)
jest.spyOn(service as any, 'method')
  .mockImplementation((...args) => 'result');
```

### **Step 3: Use the Initialize-Then-Spy Pattern**
```typescript
beforeEach(async () => {
  service = new ServiceClass();
  await service.doInitialize();     // Initialize first
  jest.spyOn(service, 'method');    // Spy after init
});
```

### **Step 4: Target Edge Cases**
```typescript
// Cover fallback logic: value || defaultValue
it('should cover fallback logic', () => {
  const emptyMap = new Map();
  service.processWithFallback(emptyMap); // Triggers || fallback
});
```

---

## 🎯 **Quick Decision Matrix**

| Situation | Solution | Pattern |
|-----------|----------|---------|
| **99%+ line coverage stuck** | Use `jest.spyOn()` | Spy, don't replace |
| **Inherited method calls** | Spy after initialization | Initialize-then-spy |
| **Fallback logic uncovered** | Test with empty/undefined | Edge case testing |
| **Branch coverage <100%** | Test both true/false paths | Dual-path testing |

---

## 🏆 **Success Checklist**

- [ ] All metrics show 100%
- [ ] No uncovered line numbers
- [ ] All tests passing
- [ ] Used spies for inherited methods
- [ ] Tested edge cases and fallbacks

---

## 🧠 **Memory Devices**

**"Spy to See, Mock to Block"**
- **Spy**: When you need coverage (original code must execute)
- **Mock**: When you need isolation (original code can be bypassed)

**"Initialize, Spy, Test"**
- Always initialize the service before setting up spies

---

## 📊 **Proven Results**

**BufferPersistenceManager.ts Achievement:**
- 99.1% → 100% statements
- 91.66% → 100% branches  
- 96% → 100% functions
- 99.07% → 100% lines
- Duration: ~2 hours systematic work

---

## 🎓 **Key Insight**

**Coverage tools track execution, not intention.**

You must let the original code execute (via spying) while maintaining test control. Complete method replacement breaks coverage tracking.

---

## 📚 **Full Documentation**

See: `docs/lessons/testing-patterns/coverage-breakthrough-methodology.md`
