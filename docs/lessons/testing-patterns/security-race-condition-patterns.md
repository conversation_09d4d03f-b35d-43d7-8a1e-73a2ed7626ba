# Security Test Race Condition Patterns - Quick Reference

**Context**: OA Framework Security Testing  
**Focus**: Race Condition Prevention in Security Tests  
**Related**: `lesson-25-governance-security-race-condition-resolution.md`

---

## 🚨 Race Condition Identification Patterns

### Common Symptoms
```bash
# Test failure pattern
expect(received).toBeGreaterThan(expected)
Expected: > 0
Received:   0

# Indicates: Counters not being incremented due to race conditions
```

### Debug Investigation Pattern
```typescript
// ✅ PATTERN: Add debug logging to identify race conditions
async enforceRateLimit(source: string): Promise<boolean> {
  console.log(`[DEBUG] Source: ${source}, Count: ${currentCount}, Time: ${now}`);
  
  // If no debug output appears, method isn't being called
  // If output shows inconsistent counts, race condition exists
}
```

---

## 🔧 Sequential Processing Patterns

### Rate Limiting Test Pattern
```typescript
// ❌ AVOID: Parallel requests cause race conditions
const promises = Array.from({ length: 150 }, async (_, i) => {
  await system.logEvent(/* ... */);
});
await Promise.allSettled(promises);

// ✅ USE: Sequential processing for counter-based operations
for (let i = 0; i < testRequests; i++) {
  try {
    await system.logEvent(/* ... */);
    successfulRequests++;
  } catch (error) {
    if (error.message.includes('Rate limit')) {
      blockedRequests++;
    }
    // Early termination for efficiency
    if (blockedRequests >= 10) break;
  }
}
```

### Security Monitor Integration Pattern
```typescript
// ✅ PATTERN: Proper security monitor setup
beforeEach(async () => {
  // 1. Set environment for security profile
  process.env.TEST_TYPE = 'security';
  
  // 2. Create system
  system = new SecuritySystem(config);
  
  // 3. Set monitor AFTER construction
  (system as any).setSecurityMonitor(monitor);
  
  // 4. Initialize
  await system.initialize();
});
```

---

## 🎯 Decision Matrix: Sequential vs Parallel

| Test Type | Processing | Reason |
|-----------|------------|---------|
| Rate Limiting | Sequential | Counter consistency required |
| Flood Protection | Sequential | Threshold tracking needed |
| Resource Exhaustion | Sequential | Resource state management |
| Input Validation | Parallel | Independent operations |
| Encryption Tests | Parallel | No shared state |
| Performance Tests | Parallel | Throughput measurement |

---

## 🛡️ Security Layer Integration Patterns

### Monitor Bridge Pattern
```typescript
// ✅ PATTERN: Security layer monitor integration
class SecurityEnforcementLayer {
  setMonitor(monitor: SecurityTestMonitor): void {
    this.monitor = monitor;
  }
}

class GovernanceSystem {
  setSecurityMonitor(monitor: LegacySecurityTestMonitor): void {
    this._securityMonitor = monitor;
    if (this._securityLayer && 'setMonitor' in this._securityLayer) {
      (this._securityLayer as any).setMonitor(
        this.createSecurityMonitorBridge(monitor)
      );
    }
  }
}
```

### Security Profile Selection Pattern
```typescript
// ✅ PATTERN: Environment-aware security layer creation
private createEnvironmentAwareSecurityLayer(): ISecurityEnforcement {
  const testType = process.env.TEST_TYPE;
  
  // Use no-op for performance/unit tests
  if (process.env.NODE_ENV === 'test' && 
      (testType === 'performance' || testType === 'unit')) {
    return new NoOpSecurityLayer();
  }
  
  // Use full security layer for security tests
  const securityConfig = getSecurityConfig(testType);
  return new SecurityEnforcementLayer(securityConfig, monitor);
}
```

---

## ⚡ Performance Optimization Patterns

### Early Termination Pattern
```typescript
// ✅ PATTERN: Stop after sufficient evidence
for (let i = 0; i < testRequests; i++) {
  try {
    await system.operation();
    successCount++;
  } catch (error) {
    if (isExpectedError(error)) {
      errorCount++;
    }
    // Stop after proving the mechanism works
    if (errorCount >= 10) {
      console.log(`Stopping after ${errorCount} expected errors`);
      break;
    }
  }
}
```

### Resource Cleanup Pattern
```typescript
// ✅ PATTERN: Proper test cleanup
afterEach(async () => {
  if (system) {
    try {
      await system.shutdown();
    } catch (error) {
      console.warn('Shutdown error:', error);
    }
    system = null as any;
  }
  
  // Force garbage collection
  if (global.gc) {
    global.gc();
  }
});
```

---

## 🔍 Debugging Patterns

### Race Condition Detection
```typescript
// ✅ PATTERN: Add timing and state logging
async rateLimit(source: string): Promise<boolean> {
  const startTime = Date.now();
  const currentCount = this.getCount(source);
  
  console.log(`[RACE DEBUG] ${source}: count=${currentCount}, time=${startTime}`);
  
  // ... rate limiting logic
  
  const endTime = Date.now();
  console.log(`[RACE DEBUG] ${source}: completed in ${endTime - startTime}ms`);
}
```

### State Consistency Validation
```typescript
// ✅ PATTERN: Validate state consistency
it('should maintain counter consistency', async () => {
  const initialCount = getCounterValue();
  
  // Perform operations
  for (let i = 0; i < 100; i++) {
    await performOperation();
  }
  
  const finalCount = getCounterValue();
  const expectedCount = initialCount + expectedIncrement;
  
  expect(finalCount).toBe(expectedCount);
});
```

---

## 🎯 Anti-Patterns to Avoid

### ❌ Parallel Counter Operations
```typescript
// DON'T: Parallel operations on shared counters
const promises = operations.map(async (op) => {
  counter++; // Race condition!
  await performOperation(op);
});
```

### ❌ Immediate Monitor Setting
```typescript
// DON'T: Set monitor before security layer creation
const monitor = new SecurityMonitor();
const system = new SecuritySystem(config, monitor); // Too early!
```

### ❌ Missing Early Termination
```typescript
// DON'T: Process all requests when few are sufficient
for (let i = 0; i < 1000; i++) {
  // This will take too long and provide no additional value
  await testOperation();
}
```

---

## 📚 Related Patterns

- **Memory-Safe Patterns**: `lesson-learned-05-MemorySafeResourceManager.md`
- **Async Testing**: `lesson-01-GovernanceTrackingSystem-Integration.md`
- **Surgical Precision**: `perfect-coverage-methodology.md`
- **Timing Integration**: `resilient-timing-surgical-precision-success.md`

---

**Quick Decision Guide**:
1. **Shared State?** → Use Sequential
2. **Counter Operations?** → Use Sequential  
3. **Independent Operations?** → Use Parallel
4. **Race Condition Symptoms?** → Add Debug Logging
5. **Security Tests?** → Set TEST_TYPE='security'
