# Coverage Diagnostic Checklist

## STEP 1: Run Coverage Analysis
```bash
npm test -- --coverage
```

## STEP 2: Identify Line Range Patterns

**Common Patterns:**
- **200-230 range:** Constructor/Initialization catch blocks → Use jest.doMock constructor pattern
- **290-300 range:** Setup/Reconfiguration catch blocks → Use call-count pattern  
- **550-600 range:** Processing/Validation catch blocks → Use processing failure pattern
- **900+ range:** Runtime condition catch blocks → Use natural error conditions

## STEP 3: Pattern Matching Rules

### Constructor Catch Blocks (209-228, 295)
```typescript
// Pattern: jest.doMock + dynamic import
jest.doMock('dependency', () => ({ Class: mockFailingConstructor }));
jest.resetModules();
const { TargetClass } = await import('target-module');
```

### Processing Catch Blocks (551)  
```typescript
// Pattern: Mock processing dependencies
jest.doMock('./processing-module', () => ({ 
  ProcessorClass: mockFailingProcessor 
}));
```

### Runtime Catch Blocks (933-940)
```typescript
// Pattern: Create conditions that trigger runtime errors
const invalidContext = { targetComponents: null };
expect(() => evaluateCondition(condition, invalidContext)).toThrow();
```

## STEP 4: Implementation Verification
- [ ] Run coverage after each test
- [ ] Verify specific lines are now covered
- [ ] Ensure no regression in existing coverage
- [ ] Time spent: Should be 15-30 minutes, not hours

---

# Branch Coverage Diagnostic Checklist

## STEP 5: Branch Coverage Analysis
```bash
npm test -- --coverage --verbose
```

Look for coverage report showing:
- **100% Line Coverage** ✅
- **< 100% Branch Coverage** ❌ (e.g., 93.69%)

## STEP 6: Branch Coverage Pattern Identification

**Branch Coverage Gaps Indicate:**
- Lines are hit but conditional branches within those lines are incomplete
- Need to test BOTH true and false paths of conditionals
- May need additional test scenarios for complete logical coverage

### Branch Coverage Pattern Matching

**If showing specific uncovered lines with 100% line coverage:**
- **Constructor lines (210-296):** Test both successful initialization AND failure recovery
- **Validation lines (324, 436):** Test both successful validation AND error handling
- **Processing lines (553):** Test both successful processing AND error conditions
- **Runtime lines (871, 891):** Test all conditional combinations

## STEP 7: Dual Path Analysis

For each target line, identify:
1. **Success scenario** - Normal operation flow
2. **Failure scenario** - Error/exception handling
3. **Edge cases** - Boundary conditions
4. **Logical combinations** - All permutations of complex conditionals

## STEP 8: Branch Implementation Strategy

```typescript
// For each uncovered branch, create complementary test
// If you have failure test, add success test
// If you have success test, add failure test
// If you have simple conditional, add complex conditional scenarios
```

## STEP 9: Branch Coverage Verification
- [ ] Run coverage after each dual path test
- [ ] Verify branch percentage increases
- [ ] Ensure no line coverage regression
- [ ] Target: 100% across all coverage dimensions
