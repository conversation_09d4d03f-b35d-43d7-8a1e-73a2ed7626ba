# Testing Quick Reference Guide

**Source**: Perfect Coverage Methodology - MemorySafetyManagerEnhanced Case Study  
**Usage**: Quick reference for complex testing scenarios in OA Framework  

---

## 🚨 Fire-and-Forget Async IIFE Issues

### Problem Pattern (❌ AVOID)
```typescript
export function problematicFunction(): void {
  (async () => {
    // Fire-and-forget async operation
    await someAsyncOperation();
  })(); // Jest cannot track completion - causes timeouts
}
```

### Solution Pattern (✅ USE)
```typescript
export async function fixedFunction(): Promise<void> {
  // Directly awaitable async function
  await someAsyncOperation();
}

// Test implementation:
it('should handle async operation', async () => {
  await fixedFunction(); // Jest can track completion
});
```

---

## 🎯 Surgical Precision Branch Coverage

### Target Pattern: Ternary Operator False Branches
```typescript
// Source code pattern:
const result = error instanceof Error ? error.message : String(error);
//             ^^^^^^^^^^^^^^^^^^^^^ TRUE branch (usually covered)
//                                                      ^^^^^^^^^^^^^ FALSE branch (often uncovered)
```

### Coverage Technique: Non-Error Object Injection
```typescript
it('should cover ternary operator false branch', async () => {
  const nonErrorObjects = [
    'string-error',           // String
    404,                      // Number
    { code: 'FAILED' },       // Object
    null,                     // Null
    undefined                 // Undefined
  ];

  for (const errorValue of nonErrorObjects) {
    // Mock to throw non-Error object
    mockMethod.mockImplementation(() => Promise.reject(errorValue));
    
    // This triggers String(error) conversion
    await expect(targetMethod()).rejects.toBe(errorValue);
  }
});
```

---

## 🔧 Mock Configuration Patterns

### Async Method Mocking
```typescript
// ✅ CORRECT: Async method rejection
(service as any).asyncMethod = jest.fn()
  .mockImplementation(() => Promise.reject(errorValue));

// ✅ ALTERNATIVE: Using mockRejectedValue
(service as any).asyncMethod = jest.fn()
  .mockRejectedValue(errorValue);
```

### Sync Method Mocking
```typescript
// ✅ CORRECT: Sync method exception
(service as any).syncMethod = jest.fn()
  .mockImplementation(() => {
    throw errorValue; // Direct throw, not Promise.reject
  });
```

### Expectation Patterns
```typescript
// ✅ CORRECT: Specific error value expectation
await expect(method()).rejects.toBe(errorValue);

// ❌ INCORRECT: Generic throw expectation (too broad)
await expect(method()).rejects.toThrow();
```

---

## 📊 Coverage Analysis Workflow

### Step 1: Identify Uncovered Branches
```bash
# Run coverage report
npm test -- --coverage --collectCoverageFrom="path/to/file.ts"

# Look for:
# - Branch coverage < 100%
# - Specific uncovered line numbers
# - Ternary operators in source code
```

### Step 2: Analyze Source Code Patterns
```typescript
// Common uncovered patterns:
error instanceof Error ? error.message : String(error)  // Line X
condition ? 'value1' : 'value2'                        // Line Y
obj?.property || 'default'                             // Line Z
```

### Step 3: Create Targeted Tests
```typescript
// Target each uncovered branch systematically
it('should cover line X - error instanceof Error false branch', () => {
  // Inject non-Error object to trigger String(error)
});

it('should cover line Y - condition false branch', () => {
  // Set up scenario where condition evaluates to false
});
```

---

## 🛠️ Common Testing Scenarios

### Error Handling with Non-Error Objects
```typescript
const testErrorTypes = [
  'string-error',
  500,
  { error: 'object-error', code: 500 },
  null,
  undefined,
  false,
  Symbol('error'),
  new Map([['error', 'map-error']]),
  /regex-error/
];

testErrorTypes.forEach(errorValue => {
  it(`should handle ${typeof errorValue} error type`, async () => {
    mockService.mockRejectedValue(errorValue);
    await expect(targetMethod()).rejects.toBe(errorValue);
    expect(typeof String(errorValue)).toBe('string');
  });
});
```

### Timing Infrastructure Error Testing
```typescript
it('should handle timing infrastructure errors', async () => {
  const timingError = { code: 'TIMING_FAILED' };
  
  // Mock timer to throw non-Error object
  const originalTimer = (instance as any)._resilientTimer;
  (instance as any)._resilientTimer = {
    start: () => ({
      end: () => { throw timingError; }
    })
  };

  try {
    await instance.targetMethod();
  } catch (error) {
    expect(error).toBe(timingError);
  } finally {
    // Always restore original timer
    (instance as any)._resilientTimer = originalTimer;
  }
});
```

### Initialization Error Testing
```typescript
it('should handle initialization errors with non-Error objects', async () => {
  const initError = 'Initialization failed';
  
  const testInstance = new TargetClass();
  (testInstance as any)._dependency = {
    initialize: jest.fn().mockImplementation(() => {
      throw initError; // Non-Error object in initialization
    })
  };

  try {
    await (testInstance as any).doInitialize();
  } catch (error) {
    expect(error).toBe(initError);
  }
});
```

---

## 🚀 Performance Optimization Tips

### Fast Test Execution
```typescript
// ✅ DO: Use specific mocks
mockService.mockResolvedValue(specificValue);

// ❌ AVOID: Complex setup for simple tests
// Unnecessary complex mocking that slows down tests

// ✅ DO: Clean up in finally blocks
try {
  // Test logic
} finally {
  // Restore mocks
  jest.restoreAllMocks();
}
```

### Efficient Coverage Testing
```typescript
// ✅ DO: Batch similar test cases
const errorScenarios = [
  { input: 'error1', expected: 'result1' },
  { input: 'error2', expected: 'result2' }
];

errorScenarios.forEach(scenario => {
  // Test each scenario efficiently
});

// ❌ AVOID: Duplicate test setup
// Repeating identical setup code across tests
```

---

## 📋 Troubleshooting Checklist

### When Tests Timeout (30+ seconds)
- [ ] Check for fire-and-forget async patterns `(async () => {})()`
- [ ] Ensure all async functions return `Promise<void>` not `void`
- [ ] Verify tests `await` all async operations
- [ ] Look for unresolved promises in mocks

### When Branch Coverage is Incomplete
- [ ] Identify ternary operators in source code
- [ ] Check `error instanceof Error` patterns
- [ ] Create non-Error object test cases
- [ ] Verify mock configuration for async/sync methods
- [ ] Test with multiple error types systematically

### When Mocks Don't Work
- [ ] Use `mockImplementation()` for complex scenarios
- [ ] Use `mockRejectedValue()` only for async methods
- [ ] Use direct `throw` for synchronous methods
- [ ] Verify expectation patterns `.rejects.toBe()` vs `.rejects.toThrow()`
- [ ] Check mock restoration in cleanup

### When Coverage Reports are Inconsistent
- [ ] Run tests with `--coverage` flag consistently
- [ ] Use `--collectCoverageFrom` for specific files
- [ ] Clear Jest cache: `npx jest --clearCache`
- [ ] Verify no cached coverage data affecting results

---

## 🏆 Success Metrics

### Target Coverage Goals
- **Statement Coverage**: 100% (Perfect)
- **Branch Coverage**: 100% (Perfect)
- **Function Coverage**: 100% (Perfect)
- **Line Coverage**: 100% (Perfect)

### Performance Benchmarks
- **Test Execution**: < 10 seconds for comprehensive suites
- **No Timeouts**: All tests complete within Jest timeout limits
- **Pass Rate**: 100% consistent pass rate
- **Maintainability**: All code paths tested and verified

---

## 📚 Related Documentation

- **Main Guide**: `./perfect-coverage-methodology.md` - Complete methodology
- **Source Example**: `shared/src/base/MemorySafetyManagerEnhanced.ts` - Reference implementation
- **Test Example**: `shared/src/base/__tests__/MemorySafetyManagerEnhanced.test.ts` - 110 tests, 100% coverage

---

**Quick Reference Version**: 1.0  
**Last Updated**: 2025-01-20  
**Usage**: Keep open during complex testing work for instant reference
