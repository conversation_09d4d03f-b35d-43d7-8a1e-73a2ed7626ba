# Test Coverage AI Assistant Decision Prompt

## BEFORE WRITING ANY TESTS - <PERSON><PERSON><PERSON><PERSON> THIS CHECKLIST:

### 1. COVERAGE ANALYSIS
Ask user: "Please run: npm test -- --coverage and paste the uncovered lines"

Analyze patterns:
- Lines 200-230 → Constructor catch blocks
- Lines 290-300 → Setup/reconfiguration catch blocks  
- Lines 550-600 → Processing/validation catch blocks
- Lines 900+ → Runtime condition catch blocks

### 2. PATTERN MATCHING
Match uncovered lines to patterns from ./docs/lessons/testing-patterns/jest-mocking-patterns.md:

**If lines 200-230 (like 209-228):**
- Use jest.doMock constructor failure pattern
- Target: Constructor/initialization catch blocks
- Method: _initializeResilientTimingSync, constructor

**If line 295 or similar:**
- Use jest.doMock reconfiguration failure pattern
- Target: Setup/reconfiguration catch blocks  
- Method: doInitialize, configure

**If line 551 or 550-600 range:**
- Use jest.doMock processing failure pattern
- Target: Processing/validation catch blocks
- Method: _validateDependencies, _processData

**If lines 900+ (like 933-940):**
- Use natural error conditions pattern
- Target: Runtime condition catch blocks
- Method: evaluateCondition, checkState

### 3. IMPLEMENTATION PROCESS
1. Copy ./docs/lessons/templates/catch-block-coverage.template.ts
2. Replace PLACEHOLDER values with actual values
3. Use jest.doMock + dynamic import pattern
4. Ensure proper cleanup

### 4. VERIFICATION CHECKLIST
- [ ] Mock dependencies BEFORE import using jest.doMock()
- [ ] Use jest.resetModules() for clean state
- [ ] Use dynamic import: await import()
- [ ] Test fallback behavior works correctly
- [ ] Cleanup: jest.dontMock() + jest.resetModules()
- [ ] Verify lines are covered after test runs

### 5. SUCCESS CRITERIA
- Coverage increases to target lines
- Test completes in 15-30 minutes (not hours)
- Fallback behavior is verified to work correctly
- No existing test regressions

---

## BRANCH COVERAGE DECISION PROCESS

### 6. BRANCH COVERAGE ANALYSIS
After achieving 100% line coverage, check branch coverage:

Ask user: "What is the current branch coverage percentage?"

**If < 100% branch coverage:**
- Lines are hit but conditional branches within those lines are incomplete
- Need dual path testing: both success AND failure branches
- Focus on the same line numbers but test complementary scenarios

### 7. BRANCH PATTERN MATCHING
**For each uncovered branch:**

**If constructor lines (210-296) with line coverage 100%:**
- Create SUCCESS path test: Normal initialization flow
- Verify FAILURE path test exists: Constructor error handling
- Template: Constructor Dual Path Pattern

**If validation lines (324, 436) with line coverage 100%:**
- Create SUCCESS path test: Valid input validation
- Verify FAILURE path test exists: Invalid input/error handling
- Template: Validation Dual Path Pattern

**If processing lines (553) with line coverage 100%:**
- Create SUCCESS path test: Successful processing flow
- Verify FAILURE path test exists: Processing error handling
- Template: Processing Dual Path Pattern

**If runtime lines (871, 891) with line coverage 100%:**
- Create tests for all conditional combinations
- Test different logical paths through complex conditions
- Template: Multi-Conditional Branch Pattern

### 8. BRANCH IMPLEMENTATION STRATEGY
1. Identify which branch is missing (success or failure)
2. Create complementary test to existing test
3. Use dual path templates from ./docs/lessons/templates/
4. Verify both branches are now covered

### 9. BRANCH COVERAGE VERIFICATION CHECKLIST
- [ ] Test both true and false paths of conditionals
- [ ] Test all logical combinations for complex conditions
- [ ] Maintain 100% line coverage while increasing branch coverage
- [ ] Target: 100% statement, branch, function, and line coverage
