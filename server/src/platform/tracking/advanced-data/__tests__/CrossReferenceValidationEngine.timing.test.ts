import { CrossReferenceValidationEngine } from '../CrossReferenceValidationEngine';

describe('CrossReferenceValidationEngine P1 Resilient Timing', () => {
  test('records timing for validateCrossReferences', async () => {
    const svc = new CrossReferenceValidationEngine({} as any);
    await svc.initialize();

    await svc.validateCrossReferences('component-1', [
      { target: 'component-2', type: 'reference', path: 'ctx.a' },
      { target: 'component-3', type: 'reference', path: 'ctx.b' }
    ]);

    const metrics = (svc as any)._metricsCollector.createCompatibleMetrics();
    expect(Object.keys(metrics)).toEqual(expect.arrayContaining(['validateCrossReferences']));

    await svc.shutdown();
  });
});

