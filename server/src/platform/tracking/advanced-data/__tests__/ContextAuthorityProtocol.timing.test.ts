import { ContextAuthorityProtocol } from '../ContextAuthorityProtocol';

describe('ContextAuthorityProtocol P1 Resilient Timing', () => {
  test('records timing for validateAuthority and performAuthorityValidation', async () => {
    const svc = new ContextAuthorityProtocol({} as any);
    await svc.initialize();

    await svc.validateAuthority('foundation-context', 'create', 'standard' as any, { user: 'u1' });

    const metrics = (svc as any)._metricsCollector.createCompatibleMetrics();
    expect(Object.keys(metrics)).toEqual(expect.arrayContaining(['validateAuthority', 'performAuthorityValidation']));

    await svc.shutdown();
  });
});

