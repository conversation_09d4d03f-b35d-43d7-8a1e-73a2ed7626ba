import { SmartPathResolutionSystem } from '../SmartPathResolutionSystem';
import { getTimerCoordinator } from '../../../../../../shared/src/base/TimerCoordinationService';

describe('SmartPathResolutionSystem MEM-SAFE-002 compliance', () => {
  const coordinator: any = getTimerCoordinator();

  beforeEach(() => {
    jest.spyOn(coordinator as any, 'clearServiceTimers').mockImplementation(() => {});
    jest.spyOn(coordinator as any, 'clearAllTimers').mockImplementation(() => {});
    jest.spyOn(coordinator as any, 'createCoordinatedInterval').mockImplementation((...args: any[]) => `${args[2]}:${args[3]}`);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('path cache is bounded with eviction warning', async () => {
    const sys = new SmartPathResolutionSystem({ maxCacheSize: 5 } as any);
    await sys.initialize();

    // Resolve many paths to fill the cache
    for (let i = 0; i < 10; i++) {
      await sys.resolvePath(`ctx-${i}`, 'component', { req: i } as any);
    }

    // Expect cache size not to exceed max
    const analytics = sys.getPathAnalytics();
    expect(analytics.totalResolutions).toBeGreaterThan(0);

    await sys.shutdown();
    const svcSpy = (coordinator as any).clearServiceTimers as jest.Mock;
    if (svcSpy && svcSpy.mock) {
      expect(svcSpy).toHaveBeenCalledWith('SmartPathResolutionSystem');
    } else {
      expect((coordinator as any).clearAllTimers).toHaveBeenCalled();
    }
  });
});

