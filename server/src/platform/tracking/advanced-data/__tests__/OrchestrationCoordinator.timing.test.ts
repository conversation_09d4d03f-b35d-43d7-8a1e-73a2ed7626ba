import { OrchestrationCoordinator } from '../OrchestrationCoordinator';

describe('OrchestrationCoordinator P1 Resilient Timing', () => {
  test('records timing for executeWorkflow and coordinateServices', async () => {
    const svc = new OrchestrationCoordinator({} as any);
    await svc.initialize();

    await svc.coordinateServices([], { type: 'round-robin' } as any);
    await svc.executeWorkflow({ workflowId: 'wf1', steps: [] } as any, { contextId: 'c1' } as any);

    const metrics = (svc as any)._metricsCollector.createCompatibleMetrics();
    expect(Object.keys(metrics)).toEqual(expect.arrayContaining(['executeWorkflow', 'coordinateServices']));

    await svc.shutdown();
  });
});

