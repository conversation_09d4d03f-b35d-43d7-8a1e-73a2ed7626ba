import { ContextAuthorityProtocol } from '../ContextAuthorityProtocol';
import { getTimerCoordinator } from '../../../../../../shared/src/base/TimerCoordinationService';

const coordinator: any = getTimerCoordinator();

describe('ContextAuthorityProtocol MEM-SAFE-002 compliance', () => {
  beforeEach(() => {
    jest.spyOn(coordinator as any, 'clearServiceTimers').mockImplementation(() => {});
    jest.spyOn(coordinator as any, 'clearAllTimers').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('validation cache and history are bounded with eviction', async () => {
    const cap = new ContextAuthorityProtocol({ maxCacheSize: 5 } as any);
    await cap.initialize();

    // Add validations to exceed cache size
    for (let i = 0; i < 20; i++) {
      await cap.validateAuthority(`ctx-${i}`, 'op', { user: 'u' } as any);
    }

    await cap.shutdown();
    const svcSpy = (coordinator as any).clearServiceTimers as jest.Mock;
    if (svcSpy && svcSpy.mock) {
      expect(svcSpy).toHaveBeenCalledWith('ContextAuthorityProtocol');
    } else {
      expect((coordinator as any).clearAllTimers).toHaveBeenCalled();
    }
  });
});

