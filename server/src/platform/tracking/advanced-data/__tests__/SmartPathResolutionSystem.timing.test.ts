import { SmartPathResolutionSystem } from '../SmartPathResolutionSystem';

describe('SmartPathResolutionSystem P1 Resilient Timing', () => {
  test('records timing for resolvePath', async () => {
    const svc = new SmartPathResolutionSystem({ maxCacheSize: 5 } as any);
    await svc.initialize();

    await svc.resolvePath('ctx', 'component', { a: 1 });

    const metrics = (svc as any)._metricsCollector.createCompatibleMetrics();
    expect(Object.keys(metrics)).toEqual(expect.arrayContaining(['resolvePath']));

    await svc.shutdown();
  });
});

