import { describe, expect, test, jest } from '@jest/globals';

describe('DashboardManager MEM-SAFE-002 compliance', () => {
  test('timer cleanup uses clearServiceTimers with serviceId', async () => {
    jest.resetModules();

    let DashboardManager: any;
    const path = require('path');

    jest.isolateModules(() => {
      // Resolve exact module id as SUT will load
      const sutAbs = require.resolve('../DashboardManager');
      const sutDir = path.dirname(sutAbs);
      const timerId = require.resolve('../../../../../shared/src/base/TimerCoordinationService', { paths: [sutDir] });

      // Get the exact singleton instance SUT will use and override its method
      const timerModule = require(timerId);
      const coordinator = timerModule.getTimerCoordinator();
      const clearSvcMock = jest.fn();
      coordinator.clearServiceTimers = clearSvcMock;

      // Import SUT after override is in place
      DashboardManager = require('../DashboardManager').DashboardManager;

      // Attach the mock to outer scope for assertion
      (global as any).__clearSvcMock = clearSvcMock;
    });

    const mgr = new DashboardManager({ custom: { refreshInterval: 5 } } as any);
    await mgr.initialize();
    // Call protected method directly to ensure we hit the exact code path under test
    await (mgr as any).doShutdown();

    expect((global as any).__clearSvcMock).toHaveBeenCalledWith('DashboardManager');
  });

  test('falls back to clearAllTimers when clearServiceTimers is missing', async () => {
    jest.resetModules();

    let DashboardManager: any;
    const path = require('path');

    jest.isolateModules(() => {
      const sutAbs = require.resolve('../DashboardManager');
      const sutDir = path.dirname(sutAbs);
      const timerId = require.resolve('../../../../../shared/src/base/TimerCoordinationService', { paths: [sutDir] });

      const timerModule = require(timerId);
      const coordinator = timerModule.getTimerCoordinator();
      // Override to simulate missing clearServiceTimers and available clearAllTimers
      (coordinator as any).clearServiceTimers = undefined;
      const clearAllMock = jest.fn();
      (coordinator as any).clearAllTimers = clearAllMock;

      DashboardManager = require('../DashboardManager').DashboardManager;
      (global as any).__clearAllMock = clearAllMock;
    });

    const mgr = new DashboardManager({ custom: { refreshInterval: 5 } } as any);
    await mgr.initialize();
    await (mgr as any).doShutdown();

    expect((global as any).__clearAllMock).toHaveBeenCalled();
  });

  test('logs warning when clearAllTimers throws during fallback', async () => {
    jest.resetModules();

    let DashboardManager: any;
    const path = require('path');

    jest.isolateModules(() => {
      const sutAbs = require.resolve('../DashboardManager');
      const sutDir = path.dirname(sutAbs);
      const timerId = require.resolve('../../../../../shared/src/base/TimerCoordinationService', { paths: [sutDir] });

      const timerModule = require(timerId);
      const coordinator = timerModule.getTimerCoordinator();
      // Force fallback then throw
      (coordinator as any).clearServiceTimers = undefined;
      (coordinator as any).clearAllTimers = jest.fn(() => { throw new Error('fallback-fail'); });

      DashboardManager = require('../DashboardManager').DashboardManager;
    });

    const mgr = new DashboardManager({ custom: { refreshInterval: 5 } } as any);
    const warnSpy = jest.spyOn(mgr as any, 'log');
    await mgr.initialize();
    await (mgr as any).doShutdown();

    expect(warnSpy).toHaveBeenCalledWith(
      'warn',
      expect.stringContaining('Timer cleanup error during shutdown'),
      expect.objectContaining({ error: expect.any(String) })
    );
  });

  test('handles missing timer methods gracefully (no-op)', async () => {
    jest.resetModules();

    let DashboardManager: any;
    const path = require('path');

    jest.isolateModules(() => {
      const sutAbs = require.resolve('../DashboardManager');
      const sutDir = path.dirname(sutAbs);
      const timerId = require.resolve('../../../../../shared/src/base/TimerCoordinationService', { paths: [sutDir] });

      const timerModule = require(timerId);
      const coordinator = timerModule.getTimerCoordinator();
      // Remove both to hit typeof checks (both false)
      (coordinator as any).clearServiceTimers = undefined;
      (coordinator as any).clearAllTimers = undefined;

      DashboardManager = require('../DashboardManager').DashboardManager;
      (global as any).__coordinator = coordinator;
    });

    const mgr = new DashboardManager({ custom: { refreshInterval: 5 } } as any);
    await mgr.initialize();
    await (mgr as any).doShutdown();

    const coordinator = (global as any).__coordinator;
    expect(coordinator.clearServiceTimers).toBeUndefined();
    expect(coordinator.clearAllTimers).toBeUndefined();
  });
});
