/**
 * @file FileManager.functional.test.ts
 * @description Functional tests for FileManager (core-managers)
 */

import { promises as fs } from 'fs';
import { join } from 'path';
import { FileManager } from '../FileManager';

const TMP_DIR = join(process.cwd(), 'tmp_file_manager_tests');
const TMP_FILE = join(TMP_DIR, 'data.json');
const TMP_HIDDEN = join(TMP_DIR, '.hidden');

describe('FileManager (functional)', () => {
  let mgr: FileManager;

  beforeAll(async () => {
    mgr = new FileManager({ custom: { allowedExtensions: ['.json', '.txt'] } } as any);
    await mgr.initialize();
    await fs.mkdir(TMP_DIR, { recursive: true });
  });

  afterAll(async () => {
    try {
      await mgr.shutdown();
    } catch {}
    try {
      await fs.rm(TMP_DIR, { recursive: true, force: true });
    } catch {}
  });

  test('writeFile -> readFile -> deleteFile round trip', async () => {
    const payload = { a: 1, b: 'x' };

    const writeRes = await mgr.writeFile(TMP_FILE, payload);
    expect(writeRes.success).toBe(true);
    expect(writeRes.operation).toBe('write');

    const readRes = await mgr.readFile(TMP_FILE);
    expect(readRes.success).toBe(true);
    expect(readRes.data).toContain('"a": 1');

    const delRes = await mgr.deleteFile(TMP_FILE);
    expect(delRes.success).toBe(true);
    expect(await mgr.exists(TMP_FILE)).toBe(false);
  });

  test('ensureDirectory creates directories recursively', async () => {
    const nested = join(TMP_DIR, 'a/b/c');
    await mgr.ensureDirectory(nested);

    // Use fs to verify
    const stat = await fs.stat(nested);
    expect(stat.isDirectory()).toBe(true);
  });

  test('listFiles excludes dotfiles', async () => {
    const f1 = join(TMP_DIR, 'one.txt');
    await fs.writeFile(f1, '1', 'utf8');
    await fs.writeFile(TMP_HIDDEN, 'hidden', 'utf8');

    const files = await mgr.listFiles(TMP_DIR);
    expect(files).toContain('one.txt');
    expect(files).not.toContain('.hidden');
  });

  test('getFileStats returns expected shape', async () => {
    await mgr.writeFile(TMP_FILE, { z: 9 });
    const stats = await mgr.getFileStats(TMP_FILE);
    expect(typeof stats.size).toBe('number');
    expect(stats.type).toBe('file');
    expect(stats.readable).toBe(true);
  });

  test('getPermissions & setPermissions round trip', async () => {
    const before = await mgr.getPermissions(TMP_FILE);
    expect(typeof before.octal).toBe('string');

    await mgr.setPermissions(TMP_FILE, { mode: '600' });
    const after = await mgr.getPermissions(TMP_FILE);
    expect(after.octal.endsWith('600')).toBe(true);
  });

  test('validation blocks disallowed extension', async () => {
    const badFile = join(TMP_DIR, 'evil.exe');
    await expect(mgr.writeFile(badFile, 'nope')).rejects.toThrow(/not allowed/);
  });

  test('validation blocks path traversal', async () => {
    await expect(mgr.writeFile('../outside.json', 'x')).rejects.toThrow(/Path traversal/);
  });

  test('exists() true for present file, false for missing', async () => {
    await mgr.writeFile(TMP_FILE, { a: 2 });
    expect(await mgr.exists(TMP_FILE)).toBe(true);
    await mgr.deleteFile(TMP_FILE);
    expect(await mgr.exists(TMP_FILE)).toBe(false);
  });
});

