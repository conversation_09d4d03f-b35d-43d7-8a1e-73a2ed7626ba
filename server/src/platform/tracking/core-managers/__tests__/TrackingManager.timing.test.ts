import { TrackingManager } from '../TrackingManager';
import { getTimerCoordinator } from '../../../../../../shared/src/base/TimerCoordinationService';

// Timing integration tests

describe('TrackingManager P1 Resilient Timing', () => {
  test('captures timing for doTrack/processBatch/updateManagerMetrics', async () => {
    const mgr = new TrackingManager({ custom: { batchProcessingSize: 5 }, monitoring: { enabled: true, interval: 50 } } as any);
    await mgr.initialize();

    // Feed enough items to trigger processBatch
    for (let i = 0; i < 6; i++) {
      await mgr.track({ componentId: `c-${i}`, timestamp: new Date().toISOString(), status: 'in-progress' } as any);
    }

    // Force metrics update path
    const metrics = await mgr.getMetrics();
    expect(metrics).toBeTruthy();

    // Verify resilient metrics contain timing entries
    const safeMetrics = (mgr as any)._metricsCollector.createCompatibleMetrics();
    expect(Object.keys(safeMetrics)).toEqual(expect.arrayContaining(['doTrack', 'processBatch', 'updateManagerMetrics']));

    await mgr.shutdown();
  });
});

