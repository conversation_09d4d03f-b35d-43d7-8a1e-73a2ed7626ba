import { TrackingManager } from '../TrackingManager';
import { getTimerCoordinator } from '../../../../../../shared/src/base/TimerCoordinationService';

const coordinator: any = getTimerCoordinator();
let clearServiceTimersSpy: jest.SpyInstance | undefined;
let clearAllTimersSpy: jest.SpyInstance | undefined;
let createIntervalSpy: jest.SpyInstance | undefined;
let removeTimerSpy: jest.SpyInstance | undefined;

beforeEach(() => {
  clearServiceTimersSpy = jest.spyOn(coordinator as any, 'clearServiceTimers').mockImplementation(() => {});
  clearAllTimersSpy = jest.spyOn(coordinator as any, 'clearAllTimers').mockImplementation(() => {});
  createIntervalSpy = jest
    .spyOn(coordinator as any, 'createCoordinatedInterval')
    .mockImplementation((...args: any[]) => `${args[2]}:${args[3]}`);
  removeTimerSpy = jest.spyOn(coordinator as any, 'removeCoordinatedTimer').mockImplementation(() => {});
});

afterEach(() => {
  jest.clearAllMocks();
});

describe('TrackingManager MEM-SAFE-002 compliance', () => {
  test('queue is bounded and evicts when exceeding MAX_QUEUE_SIZE', async () => {
    const mgr = new TrackingManager({ custom: { batchProcessingSize: 10000 }, monitoring: { enabled: false, interval: 60000 } } as any);
    await mgr.initialize();

    const max = (TrackingManager as any).MAX_QUEUE_SIZE as number;
    for (let i = 0; i < max + 10; i++) {
      await mgr.track({ componentId: 'c', status: 'ok', context: {}, payload: { i } } as any);
    }
    const metrics: any = await mgr.getMetrics();
    expect(metrics.resources.queueSize).toBeLessThanOrEqual(max);

    await mgr.shutdown();
  });

  test('active operations is bounded and evicts oldest when exceeding MAX_ACTIVE_OPERATIONS', async () => {
    const mgr = new TrackingManager({ custom: { batchProcessingSize: 10000 }, monitoring: { enabled: false, interval: 60000 } } as any);
    await mgr.initialize();

    const max = (TrackingManager as any).MAX_ACTIVE_OPERATIONS as number;
    for (let i = 0; i < max + 5; i++) {
      const data: any = { componentId: 'c', status: 'ok', context: {}, payload: { i } };
      const p = mgr.track(data).catch(() => {});
      // Intentionally do not await to simulate many concurrent updates; ensure map grows
      await Promise.resolve();
      await p;
    }

    const activeSize = (mgr as any)._activeOperations.size as number;
    expect(activeSize).toBeLessThanOrEqual(max);

    await mgr.shutdown();
  });

  test('doShutdown clears timers via serviceId or falls back to clearAllTimers', async () => {
    const mgr = new TrackingManager({ monitoring: { enabled: true, interval: 500 } } as any);
    await mgr.initialize();

    // simulate timer creation
    const svc: any = getTimerCoordinator();
    svc.createCoordinatedInterval(() => {}, 1000, 'TrackingManager', 'background-processing');
    svc.createCoordinatedInterval(() => {}, 1000, 'TrackingManager', 'monitoring');

    await mgr.shutdown();

    // Prefer service-scoped cleanup
    if (typeof svc.clearServiceTimers === 'function') {
      expect(svc.clearServiceTimers).toHaveBeenCalledWith('TrackingManager');
    } else {
      expect(svc.clearAllTimers).toHaveBeenCalled();
    }
  });
});

