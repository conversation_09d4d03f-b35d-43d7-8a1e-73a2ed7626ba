import { DashboardManager } from '../DashboardManager';

describe('DashboardManager P1 Resilient Timing', () => {
  test('records timing for getDashboardData, updateDashboardData, renderComponent', async () => {
    const mgr = new DashboardManager({ testMode: true } as any);
    await mgr.initialize();

    // seed a dashboard to operate on
    const id = 'db-1';
    (mgr as any).dashboards.set(id, { id, widgets: [], metadata: { created: new Date().toISOString() } });

    await mgr.getDashboardData(id);
    await mgr.updateDashboardData(id, { widgets: [{ id: 'w1', type: 'chart' }] } as any);
    await mgr.renderComponent('cmp-1', { value: 1 });

    const metrics = (mgr as any)._metricsCollector.createCompatibleMetrics();
    expect(Object.keys(metrics)).toEqual(expect.arrayContaining(['getDashboardData', 'updateDashboardData', 'renderComponent']));

    await mgr.shutdown();
  });
});

