import { RealTimeManager } from '../RealTimeManager';

describe('RealTimeManager P1 Resilient Timing', () => {
  test('captures timing for processEventQueue and getMetrics', async () => {
    const mgr = new RealTimeManager({ custom: { heartbeatInterval: 10 } } as any);
    await mgr.initialize();

    // Simulate a few events to process
    (mgr as any).eventQueue.push({ id: 'e1' }, { id: 'e2' });
    await (mgr as any).processEventQueue();

    const metrics = await mgr.getMetrics();
    expect(metrics).toBeTruthy();

    const safeMetrics = (mgr as any)._metricsCollector.createCompatibleMetrics();
    expect(Object.keys(safeMetrics)).toEqual(expect.arrayContaining(['processEventQueue', 'getMetrics']));

    await mgr.shutdown();
  });
});

