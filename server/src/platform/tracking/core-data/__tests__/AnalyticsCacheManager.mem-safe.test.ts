import { AnalyticsCacheManager } from '../AnalyticsCacheManager';
import { getTimerCoordinator } from '../../../../../../shared/src/base/TimerCoordinationService';

describe('AnalyticsCacheManager MEM-SAFE-002 compliance', () => {
  const coordinator: any = getTimerCoordinator();

  beforeEach(() => {
    jest.spyOn(coordinator as any, 'clearServiceTimers').mockImplementation(() => {});
    jest.spyOn(coordinator as any, 'clearAllTimers').mockImplementation(() => {});
    jest.spyOn(coordinator as any, 'createCoordinatedInterval').mockImplementation((...args: any[]) => `${args[2]}:${args[3]}`);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('shutdown clears timers via serviceId', async () => {
    const mgr = new AnalyticsCacheManager();
    await mgr.initialize();

    await mgr.shutdown();

    const svcSpy = (coordinator as any).clearServiceTimers as jest.Mock;
    if (svcSpy && svcSpy.mock) {
      expect(svcSpy).toHaveBeenCalledWith('AnalyticsCacheManager');
    } else {
      expect((coordinator as any).clearAllTimers).toHaveBeenCalled();
    }
  });
});

