import { SessionLogTracker } from '../SessionLogTracker';
import { getTimerCoordinator } from '../../../../../../shared/src/base/TimerCoordinationService';

const coordinator: any = getTimerCoordinator();
let clearServiceTimersSpy: jest.SpyInstance | undefined;
let clearAllTimersSpy: jest.SpyInstance | undefined;
beforeEach(() => {
  clearServiceTimersSpy = jest.spyOn(coordinator as any, 'clearServiceTimers').mockImplementation(() => {});
  clearAllTimersSpy = jest.spyOn(coordinator as any, 'clearAllTimers').mockImplementation(() => {});
  jest
    .spyOn(coordinator as any, 'createCoordinatedInterval')
    .mockImplementation((...args: any[]) => `${args[2]}:${args[3]}`);
});

afterEach(() => {
  jest.clearAllMocks();
});

describe('SessionLogTracker MEM-SAFE-002 compliance', () => {
  test('bounded session history with eviction', async () => {
    const tracker = new SessionLogTracker();
    await tracker.initialize();

    // Create and end many sessions to exceed history size
    const maxHistory = (await import('../../../../../../shared/src/constants/platform/tracking/tracking-constants'))
      .getMaxTrackingHistorySize();

    const total = maxHistory + 10;
    for (let i = 0; i < total; i++) {
      const id = `s-${i}`;
      await tracker.startSession(id, 'actor-1', 'system', {} as any);
      await tracker.endSession(id, 'done');
    }

    // Invoke boundary enforcement (periodic in production)
    await (tracker as any).enforceHistoryBoundaries();

    // The sessionHistory map size should be bounded
    const auditTrail: any = await tracker.generateAuditTrail({ includeDetails: true });
    // slice(-100) in generateAuditTrail caps looked-up entries, but our enforced bound applies at map size level
    expect(auditTrail.details.sessionHistory.length).toBeLessThanOrEqual(100);

    await tracker.shutdown();
    if (clearServiceTimersSpy && clearServiceTimersSpy.mock) {
      expect(clearServiceTimersSpy).toHaveBeenCalledWith('SessionLogTracker');
    } else {
      expect(clearAllTimersSpy).toHaveBeenCalled();
    }
  });

  test('doShutdown clears timers and processes sessions', async () => {
    const tracker = new SessionLogTracker();
    await tracker.initialize();

    // Simulate timers created
    (coordinator as any).createCoordinatedInterval(() => {}, 1000, 'SessionLogTracker', 'performance-monitoring');
    (coordinator as any).createCoordinatedInterval(() => {}, 1000, 'SessionLogTracker', 'log-rotation');

    await tracker.shutdown();

    if (clearServiceTimersSpy && clearServiceTimersSpy.mock) {
      expect(clearServiceTimersSpy).toHaveBeenCalledWith('SessionLogTracker');
    } else {
      expect(clearAllTimersSpy).toHaveBeenCalled();
    }
  });

  test('lifecycle initialize → operations → shutdown', async () => {
    const tracker = new SessionLogTracker();
    await tracker.initialize();

    // Start a session and add logs to exercise real operations
    const sessionId = 'sess-1';
    await tracker.startSession(sessionId, 'actor-1', 'system');

    await tracker.logSessionEvent(sessionId, 'info', 'event1', 'm1');
    await tracker.logSessionEvent(sessionId, 'warn', 'event2', 'm2');
    await tracker.endSession(sessionId, 'done');

    // Get history to ensure it’s recorded
    const history = await tracker.getSessionHistory(sessionId);
    expect(Array.isArray(history)).toBe(true);

    await tracker.shutdown();
  });
});

