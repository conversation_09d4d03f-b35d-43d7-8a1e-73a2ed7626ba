import { AnalyticsCacheManager } from '../AnalyticsCacheManager';

describe('AnalyticsCacheManager P1 Resilient Timing', () => {
  test('records timing for cacheAnalyticsData and getCachedAnalytics', async () => {
    const mgr = new AnalyticsCacheManager({ testMode: true } as any);
    await mgr.initialize();

    const key = 'k1';
    await mgr.cacheAnalyticsData(key, { query: { q: 'x' }, result: [{ a: 1 }], timestamp: Date.now(), source: 'test' } as any);
    await mgr.getCachedAnalytics(key);

    const metrics = (mgr as any)._metricsCollector.createCompatibleMetrics();
    expect(Object.keys(metrics)).toEqual(expect.arrayContaining(['cacheAnalyticsData', 'getCachedAnalytics']));

    await mgr.shutdown();
  });
});

