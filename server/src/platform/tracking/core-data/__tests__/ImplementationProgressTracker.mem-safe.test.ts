import { getTimerCoordinator } from '../../../../../../shared/src/base/TimerCoordinationService';

// We'll resolve and spy on the exact TimerCoordinationService instance used by the SUT
const globalCoordinator: any = getTimerCoordinator();

describe('ImplementationProgressTracker MEM-SAFE-002 compliance', () => {
  afterEach(() => {
    jest.clearAllMocks();
    jest.resetModules();
  });

  test('doShutdown clears timers via serviceId with fallback to clearAllTimers', async () => {
    let ImplementationProgressTracker: any;
    let svc: any;

    await jest.isolateModulesAsync(async () => {
      const timerModule = require('../../../../../../shared/src/base/TimerCoordinationService');
      const coordinator = timerModule.getTimerCoordinator();
      const svcSpy = jest.spyOn(coordinator as any, 'clearServiceTimers').mockImplementation(() => {});
      const allSpy = jest.spyOn(coordinator as any, 'clearAllTimers').mockImplementation(() => {});

      ImplementationProgressTracker = require('../ImplementationProgressTracker').ImplementationProgressTracker;
      svc = new ImplementationProgressTracker();
      await svc.initialize();
      await svc.shutdown();

      if (svcSpy.mock.calls.length > 0) {
        expect(svcSpy).toHaveBeenCalledWith('ImplementationProgressTracker');
      } else {
        expect(allSpy).toHaveBeenCalled();
      }
    });
  });

  test('bounded data structures: progress data/history/dependency graph enforce limits during tracking', async () => {
    const { ImplementationProgressTracker } = await import('../ImplementationProgressTracker');
    const svc = new ImplementationProgressTracker();
    await svc.initialize();

    // Push towards boundaries to trigger enforcement
    const maxData = (svc as any)._maxProgressData as number;
    const maxHistory = (svc as any)._maxProgressHistory as number;
    const maxDeps = (svc as any)._maxDependencyGraph as number;

    const total = Math.ceil(Math.max(maxData, maxDeps) * 1.05); // 105% to trigger cleanup
    for (let i = 0; i < total; i++) {
      const componentId = `comp-${i}`;
      await svc.track({
        componentId,
        status: 'active',
        context: {},
        payload: {
          implementation: {
            milestone: 'M0',
            phase: 'foundation'
          }
        }
      } as any).catch(() => {});
    }

    const progressDataSize = (svc as any)._progressData.size as number;
    const dependencySize = (svc as any)._dependencyGraph.size as number;
    expect(progressDataSize).toBeLessThanOrEqual(maxData);
    expect(dependencySize).toBeLessThanOrEqual(maxDeps);

    const historyMap = (svc as any)._progressHistory as Map<string, any[]>;
    expect(historyMap.size).toBeLessThanOrEqual(maxHistory);

    await svc.shutdown();
  });
});

