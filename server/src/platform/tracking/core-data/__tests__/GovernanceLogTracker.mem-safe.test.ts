import { GovernanceLogTracker } from '../GovernanceLogTracker';
import { getTimerCoordinator } from '../../../../../../shared/src/base/TimerCoordinationService';

const coordinator: any = getTimerCoordinator();

describe('GovernanceLogTracker MEM-SAFE-002 compliance', () => {
  beforeEach(() => {
    jest.spyOn(coordinator as any, 'clearServiceTimers').mockImplementation(() => {});
    jest.spyOn(coordinator as any, 'clearAllTimers').mockImplementation(() => {});
    jest.spyOn(coordinator as any, 'createCoordinatedInterval').mockImplementation((...args: any[]) => `${args[2]}:${args[3]}`);
    jest.spyOn(coordinator as any, 'removeCoordinatedTimer').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('doShutdown clears timers via serviceId with fallback to clearAllTimers', async () => {
    const tracker = new GovernanceLogTracker({} as any);
    await tracker.initialize();

    // Simulate health monitoring timer
    (coordinator as any).createCoordinatedInterval(() => {}, 1000, 'GovernanceLogTracker', 'health-monitoring');

    await tracker.shutdown();

    const svcSpy = (coordinator as any).clearServiceTimers as jest.Mock;
    if (svcSpy && svcSpy.mock) {
      expect(svcSpy).toHaveBeenCalledWith('GovernanceLogTracker');
    } else {
      expect((coordinator as any).clearAllTimers).toHaveBeenCalled();
    }
  });

  test('lifecycle initialize → operations → shutdown', async () => {
    const tracker = new GovernanceLogTracker({} as any);
    await tracker.initialize();

    // Perform a minimal operation: log a governance event via public API
    await tracker.logGovernanceEvent(
      'governance_update',
      'info',
      'GovernanceLogTrackerTest',
      'Test governance update',
      {
        milestone: 'M0',
        category: 'governance',
        documents: [],
        affectedComponents: ['GovernanceLogTrackerTest'],
        metadata: {}
      } as any
    );

    await expect(tracker.shutdown()).resolves.toBeUndefined();
  });

  test('fallback path: when clearServiceTimers is undefined, clearAllTimers is used', async () => {
    const tracker = new GovernanceLogTracker({} as any);
    await tracker.initialize();

    // Remove serviceId method to force fallback
    (coordinator as any).clearServiceTimers = undefined;

    await tracker.shutdown();

    expect((coordinator as any).clearAllTimers).toHaveBeenCalled();
  });
});

