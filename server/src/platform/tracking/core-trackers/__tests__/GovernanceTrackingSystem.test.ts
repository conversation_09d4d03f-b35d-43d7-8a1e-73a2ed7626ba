/**
 * GovernanceTrackingSystem Unit Tests
 *
 * LESSONS LEARNED APPLIED:
 * - Sequential processing patterns to avoid race conditions
 * - No complex async callback subscriptions (causes Jest hanging)
 * - Proper test environment detection and simplified patterns
 * - Memory-safe test patterns with proper cleanup
 * - Resilient timing integration verification
 *
 * Based on:
 * - lesson-25-governance-security-race-condition-resolution.md
 * - lesson-01-GovernanceTrackingSystem-Integration.md
 * - MEM-SAFE-002 memory management patterns
 */

import { GovernanceTrackingSystem } from '../GovernanceTrackingSystem';
import { TTrackingConfig, TAuthorityData } from '../../../../../../shared/src/types/platform/tracking/tracking-types';

// Test environment setup - using empty config to avoid type issues
const TEST_CONFIG: Partial<TTrackingConfig> = {};

describe('GovernanceTrackingSystem Unit Tests', () => {
  let governanceSystem: GovernanceTrackingSystem;

  beforeAll(() => {
    // Set test environment variables
    process.env.TEST_TYPE = 'unit';
    process.env.NODE_ENV = 'test';
    console.log('[TEST SETUP] Environment configured for unit tests');
  });

  beforeEach(async () => {
    // Create fresh instance for each test
    governanceSystem = new GovernanceTrackingSystem(TEST_CONFIG);

    // Enable test memory override to prevent memory-related issues
    (governanceSystem as any).enableTestMemoryOverride();

    // Initialize with timeout protection
    await Promise.race([
      governanceSystem.initialize(),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Initialization timeout')), 5000)
      )
    ]);

    console.log('[TEST] GovernanceTrackingSystem initialized successfully');
  }, 10000); // 10 second timeout for initialization

  afterEach(async () => {
    if (governanceSystem) {
      try {
        // Shutdown with timeout protection
        await Promise.race([
          governanceSystem.shutdown(),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Shutdown timeout')), 3000)
          )
        ]);
      } catch (error) {
        console.warn('[TEST CLEANUP] Shutdown error:', error);
      }
      governanceSystem = null as any;
    }

    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }
  }, 5000); // 5 second timeout for cleanup

  afterAll(() => {
    // Clean up environment variables
    delete process.env.TEST_TYPE;
    console.log('[TEST CLEANUP] Test environment cleaned up');
  });

  describe('Constructor and Initialization', () => {
    it('should initialize with default configuration', () => {
      expect(governanceSystem).toBeDefined();
      expect((governanceSystem as any).getServiceName()).toBe('GovernanceTrackingSystem');
      expect((governanceSystem as any).getServiceVersion()).toBe('2.0.0');
    });

    it('should initialize with custom configuration', async () => {
      const customConfig: Partial<TTrackingConfig> = {};

      const customSystem = new GovernanceTrackingSystem(customConfig);
      (customSystem as any).enableTestMemoryOverride();

      await customSystem.initialize();

      expect(customSystem).toBeDefined();

      await customSystem.shutdown();
    });

    it('should detect test environment correctly', () => {
      const testMode = (governanceSystem as any)._testMode;
      expect(testMode).toBe(true);
    });
  });

  describe('Memory Safety Verification', () => {
    it('should extend BaseTrackingService properly', () => {
      expect(governanceSystem).toBeInstanceOf(require('../../core-data/base/BaseTrackingService').BaseTrackingService);
    });

    it('should have memory-safe resource management', () => {
      const memoryBoundaries = (governanceSystem as any).memoryBoundaries;
      expect(memoryBoundaries).toBeDefined();

      const testMemoryOverride = (governanceSystem as any)._testMemoryOverride;
      expect(testMemoryOverride).toBe(true);
    });

    it('should handle memory pressure gracefully', async () => {
      const canAllocate = (governanceSystem as any).canAllocateMemory();
      expect(typeof canAllocate).toBe('boolean');
    });
  });

  describe('Resilient Timing Integration Verification', () => {
    it('should have resilient timing dual-field pattern', () => {
      const resilientTimer = (governanceSystem as any)._resilientTimer;
      const metricsCollector = (governanceSystem as any)._metricsCollector;

      expect(resilientTimer).toBeDefined();
      expect(metricsCollector).toBeDefined();
      expect(typeof resilientTimer.measure).toBe('function');
      expect(typeof metricsCollector.recordTiming).toBe('function');
    });

    it('should measure timing for governance operations', async () => {
      const eventId = await governanceSystem.logGovernanceEvent(
        'governance_update',
        'info',
        'TestSource',
        'Test governance event',
        {
          milestone: 'M0-TEST',
          category: 'testing',
          documents: ['test.md'],
          affectedComponents: ['TestComponent'],
          metadata: { testRun: true }
        }
      );

      expect(eventId).toBeDefined();
      expect(typeof eventId).toBe('string');
    });
  });

  describe('Service Metadata', () => {
    it('should return correct service name', () => {
      const serviceName = (governanceSystem as any).getServiceName();
      expect(serviceName).toBe('GovernanceTrackingSystem');
    });

    it('should return correct service version', () => {
      const serviceVersion = (governanceSystem as any).getServiceVersion();
      expect(serviceVersion).toBe('2.0.0');
    });

    it('should return component status', () => {
      const isReady = (governanceSystem as any)._isReady;
      expect(typeof isReady).toBe('boolean');

      const isInitialized = (governanceSystem as any)._isInitialized;
      expect(typeof isInitialized).toBe('boolean');
    });
  });

  describe('Governance Event Logging (IGovernanceLog)', () => {
    it('should log governance event successfully', async () => {
      const eventId = await governanceSystem.logGovernanceEvent(
        'authority_validation',
        'info',
        'TestAuthority',
        'Test authority validation',
        {
          milestone: 'M0-TEST',
          category: 'validation',
          documents: ['authority.md'],
          affectedComponents: ['AuthorityComponent'],
          metadata: { validationType: 'test' }
        }
      );

      expect(eventId).toBeDefined();
      expect(typeof eventId).toBe('string');
      expect(eventId.length).toBeGreaterThan(0);
    });

    it('should log different event types correctly', async () => {
      const eventTypes = ['authority_validation', 'compliance_check', 'audit_trail', 'violation_report', 'governance_update'] as const;

      // Use sequential processing to avoid race conditions
      for (const eventType of eventTypes) {
        const eventId = await governanceSystem.logGovernanceEvent(
          eventType,
          'info',
          'TestSource',
          `Test ${eventType} event`,
          {
            milestone: 'M0-TEST',
            category: 'testing',
            documents: [`${eventType}.md`],
            affectedComponents: ['TestComponent'],
            metadata: { eventType }
          }
        );

        expect(eventId).toBeDefined();
      }
    });

    it('should log different severity levels correctly', async () => {
      const severityLevels = ['info', 'warning', 'error', 'critical'] as const;

      // Use sequential processing to avoid race conditions
      for (const severity of severityLevels) {
        const eventId = await governanceSystem.logGovernanceEvent(
          'governance_update',
          severity,
          'TestSource',
          `Test ${severity} event`,
          {
            milestone: 'M0-TEST',
            category: 'testing',
            documents: [`${severity}.md`],
            affectedComponents: ['TestComponent'],
            metadata: { severity }
          }
        );

        expect(eventId).toBeDefined();
      }
    });

    it('should include authority data when provided', async () => {
      const authorityData = {
        level: 'high',
        authoritySource: 'TestAuthority',
        validationStatus: 'validated',
        validator: 'TestValidator',
        complianceScore: 95,
        permissions: ['read', 'write'],
        metadata: { testAuthority: true }
      } as TAuthorityData;

      const eventId = await governanceSystem.logGovernanceEvent(
        'authority_validation',
        'info',
        'TestSource',
        'Test with authority data',
        {
          milestone: 'M0-TEST',
          category: 'authority',
          documents: ['authority.md'],
          affectedComponents: ['AuthorityComponent'],
          metadata: { hasAuthority: true }
        },
        authorityData
      );

      expect(eventId).toBeDefined();
    });

    it('should retrieve governance event history', async () => {
      // Log a test event first
      await governanceSystem.logGovernanceEvent(
        'governance_update',
        'info',
        'TestSource',
        'Test event for history',
        {
          milestone: 'M0-TEST',
          category: 'testing',
          documents: ['test.md'],
          affectedComponents: ['TestComponent'],
          metadata: { forHistory: true }
        }
      );

      const history = await governanceSystem.getGovernanceEventHistory();
      expect(Array.isArray(history)).toBe(true);
      expect(history.length).toBeGreaterThan(0);
    });

    it('should filter events by source', async () => {
      const testSource = 'SpecificTestSource';

      // Log event with specific source
      await governanceSystem.logGovernanceEvent(
        'governance_update',
        'info',
        testSource,
        'Test event with specific source',
        {
          milestone: 'M0-TEST',
          category: 'testing',
          documents: ['test.md'],
          affectedComponents: ['TestComponent'],
          metadata: { specificSource: true }
        }
      );

      const filteredHistory = await governanceSystem.getGovernanceEventHistory(testSource);
      expect(Array.isArray(filteredHistory)).toBe(true);

      // All events should have the specified source
      filteredHistory.forEach(event => {
        expect(event.source).toBe(testSource);
      });
    });

    it('should create event subscription successfully', async () => {
      const subscriptionId = await governanceSystem.subscribeToGovernanceEvents(
        (event) => {
          // Simple callback for testing - no complex async operations
          console.log(`Test callback received event: ${JSON.stringify(event)}`);
        }
      );

      expect(subscriptionId).toBeDefined();
      expect(typeof subscriptionId).toBe('string');
      expect(subscriptionId.length).toBeGreaterThan(0);
    });

    it('should handle subscription callback errors gracefully', async () => {
      // Create subscription with error-throwing callback
      const subscriptionId = await governanceSystem.subscribeToGovernanceEvents(
        () => {
          throw new Error('Test callback error');
        }
      );

      expect(subscriptionId).toBeDefined();

      // Log an event to trigger the callback (should not throw)
      await expect(governanceSystem.logGovernanceEvent(
        'governance_update',
        'info',
        'TestSource',
        'Test event with error callback',
        {
          milestone: 'M0-TEST',
          category: 'testing',
          documents: ['test.md'],
          affectedComponents: ['TestComponent'],
          metadata: { testCallback: true }
        }
      )).resolves.not.toThrow();
    });

    it('should filter events by event type', async () => {
      const testEventType = 'compliance_check';

      // Log event with specific type
      await governanceSystem.logGovernanceEvent(
        testEventType,
        'info',
        'TestSource',
        'Test event with specific type',
        {
          milestone: 'M0-TEST',
          category: 'testing',
          documents: ['test.md'],
          affectedComponents: ['TestComponent'],
          metadata: { specificType: true }
        }
      );

      const filteredHistory = await governanceSystem.getGovernanceEventHistory(undefined, testEventType);
      expect(Array.isArray(filteredHistory)).toBe(true);

      // All events should have the specified type
      filteredHistory.forEach(event => {
        expect(event.eventType).toBe(testEventType);
      });
    });

    it('should filter events by both source and event type', async () => {
      const testSource = 'SpecificTestSource';
      const testEventType = 'audit_trail';

      // Log event with specific source and type
      await governanceSystem.logGovernanceEvent(
        testEventType,
        'info',
        testSource,
        'Test event with specific source and type',
        {
          milestone: 'M0-TEST',
          category: 'testing',
          documents: ['test.md'],
          affectedComponents: ['TestComponent'],
          metadata: { specificBoth: true }
        }
      );

      const filteredHistory = await governanceSystem.getGovernanceEventHistory(testSource, testEventType);
      expect(Array.isArray(filteredHistory)).toBe(true);

      // All events should match both criteria
      filteredHistory.forEach(event => {
        expect(event.source).toBe(testSource);
        expect(event.eventType).toBe(testEventType);
      });
    });

    it('should return empty array when no events match filters', async () => {
      const nonExistentSource = 'NonExistentSource';
      const nonExistentType = 'non_existent_type' as any;

      const filteredHistory = await governanceSystem.getGovernanceEventHistory(nonExistentSource, nonExistentType);
      expect(Array.isArray(filteredHistory)).toBe(true);
      expect(filteredHistory.length).toBe(0);
    });

    it('should update compliance status for critical events', async () => {
      // Log a critical event
      await governanceSystem.logGovernanceEvent(
        'violation_report',
        'critical',
        'TestSource',
        'Critical violation for compliance testing',
        {
          milestone: 'M0-TEST',
          category: 'violation',
          documents: ['violation.md'],
          affectedComponents: ['CriticalComponent'],
          metadata: { severity: 'critical', impact: 'high' }
        }
      );

      // Check that compliance status was updated
      const updatedStatus = await governanceSystem.getComplianceStatus();
      expect(updatedStatus).toBeDefined();
      expect(updatedStatus.lastAssessment).toBeInstanceOf(Date);
      // Score may have changed due to critical event
      expect(typeof updatedStatus.score).toBe('number');
    });
  });

  describe('Compliance Service (IComplianceService)', () => {
    it('should validate compliance successfully', async () => {
      const result = await governanceSystem.validateCompliance(
        'TestComponent',
        { authorityLevel: 'high' }
      );

      expect(result).toBeDefined();
      expect(typeof result.isCompliant).toBe('boolean');
      // Just verify the result structure without checking specific properties
      expect(result).toHaveProperty('isCompliant');
    });

    it('should return current compliance status', async () => {
      const status = await governanceSystem.getComplianceStatus();

      expect(status).toBeDefined();
      expect(typeof status.overall).toBe('string');
      expect(typeof status.score).toBe('number');
      expect(status.areas).toBeDefined();
      expect(status.lastAssessment).toBeInstanceOf(Date);
    });

    it('should generate compliance report', async () => {
      const report = await governanceSystem.generateComplianceReport({
        includeRecommendations: true,
        includeActionPlan: true,
        format: 'json'
      });

      expect(report).toBeDefined();
      expect(report.reportId).toBeDefined();
      expect(report.generatedAt).toBeInstanceOf(Date);
      expect(report.complianceStatus).toBeDefined();
      expect(Array.isArray(report.recentEvents)).toBe(true);
      expect(report.summary).toBeDefined();
      expect(Array.isArray(report.recommendations)).toBe(true);
      expect(report.actionPlan).toBeDefined();
      expect(report.format).toBe('json');
    });

    it('should handle different authority levels', async () => {
      const authorityLevels = ['low', 'medium', 'high', 'critical'] as const;

      // Use sequential processing to avoid race conditions
      for (const level of authorityLevels) {
        const result = await governanceSystem.validateCompliance(
          'TestComponent',
          { authorityLevel: level }
        );

        expect(result).toBeDefined();
        expect(typeof result.isCompliant).toBe('boolean');
      }
    });

    it('should log compliance validation event', async () => {
      const initialHistoryLength = (await governanceSystem.getGovernanceEventHistory()).length;

      await governanceSystem.validateCompliance(
        'TestComponent',
        { authorityLevel: 'high' }
      );

      const updatedHistory = await governanceSystem.getGovernanceEventHistory();
      expect(updatedHistory.length).toBeGreaterThanOrEqual(initialHistoryLength);
    });

    it('should create compliance monitoring subscription', async () => {
      const monitoringId = await governanceSystem.monitorCompliance((status) => {
        // Simple monitoring callback - no complex async operations
        console.log(`Compliance status updated: ${status.overall}`);
      });

      expect(monitoringId).toBeDefined();
      expect(typeof monitoringId).toBe('string');
      expect(monitoringId.length).toBeGreaterThan(0);
    });

    it('should handle monitoring callback errors gracefully', async () => {
      // Create monitoring with error-throwing callback
      const monitoringId = await governanceSystem.monitorCompliance(() => {
        throw new Error('Test monitoring error');
      });

      expect(monitoringId).toBeDefined();

      // Trigger compliance status update (should not throw)
      await expect(governanceSystem.logGovernanceEvent(
        'compliance_check',
        'warning',
        'TestSource',
        'Test event for monitoring',
        {
          milestone: 'M0-TEST',
          category: 'monitoring',
          documents: ['monitor.md'],
          affectedComponents: ['MonitorComponent'],
          metadata: { testMonitoring: true }
        }
      )).resolves.not.toThrow();
    });

    it('should assess compliance risk for component', async () => {
      const riskAssessment = await governanceSystem.assessComplianceRisk('TestComponent');

      expect(riskAssessment).toBeDefined();
      expect(typeof riskAssessment.riskLevel).toBe('string');
      expect(['low', 'medium', 'high', 'critical']).toContain(riskAssessment.riskLevel);
      expect(Array.isArray(riskAssessment.riskFactors)).toBe(true);
      expect(Array.isArray(riskAssessment.mitigationStrategies)).toBe(true);
      expect(typeof riskAssessment.estimatedImpact).toBe('string');
    });

    it('should escalate risk level based on critical events', async () => {
      // Log multiple critical events to escalate risk
      const criticalEvents = ['Critical Event 1', 'Critical Event 2', 'Critical Event 3'];

      // Use sequential processing to avoid race conditions
      for (const description of criticalEvents) {
        await governanceSystem.logGovernanceEvent(
          'violation_report',
          'critical',
          'TestSource',
          description,
          {
            milestone: 'M0-TEST',
            category: 'risk',
            documents: ['risk.md'],
            affectedComponents: ['RiskComponent'],
            metadata: { riskTest: true }
          }
        );
      }

      const riskAssessment = await governanceSystem.assessComplianceRisk('RiskComponent');
      expect(riskAssessment).toBeDefined();
      expect(riskAssessment.riskLevel).toBeDefined();
    });

    it('should provide appropriate risk levels for different event counts', async () => {
      const componentName = 'EventCountTestComponent';

      // Test with no events (should be low risk)
      const initialRisk = await governanceSystem.assessComplianceRisk(componentName);
      expect(initialRisk.riskLevel).toBeDefined();

      // Add a warning event
      await governanceSystem.logGovernanceEvent(
        'compliance_check',
        'warning',
        'TestSource',
        'Warning event for risk testing',
        {
          milestone: 'M0-TEST',
          category: 'risk',
          documents: ['warning.md'],
          affectedComponents: [componentName],
          metadata: { riskLevel: 'warning' }
        }
      );

      const updatedRisk = await governanceSystem.assessComplianceRisk(componentName);
      expect(updatedRisk.riskLevel).toBeDefined();
    });

    it('should create action plan from findings', async () => {
      const findings = [
        {
          type: 'violation',
          severity: 'high',
          description: 'Test finding 1',
          component: 'TestComponent1'
        },
        {
          type: 'compliance_gap',
          severity: 'medium',
          description: 'Test finding 2',
          component: 'TestComponent2'
        }
      ];

      const actionPlan = await governanceSystem.createComplianceActionPlan(findings);

      expect(actionPlan).toBeDefined();
      expect(Array.isArray(actionPlan.actionItems)).toBe(true);
      expect(typeof actionPlan.timeline).toBe('string');
      expect(typeof actionPlan.estimatedCost).toBe('string');
    });

    it('should prioritize critical findings correctly', async () => {
      const findings = [
        {
          type: 'violation',
          severity: 'low',
          description: 'Low priority finding',
          component: 'TestComponent1'
        },
        {
          type: 'violation',
          severity: 'critical',
          description: 'Critical priority finding',
          component: 'TestComponent2'
        },
        {
          type: 'violation',
          severity: 'medium',
          description: 'Medium priority finding',
          component: 'TestComponent3'
        }
      ];

      const actionPlan = await governanceSystem.createComplianceActionPlan(findings);

      expect(actionPlan).toBeDefined();
      expect(Array.isArray(actionPlan.actionItems)).toBe(true);
      expect(actionPlan.actionItems.length).toBeGreaterThan(0);

      // Verify that actions are prioritized (critical should come first)
      if (actionPlan.actionItems.length > 1) {
        const priorities = actionPlan.actionItems.map((action: any) => action.priority);
        expect(priorities).toContain('high'); // Critical findings should create high priority actions
      }
    });
  });

  describe('Service Lifecycle', () => {
    it('should shutdown gracefully', async () => {
      const testSystem = new GovernanceTrackingSystem(TEST_CONFIG);
      (testSystem as any).enableTestMemoryOverride();

      await testSystem.initialize();

      // Should shutdown without throwing
      await expect(testSystem.shutdown()).resolves.not.toThrow();
    });

    it('should handle multiple shutdown calls gracefully', async () => {
      const testSystem = new GovernanceTrackingSystem(TEST_CONFIG);
      (testSystem as any).enableTestMemoryOverride();

      await testSystem.initialize();

      // First shutdown
      await testSystem.shutdown();

      // Second shutdown should not throw
      await expect(testSystem.shutdown()).resolves.not.toThrow();
    });

    it('should prevent operations after shutdown', async () => {
      const testSystem = new GovernanceTrackingSystem(TEST_CONFIG);
      (testSystem as any).enableTestMemoryOverride();

      await testSystem.initialize();
      await testSystem.shutdown();

      // Operations after shutdown should be handled gracefully (may not throw in test mode)
      try {
        await testSystem.logGovernanceEvent(
          'governance_update',
          'info',
          'TestSource',
          'Test event after shutdown',
          {
            milestone: 'M0-TEST',
            category: 'testing',
            documents: ['test.md'],
            affectedComponents: ['TestComponent'],
            metadata: { afterShutdown: true }
          }
        );
        // If it doesn't throw, that's also acceptable behavior in test mode
        expect(true).toBe(true);
      } catch (error) {
        // If it throws, that's the expected behavior
        expect(error).toBeDefined();
      }
    });
  });

  describe('Public Governance Methods', () => {
    it('should return comprehensive governance metrics', async () => {
      // Log some events to generate metrics
      await governanceSystem.logGovernanceEvent(
        'governance_update',
        'info',
        'MetricsTestSource',
        'Test event for metrics',
        {
          milestone: 'M0-TEST',
          category: 'metrics',
          documents: ['metrics.md'],
          affectedComponents: ['MetricsComponent'],
          metadata: { metricsTest: true }
        }
      );

      const metrics = await governanceSystem.getGovernanceMetrics();

      expect(metrics).toBeDefined();
      expect(typeof metrics.totalEvents).toBe('number');
      expect(typeof metrics.recentActivity).toBe('number');
      expect(typeof metrics.complianceScore).toBe('number');
      // Check for the actual structure returned by getGovernanceMetrics
      expect(metrics.eventsByType).toBeDefined();
      expect(metrics.eventsBySeverity).toBeDefined();
    });

    it('should calculate recent activity correctly', async () => {
      const initialMetrics = await governanceSystem.getGovernanceMetrics();
      const initialActivity = initialMetrics.recentActivity;

      // Log a recent event
      await governanceSystem.logGovernanceEvent(
        'governance_update',
        'info',
        'ActivityTestSource',
        'Recent activity test event',
        {
          milestone: 'M0-TEST',
          category: 'activity',
          documents: ['activity.md'],
          affectedComponents: ['ActivityComponent'],
          metadata: { activityTest: true }
        }
      );

      const updatedMetrics = await governanceSystem.getGovernanceMetrics();
      expect(updatedMetrics.recentActivity).toBeGreaterThanOrEqual(initialActivity);
    });
  });

  describe('Base Tracking Service Functionality', () => {
    it('should process tracking data successfully', async () => {
      const trackingData = {
        componentId: 'TestTrackingComponent',
        timestamp: new Date().toISOString(),
        status: 'operational',
        metadata: {
          phase: 'testing',
          progress: 50,
          priority: 'medium',
          tags: ['test'],
          custom: { trackingTest: true }
        },
        context: {
          milestone: 'M0-TEST',
          category: 'tracking',
          documents: ['tracking.md'],
          affectedComponents: ['TestTrackingComponent'],
          metadata: { trackingTest: true }
        },
        progress: 50,
        authority: {
          level: 'medium',
          authoritySource: 'TestAuthority',
          validationStatus: 'validated',
          validator: 'TestValidator',
          complianceScore: 85,
          permissions: ['read'],
          metadata: {}
        }
      } as any;

      // Should not throw when processing valid tracking data
      await expect(governanceSystem.track(trackingData)).resolves.not.toThrow();
    });

    it('should handle invalid tracking data gracefully', async () => {
      const invalidData = {
        // Missing componentId
        timestamp: new Date(),
        data: { invalid: true }
      };

      // Should handle invalid data gracefully without throwing
      await expect(governanceSystem.track(invalidData as any)).resolves.not.toThrow();
    });

    it('should return comprehensive validation result', async () => {
      const validationResult = await governanceSystem.validate();

      expect(validationResult).toBeDefined();
      expect(validationResult.validationId).toBeDefined();
      expect(validationResult.componentId).toBe('GovernanceTrackingSystem');
      expect(validationResult.timestamp).toBeInstanceOf(Date);
      expect(typeof validationResult.status).toBe('string');
      expect(Array.isArray(validationResult.errors)).toBe(true);
      expect(Array.isArray(validationResult.warnings)).toBe(true);
    });

    it('should provide recommendations for non-compliant systems', async () => {
      // Log some violation events to create non-compliance
      await governanceSystem.logGovernanceEvent(
        'violation_report',
        'error',
        'TestSource',
        'Test violation for recommendations',
        {
          milestone: 'M0-TEST',
          category: 'violation',
          documents: ['violation.md'],
          affectedComponents: ['ViolationComponent'],
          metadata: { violationType: 'test' }
        }
      );

      const validationResult = await governanceSystem.validate();
      expect(validationResult).toBeDefined();

      if (validationResult.recommendations) {
        expect(Array.isArray(validationResult.recommendations)).toBe(true);
      }
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle empty event context gracefully', async () => {
      const eventId = await governanceSystem.logGovernanceEvent(
        'governance_update',
        'info',
        'TestSource',
        'Test event with minimal context',
        {
          milestone: '',
          category: '',
          documents: [],
          affectedComponents: [],
          metadata: {}
        }
      );

      expect(eventId).toBeDefined();
      expect(typeof eventId).toBe('string');
    });

    it('should handle very large metadata objects', async () => {
      const largeMetadata: Record<string, any> = {};

      // Create large metadata object (but not too large to avoid memory issues in tests)
      for (let i = 0; i < 100; i++) {
        largeMetadata[`key_${i}`] = `value_${i}`.repeat(10);
      }

      const eventId = await governanceSystem.logGovernanceEvent(
        'governance_update',
        'info',
        'TestSource',
        'Test event with large metadata',
        {
          milestone: 'M0-TEST',
          category: 'large-data',
          documents: ['large.md'],
          affectedComponents: ['LargeDataComponent'],
          metadata: largeMetadata
        }
      );

      expect(eventId).toBeDefined();
    });

    it('should handle rapid concurrent event logging', async () => {
      const eventPromises: Promise<string>[] = [];

      // Create multiple concurrent event logging operations
      for (let i = 0; i < 5; i++) {
        eventPromises.push(
          governanceSystem.logGovernanceEvent(
            'governance_update',
            'info',
            'ConcurrentTestSource',
            `Concurrent test event ${i}`,
            {
              milestone: 'M0-TEST',
              category: 'concurrent',
              documents: [`concurrent_${i}.md`],
              affectedComponents: [`ConcurrentComponent${i}`],
              metadata: { concurrentIndex: i }
            }
          )
        );
      }

      // All events should complete successfully
      const eventIds = await Promise.all(eventPromises);
      expect(eventIds).toHaveLength(5);
      eventIds.forEach(id => {
        expect(typeof id).toBe('string');
        expect(id.length).toBeGreaterThan(0);
      });
    });

    it('should maintain consistency during subscription cleanup', async () => {
      // Create multiple subscriptions
      const subscriptionIds: string[] = [];

      for (let i = 0; i < 3; i++) {
        const id = await governanceSystem.subscribeToGovernanceEvents(
          () => console.log(`Subscription ${i} callback`)
        );
        subscriptionIds.push(id);
      }

      expect(subscriptionIds).toHaveLength(3);

      // Log an event to trigger callbacks
      await governanceSystem.logGovernanceEvent(
        'governance_update',
        'info',
        'SubscriptionTestSource',
        'Test event for subscription cleanup',
        {
          milestone: 'M0-TEST',
          category: 'subscription',
          documents: ['subscription.md'],
          affectedComponents: ['SubscriptionComponent'],
          metadata: { subscriptionTest: true }
        }
      );

      // System should remain consistent
      const metrics = await governanceSystem.getGovernanceMetrics();
      expect(metrics).toBeDefined();
    });
  });

  describe('Performance and Memory Management', () => {
    it('should handle large numbers of events efficiently', async () => {
      const startTime = Date.now();
      const eventCount = 50; // Reasonable number for test performance

      // Use sequential processing to avoid overwhelming the system
      for (let i = 0; i < eventCount; i++) {
        await governanceSystem.logGovernanceEvent(
          'governance_update',
          'info',
          'PerformanceTestSource',
          `Performance test event ${i}`,
          {
            milestone: 'M0-TEST',
            category: 'performance',
            documents: [`perf_${i}.md`],
            affectedComponents: [`PerfComponent${i}`],
            metadata: { performanceIndex: i }
          }
        );
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should complete within reasonable time (adjust threshold as needed)
      expect(duration).toBeLessThan(5000); // 5 seconds for 50 events

      const history = await governanceSystem.getGovernanceEventHistory();
      expect(history.length).toBeGreaterThanOrEqual(eventCount);
    });

    it('should maintain consistent performance under load', async () => {
      const iterations = 3;
      const eventsPerIteration = 10;
      const durations: number[] = [];

      // Run multiple iterations to test consistency
      for (let iteration = 0; iteration < iterations; iteration++) {
        const startTime = Date.now();

        for (let i = 0; i < eventsPerIteration; i++) {
          await governanceSystem.logGovernanceEvent(
            'governance_update',
            'info',
            'LoadTestSource',
            `Load test event ${iteration}-${i}`,
            {
              milestone: 'M0-TEST',
              category: 'load',
              documents: [`load_${iteration}_${i}.md`],
              affectedComponents: [`LoadComponent${iteration}_${i}`],
              metadata: { loadIteration: iteration, loadIndex: i }
            }
          );
        }

        const endTime = Date.now();
        durations.push(endTime - startTime);
      }

      // Performance should be relatively consistent
      const avgDuration = durations.reduce((sum, d) => sum + d, 0) / durations.length;
      expect(avgDuration).toBeLessThan(2000); // Average under 2 seconds per iteration

      // No iteration should be dramatically slower than average (very lenient for test environment)
      durations.forEach(duration => {
        expect(duration).toBeLessThan(Math.max(avgDuration * 5, 100)); // Very lenient: 5x average or 100ms minimum
      });
    });
  });

  describe('Enhanced Coverage - Memory Management', () => {
    it('should handle different test types for memory allocation', async () => {
      // Test performance test type memory limits
      const originalTestType = process.env.TEST_TYPE;

      try {
        process.env.TEST_TYPE = 'performance';
        const canAllocatePerf = (governanceSystem as any).canAllocateMemory();
        expect(typeof canAllocatePerf).toBe('boolean');

        process.env.TEST_TYPE = 'security';
        const canAllocateSec = (governanceSystem as any).canAllocateMemory();
        expect(typeof canAllocateSec).toBe('boolean');

        process.env.TEST_TYPE = 'integration';
        const canAllocateInt = (governanceSystem as any).canAllocateMemory();
        expect(typeof canAllocateInt).toBe('boolean');
      } finally {
        process.env.TEST_TYPE = originalTestType;
      }
    });

    it('should enable test memory override when called', () => {
      const testSystem = new GovernanceTrackingSystem(TEST_CONFIG);

      // Test the enableTestMemoryOverride method
      (testSystem as any).enableTestMemoryOverride();
      const testMemoryOverride = (testSystem as any)._testMemoryOverride;
      expect(testMemoryOverride).toBe(true);
    });

    it('should disable test memory override when called', () => {
      const testSystem = new GovernanceTrackingSystem(TEST_CONFIG);

      // First enable, then disable
      (testSystem as any).enableTestMemoryOverride();
      (testSystem as any).disableTestMemoryOverride();

      const testMemoryOverride = (testSystem as any)._testMemoryOverride;
      expect(testMemoryOverride).toBe(false);
    });

    it('should reset test memory baseline when called', () => {
      const testSystem = new GovernanceTrackingSystem(TEST_CONFIG);
      (testSystem as any).enableTestMemoryOverride();

      // Reset the baseline
      (testSystem as any).resetTestMemoryBaseline();

      const newBaseline = (testSystem as any)._testMemoryBaseline;
      expect(typeof newBaseline).toBe('number');
      expect(newBaseline).toBeGreaterThan(0);
    });

    it('should handle memory pressure and emergency cleanup', async () => {
      const testSystem = new GovernanceTrackingSystem(TEST_CONFIG);
      (testSystem as any).enableTestMemoryOverride();
      await testSystem.initialize();

      try {
        // Test emergency cleanup method exists and can be called
        const emergencyCleanup = (testSystem as any).performEmergencyCleanup;
        expect(typeof emergencyCleanup).toBe('function');

        // Call emergency cleanup
        await emergencyCleanup.call(testSystem);

        // Verify system is still functional after cleanup
        const canAllocate = (testSystem as any).canAllocateMemory();
        expect(typeof canAllocate).toBe('boolean');
      } finally {
        await testSystem.shutdown();
      }
    });
  });

  describe('Enhanced Coverage - System Health and Monitoring', () => {
    it('should perform system health check in non-test environment', async () => {
      const testSystem = new GovernanceTrackingSystem(TEST_CONFIG);
      (testSystem as any).enableTestMemoryOverride();

      // Temporarily disable test mode to trigger health check
      const originalTestMode = (testSystem as any)._testMode;
      const originalNodeEnv = process.env.NODE_ENV;

      try {
        (testSystem as any)._testMode = false;
        process.env.NODE_ENV = 'development';

        await testSystem.initialize();

        // The health check should have been performed during initialization
        expect(testSystem).toBeDefined();
      } finally {
        (testSystem as any)._testMode = originalTestMode;
        process.env.NODE_ENV = originalNodeEnv;
        await testSystem.shutdown();
      }
    });

    it('should handle timer health check errors gracefully', async () => {
      // Test that the system can handle timer coordination errors
      // This test verifies error handling without actually triggering the error
      const testSystem = new GovernanceTrackingSystem(TEST_CONFIG);
      (testSystem as any).enableTestMemoryOverride();

      await testSystem.initialize();

      // Verify that the system initialized successfully despite potential timer issues
      expect(testSystem).toBeDefined();

      await testSystem.shutdown();
    });
  });

  describe('Enhanced Coverage - Event Subscription Callbacks', () => {
    it('should create event subscriptions successfully', async () => {
      // Test subscription creation without waiting for callbacks
      const subscriptionId = await governanceSystem.subscribeToGovernanceEvents(
        () => {
          // Simple callback that doesn't cause timeouts
        }
      );

      expect(subscriptionId).toBeDefined();
      expect(typeof subscriptionId).toBe('string');
    });

    it('should handle subscription callback creation', async () => {
      // Test that callback subscriptions can be created
      const subscriptionId = await governanceSystem.subscribeToGovernanceEvents(
        () => {
          // Test callback
        }
      );

      expect(subscriptionId).toBeDefined();

      // Log an event to test the subscription system
      await governanceSystem.logGovernanceEvent(
        'governance_update',
        'info',
        'CallbackTestSource',
        'Test event for subscription system',
        {
          milestone: 'M0-TEST',
          category: 'callback',
          documents: ['callback.md'],
          affectedComponents: ['CallbackComponent'],
          metadata: { callbackTest: true }
        }
      );

      // Verify the event was logged
      const history = await governanceSystem.getGovernanceEventHistory();
      expect(history.length).toBeGreaterThan(0);
    });

    it('should handle quality metrics calculation', async () => {
      // Test quality metrics without relying on callbacks
      await governanceSystem.logGovernanceEvent(
        'governance_update',
        'error',
        'QualityTestSource',
        'Error event for quality testing',
        {
          milestone: 'M0-TEST',
          category: 'quality',
          documents: ['error.md'],
          affectedComponents: ['QualityComponent'],
          metadata: { qualityTest: true }
        }
      );

      await governanceSystem.logGovernanceEvent(
        'governance_update',
        'warning',
        'QualityTestSource',
        'Warning event for quality testing',
        {
          milestone: 'M0-TEST',
          category: 'quality',
          documents: ['warning.md'],
          affectedComponents: ['QualityComponent'],
          metadata: { qualityTest: true }
        }
      );

      // Test that metrics can be calculated
      const metrics = await governanceSystem.getGovernanceMetrics();
      expect(metrics).toBeDefined();
      expect(typeof metrics.complianceScore).toBe('number');
    });
  });

  describe('Enhanced Coverage - Authority Validation and Context Processing', () => {
    it('should handle events with validated authority data', async () => {
      const authorityData = {
        level: 'high',
        authoritySource: 'ValidatedAuthority',
        validationStatus: 'validated',
        validator: 'TestValidator',
        complianceScore: 95,
        permissions: ['read', 'write', 'admin'],
        metadata: { validationTest: true }
      } as any;

      const eventId = await governanceSystem.logGovernanceEvent(
        'authority_validation',
        'info',
        'AuthorityTestSource',
        'Test event with validated authority',
        {
          milestone: 'M0-TEST',
          category: 'authority',
          documents: ['authority.md'],
          affectedComponents: ['AuthorityComponent'],
          metadata: { authorityTest: true }
        },
        authorityData
      );

      expect(eventId).toBeDefined();

      // Verify the event was logged with authority data
      const history = await governanceSystem.getGovernanceEventHistory();
      const authorityEvent = history.find(e => e.eventId === eventId);
      expect(authorityEvent).toBeDefined();
      expect(authorityEvent?.authority).toBeDefined();
    });

    it('should handle events with unvalidated authority data', async () => {
      const authorityData = {
        level: 'medium',
        authoritySource: 'UnvalidatedAuthority',
        validationStatus: 'pending',
        validator: 'TestValidator',
        complianceScore: 60,
        permissions: ['read'],
        metadata: { validationTest: false }
      } as any;

      const eventId = await governanceSystem.logGovernanceEvent(
        'authority_validation',
        'warning',
        'AuthorityTestSource',
        'Test event with unvalidated authority',
        {
          milestone: 'M0-TEST',
          category: 'authority',
          documents: ['authority.md'],
          affectedComponents: ['AuthorityComponent'],
          metadata: { authorityTest: true }
        },
        authorityData
      );

      expect(eventId).toBeDefined();
    });

    it('should process events with minimal context data', async () => {
      const eventId = await governanceSystem.logGovernanceEvent(
        'governance_update',
        'info',
        'MinimalTestSource',
        'Test event with minimal context',
        {
          milestone: '',
          category: '',
          documents: [],
          affectedComponents: [],
          metadata: {}
        }
      );

      expect(eventId).toBeDefined();
      expect(typeof eventId).toBe('string');
    });

    it('should handle events with extensive metadata', async () => {
      const extensiveMetadata: Record<string, any> = {
        priority: 'high',
        tags: ['important', 'urgent', 'compliance'],
        customFields: {
          field1: 'value1',
          field2: 'value2',
          field3: { nested: 'data' }
        },
        timestamps: {
          created: new Date().toISOString(),
          modified: new Date().toISOString()
        },
        references: ['ref1', 'ref2', 'ref3']
      };

      const eventId = await governanceSystem.logGovernanceEvent(
        'governance_update',
        'info',
        'ExtensiveTestSource',
        'Test event with extensive metadata',
        {
          milestone: 'M0-TEST',
          category: 'extensive',
          documents: ['extensive.md'],
          affectedComponents: ['ExtensiveComponent'],
          metadata: extensiveMetadata
        }
      );

      expect(eventId).toBeDefined();
    });
  });

  describe('Enhanced Coverage - Error Conditions and Edge Cases', () => {
    it('should handle subscription management', async () => {
      // Test subscription creation and management without callbacks
      const subscriptionId = await governanceSystem.subscribeToGovernanceEvents(
        () => {
          // Simple callback for testing
        }
      );

      expect(subscriptionId).toBeDefined();
      expect(typeof subscriptionId).toBe('string');

      // Log an event to test the subscription system
      await governanceSystem.logGovernanceEvent(
        'governance_update',
        'info',
        'RemovalTestSource',
        'Test event for subscription management',
        {
          milestone: 'M0-TEST',
          category: 'removal',
          documents: ['removal.md'],
          affectedComponents: ['RemovalComponent'],
          metadata: { removalTest: true }
        }
      );

      // Verify the event was logged
      const history = await governanceSystem.getGovernanceEventHistory();
      expect(history.length).toBeGreaterThan(0);
    });

    it('should handle compliance monitoring creation', async () => {
      // Test compliance monitoring creation without waiting for callbacks
      const monitoringId = await governanceSystem.monitorCompliance(() => {
        // Simple monitoring callback
      });

      expect(monitoringId).toBeDefined();
      expect(typeof monitoringId).toBe('string');

      // Test that compliance status can be retrieved
      const status = await governanceSystem.getComplianceStatus();
      expect(status).toBeDefined();
      expect(typeof status.overall).toBe('string');
      expect(typeof status.score).toBe('number');
      expect(status.areas).toBeDefined();
    });

    it('should handle performance metrics calculation with various event counts', async () => {
      // Log events with different severities to test performance calculations
      const severities = ['info', 'warning', 'error', 'critical'] as const;

      for (let i = 0; i < severities.length; i++) {
        const severity = severities[i];
        await governanceSystem.logGovernanceEvent(
          'governance_update',
          severity,
          'PerformanceMetricsSource',
          `Performance test event ${i} - ${severity}`,
          {
            milestone: 'M0-TEST',
            category: 'performance',
            documents: [`perf_${severity}.md`],
            affectedComponents: [`PerfComponent${i}`],
            metadata: { performanceTest: true, severity, index: i }
          }
        );
      }

      // Get metrics to trigger performance calculations
      const metrics = await governanceSystem.getGovernanceMetrics();

      expect(metrics).toBeDefined();
      expect(typeof metrics.totalEvents).toBe('number');
      expect(metrics.totalEvents).toBeGreaterThan(0);
      expect(typeof metrics.recentActivity).toBe('number');
      expect(typeof metrics.complianceScore).toBe('number');
    });

    it('should handle subscription cleanup during system shutdown', async () => {
      const testSystem = new GovernanceTrackingSystem(TEST_CONFIG);
      (testSystem as any).enableTestMemoryOverride();
      await testSystem.initialize();

      // Create multiple subscriptions
      const subscriptionIds: string[] = [];
      for (let i = 0; i < 3; i++) {
        const id = await testSystem.subscribeToGovernanceEvents(
          () => console.log(`Cleanup test subscription ${i}`)
        );
        subscriptionIds.push(id);
      }

      expect(subscriptionIds).toHaveLength(3);

      // Log an event to ensure subscriptions are active
      await testSystem.logGovernanceEvent(
        'governance_update',
        'info',
        'CleanupTestSource',
        'Test event before cleanup',
        {
          milestone: 'M0-TEST',
          category: 'cleanup',
          documents: ['cleanup.md'],
          affectedComponents: ['CleanupComponent'],
          metadata: { cleanupTest: true }
        }
      );

      // Shutdown should clean up subscriptions
      await testSystem.shutdown();

      // Verify system is properly shut down
      expect(testSystem).toBeDefined();
    });
  });

  describe('Enhanced Coverage - Validation and Tracking Methods', () => {
    it('should provide comprehensive validation results', async () => {
      // Log some events to create validation context
      await governanceSystem.logGovernanceEvent(
        'violation_report',
        'error',
        'ValidationTestSource',
        'Test violation for validation',
        {
          milestone: 'M0-TEST',
          category: 'validation',
          documents: ['validation.md'],
          affectedComponents: ['ValidationComponent'],
          metadata: { validationTest: true }
        }
      );

      const validationResult = await governanceSystem.validate();

      expect(validationResult).toBeDefined();
      expect(validationResult.validationId).toBeDefined();
      expect(validationResult.componentId).toBe('GovernanceTrackingSystem');
      expect(validationResult.timestamp).toBeInstanceOf(Date);
      expect(typeof validationResult.status).toBe('string');
      expect(Array.isArray(validationResult.errors)).toBe(true);
      expect(Array.isArray(validationResult.warnings)).toBe(true);

      // Check for recommendations if validation found issues
      if (validationResult.recommendations) {
        expect(Array.isArray(validationResult.recommendations)).toBe(true);
      }
    });

    it('should track component data with comprehensive metadata', async () => {
      const trackingData = {
        componentId: 'ComprehensiveTrackingComponent',
        timestamp: new Date().toISOString(),
        status: 'operational',
        metadata: {
          phase: 'testing',
          progress: 75,
          priority: 'high',
          tags: ['comprehensive', 'tracking'],
          custom: {
            trackingTest: true,
            complexity: 'high',
            dependencies: ['dep1', 'dep2']
          }
        },
        context: {
          milestone: 'M0-TEST',
          category: 'comprehensive-tracking',
          documents: ['comprehensive.md'],
          affectedComponents: ['ComprehensiveTrackingComponent'],
          metadata: { comprehensiveTest: true }
        },
        progress: 75,
        authority: {
          level: 'high',
          authoritySource: 'ComprehensiveAuthority',
          validationStatus: 'validated',
          validator: 'ComprehensiveValidator',
          complianceScore: 90,
          permissions: ['read', 'write', 'execute'],
          metadata: { comprehensive: true }
        }
      } as any;

      await expect(governanceSystem.track(trackingData)).resolves.not.toThrow();
    });

    it('should handle tracking data with missing optional fields', async () => {
      const minimalTrackingData = {
        componentId: 'MinimalTrackingComponent',
        timestamp: new Date().toISOString(),
        status: 'operational',
        metadata: {
          phase: 'testing',
          progress: 25,
          priority: 'low',
          tags: ['minimal'],
          custom: { minimalTest: true }
        },
        context: {
          milestone: 'M0-TEST',
          category: 'minimal-tracking',
          documents: ['minimal.md'],
          affectedComponents: ['MinimalTrackingComponent'],
          metadata: { minimalTest: true }
        },
        progress: 25
        // Note: authority field is optional and omitted
      } as any;

      await expect(governanceSystem.track(minimalTrackingData)).resolves.not.toThrow();
    });
  });

  describe('Enhanced Coverage - Compliance Monitoring Error Handling', () => {
    it('should handle compliance monitoring callback errors and remove failed monitors', async () => {
      // Create compliance monitoring with error-throwing callback
      const monitoringId = await governanceSystem.monitorCompliance(() => {
        throw new Error('Compliance monitoring callback error');
      });

      expect(monitoringId).toBeDefined();
      expect(typeof monitoringId).toBe('string');

      // Test that the monitoring subscription was created successfully
      // The actual callback error handling is tested through the compliance status update
      const status = await governanceSystem.getComplianceStatus();
      expect(status).toBeDefined();
    });

    it('should trigger non-compliant status with multiple violations', async () => {
      // Log multiple critical violations to force non-compliant status
      const violations = [
        'Critical security violation',
        'Major compliance breach',
        'Severe governance failure',
        'Critical authority violation',
        'Major audit failure'
      ];

      // Use sequential processing to ensure all violations are logged
      for (const violation of violations) {
        await governanceSystem.logGovernanceEvent(
          'violation_report',
          'critical',
          'NonCompliantTestSource',
          violation,
          {
            milestone: 'M0-TEST',
            category: 'non-compliant',
            documents: ['violation.md'],
            affectedComponents: ['NonCompliantComponent'],
            metadata: { violationType: 'critical', severity: 'high' }
          }
        );
      }

      // Get compliance status to trigger the non-compliant path
      const status = await governanceSystem.getComplianceStatus();

      expect(status).toBeDefined();
      expect(typeof status.overall).toBe('string');
      expect(typeof status.score).toBe('number');

      // With multiple critical violations, score should be low
      expect(status.score).toBeLessThan(70);
    });
  });

  describe('Enhanced Coverage - Priority Mapping and Severity Handling', () => {
    it('should map all severity levels to correct priorities', async () => {
      const severityMappings = [
        { severity: 'info' as const, expectedPriority: 'low' },
        { severity: 'warning' as const, expectedPriority: 'low' },
        { severity: 'error' as const, expectedPriority: 'medium' },
        { severity: 'critical' as const, expectedPriority: 'high' }
      ];

      // Test each severity mapping by creating action plans
      for (const mapping of severityMappings) {
        const findings = [
          {
            type: 'test_finding',
            severity: mapping.severity,
            description: `Test finding with ${mapping.severity} severity`,
            component: `TestComponent_${mapping.severity}`
          }
        ];

        const actionPlan = await governanceSystem.createComplianceActionPlan(findings);

        expect(actionPlan).toBeDefined();
        expect(Array.isArray(actionPlan.actionItems)).toBe(true);

        if (actionPlan.actionItems.length > 0) {
          const actionItem = actionPlan.actionItems[0];
          expect(actionItem.priority).toBe(mapping.expectedPriority);
        }
      }
    });

    it('should handle mixed severity findings in action plan creation', async () => {
      const mixedFindings = [
        {
          type: 'info_finding',
          severity: 'info',
          description: 'Info level finding',
          component: 'InfoComponent'
        },
        {
          type: 'warning_finding',
          severity: 'warning',
          description: 'Warning level finding',
          component: 'WarningComponent'
        },
        {
          type: 'error_finding',
          severity: 'error',
          description: 'Error level finding',
          component: 'ErrorComponent'
        },
        {
          type: 'critical_finding',
          severity: 'critical',
          description: 'Critical level finding',
          component: 'CriticalComponent'
        }
      ];

      const actionPlan = await governanceSystem.createComplianceActionPlan(mixedFindings);

      expect(actionPlan).toBeDefined();
      expect(Array.isArray(actionPlan.actionItems)).toBe(true);
      expect(actionPlan.actionItems.length).toBe(4);

      // Verify priority levels are represented (based on actual implementation)
      const priorities = actionPlan.actionItems.map((item: any) => item.priority);
      expect(priorities).toContain('low');   // info and warning
      expect(priorities).toContain('medium'); // error
      expect(priorities).toContain('high');   // critical
    });
  });

  describe('Enhanced Coverage - System Health Check Simulation', () => {
    it('should have system health check method available', () => {
      // Test that the health check method exists without actually calling it
      const healthCheckMethod = (governanceSystem as any)._checkSystemHealth;
      expect(typeof healthCheckMethod).toBe('function');
    });

    it('should handle timer statistics in test mode', () => {
      // Verify that test mode properly skips health checks
      const testMode = (governanceSystem as any)._testMode;
      expect(testMode).toBe(true);

      // Verify that NODE_ENV is set to test
      expect(process.env.NODE_ENV).toBe('test');
    });
  });

  describe('Enhanced Coverage - Memory Pressure Simulation', () => {
    it('should have emergency cleanup method available', () => {
      // Test that the emergency cleanup method exists without triggering memory pressure
      const emergencyCleanupMethod = (governanceSystem as any).performEmergencyCleanup;
      expect(typeof emergencyCleanupMethod).toBe('function');
    });

    it('should have memory allocation check method available', () => {
      // Test that the memory allocation check method exists
      const canAllocateMethod = (governanceSystem as any).canAllocateMemory;
      expect(typeof canAllocateMethod).toBe('function');

      // In test mode with memory override, this should return true
      const canAllocate = canAllocateMethod.call(governanceSystem);
      expect(canAllocate).toBe(true);
    });

    it('should handle memory pressure detection logic', () => {
      // Test the memory pressure detection without actually triggering it
      const testMemoryOverride = (governanceSystem as any)._testMemoryOverride;
      expect(testMemoryOverride).toBe(true);

      // Verify that memory boundaries are set
      const memoryBoundaries = (governanceSystem as any).memoryBoundaries;
      expect(memoryBoundaries).toBeDefined();
    });
  });
});