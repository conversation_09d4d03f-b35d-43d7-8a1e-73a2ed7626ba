/**
 * @file CrossReferenceTrackingEngine.test.ts
 * @description Enterprise-grade tests for CrossReferenceTrackingEngine
 */

import { CrossReferenceTrackingEngine } from '../CrossReferenceTrackingEngine';

// Minimal helper types to avoid tight coupling with shared types in tests
interface TTrackingDataLike {
  componentId: string;
  status?: string;
  timestamp?: string;
  metadata?: any;
  context?: { dependencies?: string[]; dependents?: string[] };
  progress?: any;
  authority?: any;
}

describe('CrossReferenceTrackingEngine', () => {
  let engine: CrossReferenceTrackingEngine;

  beforeEach(async () => {
    engine = new CrossReferenceTrackingEngine({} as any);
    await engine.initialize();
  });

  afterEach(async () => {
    try { await engine.shutdown(); } catch {}
  });

  // ---------------------------------------------------------------------------
  // Constructor and Initialization
  // ---------------------------------------------------------------------------
  test('creates instance and initializes successfully', async () => {
    expect(engine).toBeInstanceOf(CrossReferenceTrackingEngine);
    expect(engine.isReady()).toBe(true);
  });

  // ---------------------------------------------------------------------------
  // Tracking and Graph Building
  // ---------------------------------------------------------------------------
  test('processes tracking data and builds dependency graph', async () => {
    const data: TTrackingDataLike = {
      componentId: 'comp-A',
      status: 'active',
      timestamp: new Date().toISOString(),
      metadata: { phase: 'test' },
      context: { dependencies: ['comp-B'], dependents: [] }
    };

    await engine.track(data as any);

    const graph = engine.getDependencyGraph();
    expect(graph.nodes.has('comp-A')).toBe(true);
    expect(graph.edges.get('comp-A')).toEqual(['comp-B']);
  });

  // ---------------------------------------------------------------------------
  // Validation Flows
  // ---------------------------------------------------------------------------
  test('validates engine state and returns structured results', async () => {
    const result = await engine.validate();
    expect(result).toBeDefined();
    expect(result.componentId).toBe('CrossReferenceTrackingEngine');
    expect(['valid', 'warning', 'invalid']).toContain(result.status);
    expect(Array.isArray(result.checks)).toBe(true);
  });

  test('validateCrossReferences detects missing references and cycles', async () => {
    // Build a simple cycle A -> B -> A
    const a: TTrackingDataLike = {
      componentId: 'A',
      status: 'active',
      timestamp: new Date().toISOString(),
      context: { dependencies: ['B'] }
    };
    const b: TTrackingDataLike = {
      componentId: 'B',
      status: 'active',
      timestamp: new Date().toISOString(),
      context: { dependencies: ['A'] }
    };
    await engine.track(a as any);
    await engine.track(b as any);

    // Include one missing reference C to trigger missing warning
    const references = [
      { id: 'B', type: 'component' },
      { id: 'C', type: 'component' } // missing
    ];

    const validation = await engine.validateCrossReferences('A', references as any);

    // Missing reference should be reported
    expect(validation.references.missingReferences).toContain('C');
    expect(validation.warnings.some(w => /Missing reference: C/.test(w))).toBe(true);

    // Cycle detection warning for A
    expect(validation.warnings.some(w => /Circular dependency detected for A/.test(w))).toBe(true);

    // Score should be reduced due to issues
    expect(validation.overallScore).toBeLessThan(100);
  });

  test('getValidationHistory accumulates results over time', async () => {
    const v1 = await engine.doValidate();
    const v2 = await engine.doValidate();
    const history = engine.getValidationHistory();
    expect(history.length).toBeGreaterThanOrEqual(2);
    expect(history[history.length - 1].componentId).toBe('CrossReferenceTrackingEngine');
  });

  // ---------------------------------------------------------------------------
  // Graph Introspection
  // ---------------------------------------------------------------------------
  test('getDependencyGraph returns maps with roots/leaves/orphans arrays', async () => {
    // Track a node with no outgoing deps
    await engine.track({ componentId: 'X', context: { dependencies: [] } } as any);

    const graph = engine.getDependencyGraph();
    expect(graph.nodes instanceof Map).toBe(true);
    expect(graph.edges instanceof Map).toBe(true);
    expect(Array.isArray(graph.roots)).toBe(true);
    expect(Array.isArray(graph.leaves)).toBe(true);
    expect(Array.isArray(graph.orphans)).toBe(true);
  });

  // ---------------------------------------------------------------------------
  // Shutdown
  // ---------------------------------------------------------------------------
  test('shutdown clears internal caches/graphs (observable via getters)', async () => {
    // Build some state
    await engine.track({ componentId: 'S1', context: { dependencies: ['S2'] } } as any);

    // Verify state exists
    let graph = engine.getDependencyGraph();
    expect(graph.nodes.size).toBeGreaterThan(0);

    // Shutdown
    await engine.shutdown();

    // After shutdown, dependency graph should be cleared
    graph = engine.getDependencyGraph();
    expect(graph.nodes.size).toBe(0);
    expect(graph.edges.size).toBe(0);
  });
});

