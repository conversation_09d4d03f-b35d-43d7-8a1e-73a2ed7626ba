import { GovernanceTrackingSystem } from '../GovernanceTrackingSystem';
import { getTimerCoordinator } from '../../../../../../shared/src/base/TimerCoordinationService';

const coordinator: any = getTimerCoordinator();

describe('GovernanceTrackingSystem MEM-SAFE-002 compliance', () => {
  beforeEach(() => {
    jest.spyOn(coordinator as any, 'clearServiceTimers').mockImplementation(() => {});
    jest.spyOn(coordinator as any, 'clearAllTimers').mockImplementation(() => {});
    jest.spyOn(coordinator as any, 'createCoordinatedInterval').mockImplementation((...args: any[]) => `${args[2]}:${args[3]}`);
    jest.spyOn(coordinator as any, 'removeCoordinatedTimer').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('doShutdown clears timers via serviceId', async () => {
    const sys = new GovernanceTrackingSystem({} as any);

    // Temporarily disable test mode to test actual timer cleanup behavior
    const originalNodeEnv = process.env.NODE_ENV;
    const originalJestWorker = process.env.JEST_WORKER_ID;

    try {
      // Force the system out of test mode for this test
      process.env.NODE_ENV = 'development';
      delete process.env.JEST_WORKER_ID;

      // Force re-detection of test mode
      (sys as any)._testMode = false;

      await sys.initialize();

      // simulate monitoring timer
      (coordinator as any).createCoordinatedInterval(() => {}, 1000, 'GovernanceTrackingSystem', 'memory-monitoring');

      await sys.shutdown();

      const svcSpy = (coordinator as any).clearServiceTimers as jest.Mock;
      if (svcSpy && svcSpy.mock) {
        expect(svcSpy).toHaveBeenCalledWith('GovernanceTrackingSystem');
      } else {
        expect((coordinator as any).clearAllTimers).toHaveBeenCalled();
      }
    } finally {
      // Restore original environment
      process.env.NODE_ENV = originalNodeEnv;
      if (originalJestWorker) {
        process.env.JEST_WORKER_ID = originalJestWorker;
      }
    }
  });

  test('bounded buffers via AtomicCircularBuffer', async () => {
    const sys = new GovernanceTrackingSystem({} as any);
    await sys.initialize();

    // Fill beyond capacity to rely on ACB eviction policy
    const count = 1000;
    for (let i = 0; i < count; i++) {
      await sys.logGovernanceEvent(
        `e-${i}`,
        new Date().toISOString(),
        'unit-test',
        'info',
        'check',
        { affectedComponents: ['X'], metadata: {} },
        undefined
      );
    }

    const events = await sys.getGovernanceEventHistory();
    expect(events.length).toBeGreaterThan(0);

    await sys.shutdown();
  });
});

