import { AuthorityTrackingService } from '../AuthorityTrackingService';
import { getTimerCoordinator } from '../../../../../../shared/src/base/TimerCoordinationService';

const coordinator: any = getTimerCoordinator();

describe('AuthorityTrackingService MEM-SAFE-002 compliance', () => {
  beforeEach(() => {
    jest.spyOn(coordinator as any, 'clearServiceTimers').mockImplementation(() => {});
    jest.spyOn(coordinator as any, 'clearAllTimers').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('lifecycle initialize → operations → shutdown with timer cleanup', async () => {
    const svc = new AuthorityTrackingService();
    await svc.initialize();

    // Perform minimal operations to touch buffers
    await svc.validateAuthority('foundation-context', 'track', 'architectural-authority');
    await svc.subscribeToGovernanceEvents(() => {});
    await svc.logGovernanceEvent('governance_update', 'info', 'AuthorityTrackingServiceTest', 'test', {}, undefined as any);

    await svc.shutdown();

    const svcSpy = (coordinator as any).clearServiceTimers as jest.Mock;
    if (svcSpy && svcSpy.mock) {
      expect(svcSpy).toHaveBeenCalledWith('AuthorityTrackingService');
    } else {
      expect((coordinator as any).clearAllTimers).toHaveBeenCalled();
    }
  });

  test('bounded AtomicCircularBuffer usage does not throw when over capacity', async () => {
    const svc = new AuthorityTrackingService();
    await svc.initialize();

    // Push over the buffer limits to ensure no unbounded growth or throw
    for (let i = 0; i < 1200; i++) {
      await svc.logGovernanceEvent('governance_update', 'info', 'AuthorityTrackingServiceTest', `e-${i}`, {}, undefined as any);
    }

    const eventsSize = (svc as any)._governanceEvents.getSize();
    expect(eventsSize).toBeLessThanOrEqual(1000);

    await svc.shutdown();
  });
});

