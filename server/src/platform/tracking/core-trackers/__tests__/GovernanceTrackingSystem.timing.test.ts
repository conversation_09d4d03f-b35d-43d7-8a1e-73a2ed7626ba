import { GovernanceTrackingSystem } from '../GovernanceTrackingSystem';

describe('GovernanceTrackingSystem P1 Resilient Timing', () => {
  test('records timing for logging and report generation', async () => {
    const svc = new GovernanceTrackingSystem({ testMode: true } as any);
    await svc.initialize();

    const eventId = await svc.logGovernanceEvent('policy_update', 'info', 'test', 'desc', {} as any, undefined as any);
    expect(eventId).toBeTruthy();

    const report = await svc.generateComplianceReport({ includeActionPlan: false, format: 'json' });
    expect(report).toBeTruthy();

    const metrics = (svc as any)._metricsCollector.createCompatibleMetrics();
    expect(Object.keys(metrics)).toEqual(expect.arrayContaining(['governance_event_logging', 'report_generation']));

    await svc.shutdown();
  });
});

