/**
 * Security Enforcement Layer Implementation
 * 
 * Provides configurable security enforcement for the GovernanceTrackingSystem
 * with support for different security profiles based on environment and testing needs.
 */

import {
  ISecurityEnforcement,
  SecurityEvent,
  SecurityTestMonitor,
  SecurityViolationError
} from './ISecurityEnforcement';
import { SecurityConfig } from './SecurityConfig';
import {
  getMaxMapSize,
  getMaxCacheSize,
  getMemoryBoundaryConfig,
  getAttackPreventionLimits
} from '../../../../../../shared/src/constants/platform/tracking/tracking-constants';


import { MemorySafeResourceManager } from '../../../../../../shared/src/base/MemorySafeResourceManager';
import { AtomicCircularBuffer } from '../../../../../../shared/src/base/AtomicCircularBuffer';

export class SecurityEnforcementLayer extends MemorySafeResourceManager implements ISecurityEnforcement {
  private _requestCounts: AtomicCircularBuffer<number>;
  private _lastRequestTime: AtomicCircularBuffer<number>;
  private _securityEvents: AtomicCircularBuffer<SecurityEvent>;
  private _securityMetrics = {
    totalEvents: 0,
    blockedAttempts: 0,
    eventsByType: {} as Record<string, number>,
    eventsBySeverity: {} as Record<string, number>
  };

  // Environment-aware memory management using environment calculator
  private readonly memoryBoundaries = getMemoryBoundaryConfig();
  private readonly attackLimits = getAttackPreventionLimits();

  constructor(
    private config: SecurityConfig,
    private monitor?: SecurityTestMonitor
  ) {
    // Initialize MemorySafeResourceManager with appropriate limits
    super({
      maxIntervals: 3, // Cleanup intervals
      maxTimeouts: 2,
      maxCacheSize: getMaxCacheSize(),
      maxConnections: 0,
      memoryThresholdMB: 50,
      cleanupIntervalMs: 300000
    });

    // Validate configuration on construction
    if (!this.isValidConfig(config)) {
      throw new Error('Invalid security configuration provided');
    }

    // Initialize AtomicCircularBuffer instances
    const maxRequests = getMaxMapSize();
    const maxEvents = getMaxCacheSize();

    this._requestCounts = new AtomicCircularBuffer<number>(maxRequests);
    this._lastRequestTime = new AtomicCircularBuffer<number>(maxRequests);
    this._securityEvents = new AtomicCircularBuffer<SecurityEvent>(maxEvents);

    // Log dynamic limits for verification
    console.log('Security layer using environment-calculated limits:', {
      maxSecurityEvents: this.memoryBoundaries.maxMapSize,
      maxRequestEntries: this.memoryBoundaries.maxCacheSize,
      maxConnections: this.attackLimits.maxRealTimeConnections,
      testType: process.env.TEST_TYPE,
      nodeEnv: process.env.NODE_ENV
    });
  }

  protected async doInitialize(): Promise<void> {
    // Initialize AtomicCircularBuffer instances
    await this._requestCounts.initialize();
    await this._lastRequestTime.initialize();
    await this._securityEvents.initialize();

    // Create memory-safe cleanup interval
    this.createSafeInterval(
      () => this.performPeriodicCleanup(),
      60000, // Every minute
      'security-cleanup'
    );
  }

  protected async doShutdown(): Promise<void> {
    // Shutdown AtomicCircularBuffer instances
    await this._requestCounts.shutdown();
    await this._lastRequestTime.shutdown();
    await this._securityEvents.shutdown();
  }

  /**
   * Periodic cleanup using memory-safe patterns
   */
  private async performPeriodicCleanup(): Promise<void> {
    try {
      // Use AtomicCircularBuffer's built-in cleanup instead of manual pruning
      // The circular buffer automatically manages memory boundaries
      this.checkMemoryPressureSync();
    } catch (error) {
      console.warn('[SecurityEnforcementLayer] Cleanup error:', error);
    }
  }

  async enforceFloodProtection(source: string): Promise<boolean> {
    if (!this.config.floodProtection.enabled) {
      return true;
    }

    const now = Date.now();
    const floodKey = `flood_protection_${source}`;

    const currentCount = this._requestCounts.getItem(floodKey) || 0;
    const lastTime = this._lastRequestTime.getItem(floodKey) || 0;

    // Reset window if expired
    if (now - lastTime > this.config.floodProtection.window) {
      await this._requestCounts.addItem(floodKey, 1);
      await this._lastRequestTime.addItem(floodKey, now);
      return true;
    }
    
    // Check if threshold exceeded
    if (currentCount >= this.config.floodProtection.threshold) {
      const securityEvent: SecurityEvent = {
        type: 'flood_protection_violation',
        source,
        action: 'blocked',
        timestamp: new Date(),
        severity: 'high',
        description: `Flood protection activated for source: ${source}`,
        metadata: {
          currentCount,
          threshold: this.config.floodProtection.threshold,
          window: this.config.floodProtection.window
        }
      };

      this.logSecurityEvent(securityEvent);
      this._securityMetrics.blockedAttempts++;
      
      throw new SecurityViolationError('Flood protection activated', 'flood_protection', source);
    }

    // Increment counter
    await this._requestCounts.addItem(floodKey, currentCount + 1);
    return true;
  }

  async enforceRateLimit(source: string): Promise<boolean> {
    if (!this.config.rateLimiting.enabled) {
      return true;
    }

    const now = Date.now();
    const rateLimitKey = `rate_limit_${source}`;

    const currentCount = this._requestCounts.getItem(rateLimitKey) || 0;
    const lastTime = this._lastRequestTime.getItem(rateLimitKey) || 0;

    // Reset window if expired
    if (now - lastTime > this.config.rateLimiting.window) {
      await this._requestCounts.addItem(rateLimitKey, 1);
      await this._lastRequestTime.addItem(rateLimitKey, now);
      return true;
    }

    // Check if limit exceeded
    if (currentCount >= this.config.rateLimiting.maxRequests) {
      const securityEvent: SecurityEvent = {
        type: 'rate_limit_violation',
        source,
        action: 'blocked',
        timestamp: new Date(),
        severity: 'medium',
        description: `Rate limit exceeded for source: ${source}`,
        metadata: {
          currentCount,
          maxRequests: this.config.rateLimiting.maxRequests,
          window: this.config.rateLimiting.window
        }
      };

      this.logSecurityEvent(securityEvent);
      this._securityMetrics.blockedAttempts++;

      throw new SecurityViolationError('Rate limit exceeded', 'rate_limit', source);
    }

    // Increment counter
    await this._requestCounts.addItem(rateLimitKey, currentCount + 1);
    return true;
  }

  sanitizeInput(input: string): string {
    if (!this.config.inputSanitization.enabled || !input) {
      return input;
    }
    
    // Remove XSS payloads
    let sanitized = input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '[SCRIPT_REMOVED]')
      .replace(/javascript:/gi, 'javascript_removed:')
      .replace(/onerror\s*=/gi, 'onerror_removed=')
      .replace(/onload\s*=/gi, 'onload_removed=')
      .replace(/<iframe\b[^>]*>/gi, '[IFRAME_REMOVED]')
      .replace(/<svg\b[^>]*onload[^>]*>/gi, '[SVG_REMOVED]')
      .replace(/<img\b[^>]*onerror[^>]*>/gi, '[IMG_REMOVED]');

    // Remove SQL injection patterns
    sanitized = sanitized
      .replace(/['"];?\s*(DROP|DELETE|INSERT|UPDATE|SELECT)\s+/gi, '[SQL_INJECTION_REMOVED]')
      .replace(/'\s*OR\s*'1'\s*=\s*'1/gi, '[SQL_INJECTION_REMOVED]')
      .replace(/--\s*$/gm, '[COMMENT_REMOVED]');

    // Remove path traversal
    sanitized = sanitized.replace(/\.\.\//g, '[PATH_TRAVERSAL_REMOVED]');
    
    // Remove command injection
    sanitized = sanitized.replace(/;\s*(rm|del|format|shutdown)/gi, '[COMMAND_INJECTION_REMOVED]');
    
    // Remove null bytes
    sanitized = sanitized.replace(/\x00/g, '[NULL_BYTE_REMOVED]');
    
    // Truncate if too large
    if (sanitized.length >= this.config.inputSanitization.maxSize) {
      const truncateMarker = this.config.inputSanitization.truncateMarker;
      const maxContentLength = this.config.inputSanitization.maxSize - truncateMarker.length - 1;
      sanitized = sanitized.substring(0, maxContentLength) + truncateMarker;
    }

    return sanitized;
  }

  sanitizeMetadata(metadata: Record<string, unknown>): Record<string, unknown> {
    if (!this.config.inputSanitization.enabled || !metadata) {
      return metadata;
    }

    const sanitized: Record<string, unknown> = {};
    
    for (const [key, value] of Object.entries(metadata)) {
      if (typeof value === 'string') {
        sanitized[key] = this.sanitizeInput(value);
      } else if (typeof value === 'object' && value !== null) {
        sanitized[key] = this.sanitizeMetadata(value as Record<string, unknown>);
      } else {
        sanitized[key] = value;
      }
    }
    
    return sanitized;
  }

  logSecurityEvent(event: SecurityEvent): void {
    // Use AtomicCircularBuffer for memory-safe event storage
    this._securityEvents.addItem(`event_${Date.now()}_${Math.random()}`, event).catch(error => {
      console.warn('[SecurityEnforcementLayer] Failed to log security event:', error);
    });
    this._securityMetrics.totalEvents++;

    // AtomicCircularBuffer automatically manages memory boundaries

    // Update metrics
    this._securityMetrics.eventsByType[event.type] =
      (this._securityMetrics.eventsByType[event.type] || 0) + 1;

    if (event.severity) {
      this._securityMetrics.eventsBySeverity[event.severity] =
        (this._securityMetrics.eventsBySeverity[event.severity] || 0) + 1;
    }

    // Delegate to monitor if available
    if (this.monitor) {
      this.monitor.logSecurityEvent(event);
    }

    // Log to console if audit logging is enabled
    if (this.config.auditLogging.enabled) {
      console.log(`[SECURITY] ${event.type}: ${event.description || event.action}`);
    }

    // Trigger automatic memory pressure detection using environment calculator
    this.checkMemoryPressureSync();
  }

  isSecurityError(error: Error): boolean {
    return error instanceof SecurityViolationError;
  }

  getSecurityConfig(): SecurityConfig {
    return { ...this.config };
  }

  resetSecurityCounters(): void {
    // Use AtomicCircularBuffer's memory-safe clear methods
    this._requestCounts.clear().catch(error => {
      console.warn('[SecurityEnforcementLayer] Failed to clear request counts:', error);
    });
    this._lastRequestTime.clear().catch(error => {
      console.warn('[SecurityEnforcementLayer] Failed to clear request times:', error);
    });
    this._securityEvents.clear().catch(error => {
      console.warn('[SecurityEnforcementLayer] Failed to clear security events:', error);
    });

    this._securityMetrics = {
      totalEvents: 0,
      blockedAttempts: 0,
      eventsByType: {},
      eventsBySeverity: {}
    };

    if (this.monitor) {
      this.monitor.resetSecurityCounters();
    }
  }

  /**
   * Set security monitor for testing purposes
   */
  setMonitor(monitor: SecurityTestMonitor): void {
    this.monitor = monitor;
  }

  /**
   * Check memory pressure using MemorySafeResourceManager's built-in monitoring
   */
  private checkMemoryPressureSync(): void {
    // Use inherited memory pressure detection from MemorySafeResourceManager
    // AtomicCircularBuffer automatically manages memory boundaries
    const memUsage = process.memoryUsage();
    const heapUsedMB = memUsage.heapUsed / (1024 * 1024);

    // Log memory usage for monitoring
    if (heapUsedMB > 100) {
      console.warn(`[SECURITY] Memory usage: ${heapUsedMB.toFixed(2)}MB`);
    }
  }





  private isValidConfig(config: SecurityConfig): boolean {
    return !!(
      config &&
      config.floodProtection &&
      config.rateLimiting &&
      config.inputSanitization &&
      typeof config.floodProtection.enabled === 'boolean' &&
      typeof config.rateLimiting.enabled === 'boolean' &&
      typeof config.inputSanitization.enabled === 'boolean'
    );
  }
}
