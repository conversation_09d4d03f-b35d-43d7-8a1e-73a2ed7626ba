/**
 * @file GovernanceRuleValidatorFactory Test Suite
 * @filepath server/src/platform/governance/rule-management/core/__tests__/GovernanceRuleValidatorFactory.test.ts
 * @task-id G-TSK-01.SUB-01.1.IMP-02-TEST
 * @component governance-rule-validator-factory-test
 * @reference foundation-context.GOVERNANCE.004
 * @tier T1
 * @context foundation-context
 * @category Foundation
 * @created 2025-08-28
 * 
 * @description
 * Comprehensive test suite for GovernanceRuleValidatorFactory following OA Framework testing excellence standards:
 * - 95%+ coverage across all metrics (statements, branches, functions, lines)
 * - Anti-Simplification Policy compliance with genuine business value testing
 * - Memory safety compliance (MEM-SAFE-002) with BaseTrackingService inheritance
 * - Surgical precision testing patterns for hard-to-reach code paths
 * - Dual path testing for complete branch coverage
 * - Constructor success and failure path coverage
 * - Validator factory patterns and creation mechanisms testing
 * - Error handling and fallback mechanisms validation
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E<PERSON>Z. Consultancy"
 * @governance-adr ADR-foundation-001-governance-validators
 * @governance-dcr DCR-foundation-001-governance-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🧪 TESTING EXCELLENCE STANDARDS
 * @testing-methodology surgical-precision-testing
 * @coverage-target 95%+ across all metrics
 * @anti-simplification-compliance full
 * @memory-safety-compliance MEM-SAFE-002
 * @performance-standards enterprise-grade
 * 
 * 📋 TEST IMPLEMENTATION STANDARDS
 * @pattern-application proven-lessons-learned
 * @constructor-testing success-and-failure-paths
 * @branch-coverage dual-path-testing
 * @error-handling comprehensive-validation
 * @mock-strategy spy-dont-replace
 * 
 * 📝 VERSION HISTORY
 * @version-history
 * v1.0.0 (2025-08-28) - Initial comprehensive test suite with 95%+ coverage target and Anti-Simplification Policy compliance
 */

// ============================================================================
// TABLE OF CONTENTS
// ============================================================================
// IMPORTS: External dependencies and internal modules
// TEST SETUP: Mock factories and test utilities
// MOCK IMPLEMENTATIONS: Built-in validator mocks and factory mocks
// TEST SUITES:
//   - Constructor and Initialization Tests
//   - Validator Creation Tests
//   - Validator Registration Tests
//   - Built-in Validator Tests
//   - Error Handling Tests
//   - Performance and Metrics Tests
//   - Cache Management Tests
//   - Cleanup and Shutdown Tests
//   - Edge Cases and Boundary Tests
// UTILITIES: Helper functions and test data generators
// ============================================================================

import { jest, describe, beforeEach, afterEach, it, expect, beforeAll, afterAll } from '@jest/globals';
import * as crypto from 'crypto';

// Import target component
import { GovernanceRuleValidatorFactory } from '../GovernanceRuleValidatorFactory';

// Import interfaces and types
import {
  IGovernanceRuleValidatorFactory,
  IGovernanceRuleValidator
} from '../../../../../../../shared/src/types/platform/governance/governance-interfaces';

import {
  TGovernanceRuleType,
  TValidatorConfiguration,
  TGovernanceRule,
  TRuleValidationResult,
  TRetryConfiguration
} from '../../../../../../../shared/src/types/platform/governance/rule-management-types';

import {
  TValidationResult,
  TValidationError,
  TValidationWarning,
  TMetrics,
  TComponentStatus
} from '../../../../../../../shared/src/types/platform/tracking/tracking-types';

import {
  VALIDATION_ERROR_CODES,
  VALIDATION_WARNING_CODES,
  ERROR_MESSAGES,
  WARNING_MESSAGES
} from '../../../../../../../shared/src/constants/platform/tracking/tracking-constants';

// ============================================================================
// TEST SETUP AND UTILITIES
// ============================================================================

/**
 * Test configuration constants
 */
const TEST_CONFIG = {
  TIMEOUT_MS: 30000,
  MAX_VALIDATORS_PER_TYPE: 10,
  VALIDATOR_CACHE_TTL_MS: 3600000,
  MAX_CONCURRENT_VALIDATIONS: 100,
  VALIDATOR_CLEANUP_INTERVAL_MS: 300000,
  PERFORMANCE_METRICS_RETENTION_HOURS: 48
};

/**
 * Mock validator class for testing custom validator registration
 */
class MockCustomValidator implements IGovernanceRuleValidator {
  private _configuration?: TValidatorConfiguration;

  public async validateRule(rule: TGovernanceRule, target: unknown): Promise<TRuleValidationResult> {
    const validationId = `mock-val-${Date.now()}-${crypto.randomBytes(4).toString('hex')}`;

    return {
      validationId,
      criteriaId: rule.configuration.criteria.expression,
      status: 'passed',
      score: 85,
      message: 'Mock validation passed',
      details: {
        expected: 'Mock validation',
        actual: 'Mock validated',
        operator: 'equals',
        context: { ruleId: rule.ruleId }
      },
      timestamp: new Date()
    };
  }

  public getSupportedTypes(): TGovernanceRuleType[] {
    return ['custom-rule'];
  }

  public async configure(configuration: TValidatorConfiguration): Promise<void> {
    this._configuration = configuration;
  }
}

/**
 * Dynamic mock validator class that supports any rule type
 */
class DynamicMockValidator implements IGovernanceRuleValidator {
  private _supportedType: TGovernanceRuleType;
  private _configuration?: TValidatorConfiguration;

  constructor(supportedType: TGovernanceRuleType) {
    this._supportedType = supportedType;
  }

  public async validateRule(rule: TGovernanceRule, target: unknown): Promise<TRuleValidationResult> {
    const validationId = `dynamic-val-${Date.now()}-${crypto.randomBytes(4).toString('hex')}`;

    return {
      validationId,
      criteriaId: rule.configuration.criteria.expression,
      status: 'passed',
      score: 85,
      message: `Dynamic validation passed for ${this._supportedType}`,
      details: {
        expected: 'Dynamic validation',
        actual: 'Dynamic validated',
        operator: 'equals',
        context: { ruleId: rule.ruleId, type: this._supportedType }
      },
      timestamp: new Date()
    };
  }

  public getSupportedTypes(): TGovernanceRuleType[] {
    return [this._supportedType];
  }

  public async configure(configuration: TValidatorConfiguration): Promise<void> {
    this._configuration = configuration;
  }
}

/**
 * Factory function to create dynamic validators
 */
function createDynamicValidatorClass(supportedType: TGovernanceRuleType) {
  return class extends DynamicMockValidator {
    constructor() {
      super(supportedType);
    }
  };
}

/**
 * Invalid validator class for testing error scenarios
 */
class InvalidValidator {
  // Missing required methods to test validation
}

/**
 * Validator that throws during instantiation
 */
class FailingValidator implements IGovernanceRuleValidator {
  constructor() {
    throw new Error('Validator instantiation failed');
  }

  public async validateRule(rule: TGovernanceRule, target: unknown): Promise<TRuleValidationResult> {
    throw new Error('Should not reach here');
  }

  public getSupportedTypes(): TGovernanceRuleType[] {
    return ['failing-validator'];
  }

  public async configure(configuration: TValidatorConfiguration): Promise<void> {
    // Implementation not needed for test
  }
}

/**
 * Create test governance rule
 */
function createTestGovernanceRule(overrides: Partial<TGovernanceRule> = {}): TGovernanceRule {
  return {
    ruleId: 'test-rule-001',
    name: 'Test Rule',
    description: 'Test rule for validation',
    type: 'compliance-check',
    category: 'test',
    severity: 'info',
    priority: 1,
    configuration: {
      parameters: { testParam: 'testValue' },
      criteria: {
        type: 'validation',
        expression: 'test === true',
        expectedValues: [true],
        operators: ['==='],
        weight: 1
      },
      actions: [],
      dependencies: []
    },
    metadata: {
      version: '1.0.0',
      author: 'Test',
      createdAt: new Date(),
      modifiedAt: new Date(),
      tags: [],
      documentation: []
    },
    status: {
      current: 'active',
      activatedAt: new Date(),
      effectiveness: 100
    },
    ...overrides
  };
}

/**
 * Create test validator configuration
 */
function createTestValidatorConfiguration(overrides: Partial<TValidatorConfiguration> = {}): TValidatorConfiguration {
  return {
    type: 'compliance-check',
    parameters: {
      strictness: 'moderate',
      timeout: 30000,
      retry: {
        maxAttempts: 3,
        delayMs: 1000,
        backoffStrategy: 'exponential',
        maxDelayMs: 10000
      },
      customRules: {}
    },
    options: {
      caching: true,
      parallel: false,
      maxConcurrent: 1,
      detailedLogging: false
    },
    metadata: {},
    ...overrides
  };
}

// ============================================================================
// GLOBAL TEST SETUP
// ============================================================================

let factory: GovernanceRuleValidatorFactory;

describe('GovernanceRuleValidatorFactory', () => {
  // ============================================================================
  // SETUP AND TEARDOWN
  // ============================================================================

  beforeEach(async () => {
    // Create fresh factory instance for each test
    factory = new GovernanceRuleValidatorFactory();
    await factory.initialize();
  });

  afterEach(async () => {
    // Clean shutdown to prevent memory leaks
    if (factory) {
      await factory.shutdown();
    }
  });

  // ============================================================================
  // CONSTRUCTOR AND INITIALIZATION TESTS
  // ============================================================================

  describe('Constructor and Initialization', () => {
    it('should create factory instance successfully', () => {
      const newFactory = new GovernanceRuleValidatorFactory();
      
      expect(newFactory).toBeDefined();
      expect(newFactory).toBeInstanceOf(GovernanceRuleValidatorFactory);
    });

    it('should initialize with correct service metadata', async () => {
      const newFactory = new GovernanceRuleValidatorFactory();
      await newFactory.initialize();
      
      // Test service name and version through validation
      const validation = await newFactory.validate();
      expect(validation).toBeDefined();
      expect(validation.componentId).toBe('governance-rule-validator-factory');
      
      await newFactory.shutdown();
    });

    it('should register built-in validators during initialization', async () => {
      const availableTypes = await factory.getAvailableValidatorTypes();
      
      expect(availableTypes).toContain('authority-validation');
      expect(availableTypes).toContain('compliance-check');
      expect(availableTypes).toContain('security-policy');
      expect(availableTypes.length).toBeGreaterThanOrEqual(3);
    });

    it('should initialize performance metrics during startup', async () => {
      // Verify factory is operational by creating a validator
      const config = createTestValidatorConfiguration({ type: 'compliance-check' });
      const validator = await factory.createValidator('compliance-check', config);

      expect(validator).toBeDefined();
    });
  });

  // ============================================================================
  // VALIDATOR CREATION TESTS
  // ============================================================================

  describe('Validator Creation', () => {
    it('should create validator for registered type successfully', async () => {
      const config = createTestValidatorConfiguration({ type: 'compliance-check' });
      const validator = await factory.createValidator('compliance-check', config);

      expect(validator).toBeDefined();
      expect(typeof validator.validateRule).toBe('function');
      expect(typeof validator.getSupportedTypes).toBe('function');
      expect(typeof validator.configure).toBe('function');
    });

    it('should create multiple validators for same type within limits', async () => {
      const config = createTestValidatorConfiguration({ type: 'compliance-check' });
      const validators: IGovernanceRuleValidator[] = [];

      // Create multiple validators up to limit
      for (let i = 0; i < 3; i++) {
        const validator = await factory.createValidator('compliance-check', config);
        validators.push(validator);
        expect(validator).toBeDefined();
      }

      expect(validators.length).toBe(3);
    });

    it('should throw error when creating validator for unregistered type', async () => {
      const config = createTestValidatorConfiguration({ type: 'non-existent-type' as TGovernanceRuleType });

      await expect(factory.createValidator('non-existent-type' as TGovernanceRuleType, config))
        .rejects.toThrow('Validator type not registered: non-existent-type');
    });

    it('should throw error when exceeding maximum validators per type', async () => {
      const config = createTestValidatorConfiguration({ type: 'compliance-check' });

      // Create validators up to the limit
      const promises = [];
      for (let i = 0; i < TEST_CONFIG.MAX_VALIDATORS_PER_TYPE; i++) {
        promises.push(factory.createValidator('compliance-check', config));
      }
      await Promise.all(promises);

      // Attempt to create one more should fail
      await expect(factory.createValidator('compliance-check', config))
        .rejects.toThrow('Maximum validators exceeded for type: compliance-check');
    });

    it('should validate inputs before creating validator', async () => {
      // Test with null/undefined inputs
      await expect(factory.createValidator(null as any, null as any))
        .rejects.toThrow();

      await expect(factory.createValidator('compliance-check', null as any))
        .rejects.toThrow();
    });

    it('should configure validator during creation', async () => {
      const config = createTestValidatorConfiguration({
        type: 'compliance-check',
        parameters: {
          strictness: 'strict',
          timeout: 15000,
          retry: {
            maxAttempts: 5,
            delayMs: 2000,
            backoffStrategy: 'exponential',
            maxDelayMs: 20000
          },
          customRules: { testRule: 'testValue' }
        }
      });

      const validator = await factory.createValidator('compliance-check', config);
      expect(validator).toBeDefined();

      // Verify validator can be used
      const rule = createTestGovernanceRule({ type: 'compliance-check' });
      const result = await validator.validateRule(rule, { test: true });
      expect(result).toBeDefined();
      expect(result.status).toBe('passed');
    });
  });

  // ============================================================================
  // VALIDATOR REGISTRATION TESTS
  // ============================================================================

  describe('Validator Registration', () => {
    it('should register custom validator successfully', async () => {
      await factory.registerValidator('custom-rule', MockCustomValidator);

      const availableTypes = await factory.getAvailableValidatorTypes();
      expect(availableTypes).toContain('custom-rule');
    });

    it('should create validator from registered custom type', async () => {
      await factory.registerValidator('custom-rule', MockCustomValidator);

      const config = createTestValidatorConfiguration({ type: 'custom-rule' });
      const validator = await factory.createValidator('custom-rule', config);

      expect(validator).toBeDefined();
      expect(validator).toBeInstanceOf(MockCustomValidator);
    });

    it('should throw error when registering duplicate validator type', async () => {
      await factory.registerValidator('custom-rule', MockCustomValidator);

      await expect(factory.registerValidator('custom-rule', MockCustomValidator))
        .rejects.toThrow('Validator type already registered: custom-rule');
    });

    it('should validate validator class during registration', async () => {
      // Test with invalid validator class
      await expect(factory.registerValidator('invalid-type', InvalidValidator as any))
        .rejects.toThrow('Invalid validator class');
    });

    it('should test validator instantiation during registration', async () => {
      // Test with validator that fails during instantiation
      await expect(factory.registerValidator('failing-type', FailingValidator))
        .rejects.toThrow('Invalid validator class: Validator instantiation failed');
    });

    it('should validate supported types during registration', async () => {
      // Create validator that doesn't support the registered type
      class MismatchedValidator implements IGovernanceRuleValidator {
        public async validateRule(rule: TGovernanceRule, target: unknown): Promise<TRuleValidationResult> {
          return {} as TRuleValidationResult;
        }

        public getSupportedTypes(): TGovernanceRuleType[] {
          return ['different-type' as TGovernanceRuleType];
        }

        public async configure(configuration: TValidatorConfiguration): Promise<void> {
          // Implementation not needed
        }
      }

      await expect(factory.registerValidator('mismatched-type', MismatchedValidator))
        .rejects.toThrow('Validator does not support rule type: mismatched-type');
    });

    it('should throw error with null or undefined inputs', async () => {
      await expect(factory.registerValidator(null as any, MockCustomValidator))
        .rejects.toThrow('Valid rule type and validator class are required');

      await expect(factory.registerValidator('custom-rule', null as any))
        .rejects.toThrow('Valid rule type and validator class are required');
    });
  });

  // ============================================================================
  // BUILT-IN VALIDATOR TESTS
  // ============================================================================

  describe('Built-in Validators', () => {
    it('should have authority validation validator registered', async () => {
      const config = createTestValidatorConfiguration({ type: 'authority-validation' });
      const validator = await factory.createValidator('authority-validation', config);

      expect(validator).toBeDefined();
      expect(validator.getSupportedTypes()).toContain('authority-validation');
    });

    it('should have compliance check validator registered', async () => {
      const config = createTestValidatorConfiguration({ type: 'compliance-check' });
      const validator = await factory.createValidator('compliance-check', config);

      expect(validator).toBeDefined();
      expect(validator.getSupportedTypes()).toContain('compliance-check');
    });

    it('should have security policy validator registered', async () => {
      const config = createTestValidatorConfiguration({ type: 'security-policy' });
      const validator = await factory.createValidator('security-policy', config);

      expect(validator).toBeDefined();
      expect(validator.getSupportedTypes()).toContain('security-policy');
    });

    it('should validate rules with authority validation validator', async () => {
      const config = createTestValidatorConfiguration({ type: 'authority-validation' });
      const validator = await factory.createValidator('authority-validation', config);

      const rule = createTestGovernanceRule({ type: 'authority-validation' });
      const result = await validator.validateRule(rule, { authority: 'test' });

      expect(result).toBeDefined();
      expect(result.status).toBe('passed');
      expect(result.score).toBe(100);
      expect(result.message).toBe('Authority validation passed');
      expect(result.validationId).toMatch(/^auth-val-/);
    });

    it('should validate rules with compliance check validator', async () => {
      const config = createTestValidatorConfiguration({ type: 'compliance-check' });
      const validator = await factory.createValidator('compliance-check', config);

      const rule = createTestGovernanceRule({ type: 'compliance-check' });
      const result = await validator.validateRule(rule, { compliance: true });

      expect(result).toBeDefined();
      expect(result.status).toBe('passed');
      expect(result.score).toBe(95);
      expect(result.message).toBe('Compliance check passed');
      expect(result.validationId).toMatch(/^comp-val-/);
    });

    it('should validate rules with security policy validator', async () => {
      const config = createTestValidatorConfiguration({ type: 'security-policy' });
      const validator = await factory.createValidator('security-policy', config);

      const rule = createTestGovernanceRule({ type: 'security-policy' });
      const result = await validator.validateRule(rule, { security: 'enabled' });

      expect(result).toBeDefined();
      expect(result.status).toBe('passed');
      expect(result.score).toBe(98);
      expect(result.message).toBe('Security policy validation passed');
      expect(result.validationId).toMatch(/^sec-val-/);
    });
  });

  // ============================================================================
  // ERROR HANDLING TESTS
  // ============================================================================

  describe('Error Handling', () => {
    it('should handle validator creation errors gracefully', async () => {
      // Mock a validator that fails during configuration
      class FailingConfigValidator implements IGovernanceRuleValidator {
        public async validateRule(rule: TGovernanceRule, target: unknown): Promise<TRuleValidationResult> {
          return {} as TRuleValidationResult;
        }

        public getSupportedTypes(): TGovernanceRuleType[] {
          return ['failing-config'];
        }

        public async configure(configuration: TValidatorConfiguration): Promise<void> {
          throw new Error('Configuration failed');
        }
      }

      await factory.registerValidator('failing-config', FailingConfigValidator);
      const config = createTestValidatorConfiguration({ type: 'failing-config' });

      await expect(factory.createValidator('failing-config', config))
        .rejects.toThrow('Configuration failed');
    });

    it('should handle validation errors in input validation', async () => {
      // Test with null configuration
      await expect(factory.createValidator('compliance-check', null as any))
        .rejects.toThrow('Valid validator configuration is required');

      // Test with mismatched configuration type
      const mismatchedConfig = createTestValidatorConfiguration({ type: 'authority-validation' });
      await expect(factory.createValidator('compliance-check', mismatchedConfig))
        .rejects.toThrow('Configuration type must match rule type');
    });

    it('should handle registry corruption gracefully', async () => {
      // Test behavior when registry is in unexpected state
      const config = createTestValidatorConfiguration({ type: 'compliance-check' });

      // Create validator normally first
      const validator = await factory.createValidator('compliance-check', config);
      expect(validator).toBeDefined();

      // Verify factory can still operate normally
      const availableTypes = await factory.getAvailableValidatorTypes();
      expect(availableTypes).toContain('compliance-check');
    });
  });

  // ============================================================================
  // VALIDATION AND HEALTH CHECKS
  // ============================================================================

  describe('Validation and Health Checks', () => {
    it('should validate factory health successfully', async () => {
      const validation = await factory.validate();

      expect(validation).toBeDefined();
      expect(validation.componentId).toBe('governance-rule-validator-factory');
      expect(validation.status).toBe('valid');
      expect(validation.overallScore).toBeGreaterThan(80);
      expect(validation.errors).toHaveLength(0);
    });

    it('should detect missing required validators in health check', async () => {
      // Create a new factory without initialization to test validation
      const newFactory = new GovernanceRuleValidatorFactory();

      const validation = await newFactory.validate();

      expect(validation.status).toBe('invalid');
      expect(Array.isArray(validation.errors)).toBe(true);
      expect(validation.errors.length).toBeGreaterThan(0);
      expect(validation.errors.some((error: string) =>
        error.includes('Required validator type not registered')
      )).toBe(true);

      await newFactory.shutdown();
    });

    it('should warn about low validator type count', async () => {
      // Create factory with minimal validators
      const newFactory = new GovernanceRuleValidatorFactory();

      // Register only one validator
      await newFactory.registerValidator('custom-rule', MockCustomValidator);

      const validation = await newFactory.validate();

      expect(Array.isArray(validation.warnings)).toBe(true);
      expect(validation.warnings.some((warning: string) =>
        warning.includes('Low validator type count')
      )).toBe(true);

      await newFactory.shutdown();
    });
  });

  // ============================================================================
  // PERFORMANCE AND METRICS TESTS
  // ============================================================================

  describe('Performance and Metrics', () => {
    it('should track validator creation metrics', async () => {
      const config = createTestValidatorConfiguration({ type: 'compliance-check' });

      // Create multiple validators to generate metrics
      for (let i = 0; i < 3; i++) {
        await factory.createValidator('compliance-check', config);
      }

      // Verify factory is tracking metrics (indirect verification through successful operations)
      const availableTypes = await factory.getAvailableValidatorTypes();
      expect(availableTypes).toContain('compliance-check');
    });

    it('should handle concurrent validator creation', async () => {
      const config = createTestValidatorConfiguration({ type: 'compliance-check' });

      // Create multiple validators concurrently
      const promises = [];
      for (let i = 0; i < 5; i++) {
        promises.push(factory.createValidator('compliance-check', config));
      }

      const validators = await Promise.all(promises);
      expect(validators).toHaveLength(5);
      validators.forEach(validator => {
        expect(validator).toBeDefined();
      });
    });

    it('should maintain performance under load', async () => {
      const config = createTestValidatorConfiguration({ type: 'compliance-check' });
      const startTime = Date.now();

      // Create validators and measure performance
      const validators = [];
      for (let i = 0; i < 10; i++) {
        const validator = await factory.createValidator('compliance-check', config);
        validators.push(validator);
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should complete within reasonable time (enterprise performance standard)
      expect(duration).toBeLessThan(5000); // 5 seconds
      expect(validators).toHaveLength(10);
    });
  });

  // ============================================================================
  // UNREGISTRATION TESTS
  // ============================================================================

  describe('Validator Unregistration', () => {
    it('should unregister validator type successfully', async () => {
      // Register a custom validator first
      await factory.registerValidator('custom-rule', MockCustomValidator);

      let availableTypes = await factory.getAvailableValidatorTypes();
      expect(availableTypes).toContain('custom-rule');

      // Unregister the validator
      await factory.unregisterValidator('custom-rule');

      availableTypes = await factory.getAvailableValidatorTypes();
      expect(availableTypes).not.toContain('custom-rule');
    });

    it('should handle unregistering non-existent validator type gracefully', async () => {
      // Should not throw error, just log and return
      await expect(factory.unregisterValidator('non-existent-type' as TGovernanceRuleType))
        .resolves.not.toThrow();
    });

    it('should allow unregistering built-in validators', async () => {
      // The implementation allows unregistering built-in validators
      await expect(factory.unregisterValidator('compliance-check'))
        .resolves.not.toThrow();

      // Verify it was unregistered
      const availableTypes = await factory.getAvailableValidatorTypes();
      expect(availableTypes).not.toContain('compliance-check');
    });

    it('should clean up instances when unregistering validator', async () => {
      // Register and create instances
      await factory.registerValidator('custom-rule', MockCustomValidator);
      const config = createTestValidatorConfiguration({ type: 'custom-rule' });

      await factory.createValidator('custom-rule', config);
      await factory.createValidator('custom-rule', config);

      // Unregister should clean up instances
      await factory.unregisterValidator('custom-rule');

      // Verify type is no longer available
      const availableTypes = await factory.getAvailableValidatorTypes();
      expect(availableTypes).not.toContain('custom-rule');
    });
  });

  // ============================================================================
  // EDGE CASES AND BOUNDARY TESTS
  // ============================================================================

  describe('Edge Cases and Boundary Tests', () => {
    it('should handle empty validator configuration', async () => {
      const emptyConfig = {
        type: 'compliance-check',
        parameters: {
          strictness: 'moderate' as const,
          timeout: 30000,
          retry: {
            maxAttempts: 3,
            delayMs: 1000,
            backoffStrategy: 'exponential' as const,
            maxDelayMs: 10000
          },
          customRules: {}
        },
        options: {
          caching: true,
          parallel: false,
          maxConcurrent: 1,
          detailedLogging: false
        },
        metadata: {}
      };

      const validator = await factory.createValidator('compliance-check', emptyConfig);
      expect(validator).toBeDefined();
    });

    it('should handle validator configuration with extreme values', async () => {
      const extremeConfig = createTestValidatorConfiguration({
        type: 'compliance-check',
        parameters: {
          strictness: 'strict',
          timeout: 1, // Very short timeout
          retry: {
            maxAttempts: 100, // High retry count
            delayMs: 1,
            backoffStrategy: 'exponential',
            maxDelayMs: 1000000 // Very long max delay
          },
          customRules: {}
        },
        options: {
          caching: false,
          parallel: true,
          maxConcurrent: 1000, // High concurrency
          detailedLogging: true
        }
      });

      const validator = await factory.createValidator('compliance-check', extremeConfig);
      expect(validator).toBeDefined();
    });

    it('should handle rapid validator creation and destruction', async () => {
      const config = createTestValidatorConfiguration({ type: 'compliance-check' });

      // Create validators within the limit (MAX_VALIDATORS_PER_TYPE = 10)
      for (let i = 0; i < 5; i++) {
        const validator = await factory.createValidator('compliance-check', config);
        expect(validator).toBeDefined();

        // Use the validator
        const rule = createTestGovernanceRule({ type: 'compliance-check' });
        const result = await validator.validateRule(rule, { test: true });
        expect(result.status).toBe('passed');
      }
    });

    it('should maintain consistency under stress', async () => {
      const config = createTestValidatorConfiguration({ type: 'compliance-check' });

      // Perform multiple operations concurrently
      const operations = [
        factory.createValidator('compliance-check', config),
        factory.getAvailableValidatorTypes(),
        factory.validate(),
        factory.createValidator('compliance-check', config),
        factory.getAvailableValidatorTypes()
      ];

      const results = await Promise.all(operations);

      // Verify all operations completed successfully
      expect(results[0]).toBeDefined(); // First validator
      expect(results[1]).toContain('compliance-check'); // Available types
      expect(results[2].status).toBe('valid'); // Validation
      expect(results[3]).toBeDefined(); // Second validator
      expect(results[4]).toContain('compliance-check'); // Available types again
    });
  });

  // ============================================================================
  // CLEANUP AND SHUTDOWN TESTS
  // ============================================================================

  describe('Cleanup and Shutdown', () => {
    it('should shutdown cleanly without errors', async () => {
      const config = createTestValidatorConfiguration({ type: 'compliance-check' });

      // Create some validators
      await factory.createValidator('compliance-check', config);
      await factory.createValidator('compliance-check', config);

      // Shutdown should complete without errors
      await expect(factory.shutdown()).resolves.not.toThrow();
    });

    it('should clear all caches and registries on shutdown', async () => {
      const config = createTestValidatorConfiguration({ type: 'compliance-check' });

      // Create validators and register custom types
      await factory.createValidator('compliance-check', config);
      await factory.registerValidator('custom-rule', MockCustomValidator);

      // Shutdown
      await factory.shutdown();

      // Verify factory clears internal state (test indirectly through behavior)
      // After shutdown, the factory should have cleared its internal maps
      // We can't directly test this due to private properties, but we can verify
      // that the factory completed shutdown without errors
      expect(true).toBe(true); // Shutdown completed successfully
    });

    it('should handle multiple shutdown calls gracefully', async () => {
      await factory.shutdown();

      // Second shutdown should not throw
      await expect(factory.shutdown()).resolves.not.toThrow();
    });
  });

  // ============================================================================
  // SURGICAL PRECISION TESTS FOR SPECIFIC CODE PATHS
  // ============================================================================

  describe('Surgical Precision Coverage Tests', () => {
    it('should handle validator input validation edge cases', async () => {
      // Test _validateValidatorInputs method with various invalid inputs
      const validConfig = createTestValidatorConfiguration({ type: 'compliance-check' });

      // Test with empty string rule type
      await expect(factory.createValidator('' as TGovernanceRuleType, validConfig))
        .rejects.toThrow('Valid rule type is required');

      // Test with non-object configuration
      await expect(factory.createValidator('compliance-check', 'invalid' as any))
        .rejects.toThrow('Valid validator configuration is required');
    });

    it('should test validator ID generation uniqueness', async () => {
      const config = createTestValidatorConfiguration({ type: 'compliance-check' });

      // Create multiple validators and verify unique IDs (indirect test)
      const validators = [];
      for (let i = 0; i < 5; i++) {
        const validator = await factory.createValidator('compliance-check', config);
        validators.push(validator);
      }

      // All validators should be unique instances
      expect(validators).toHaveLength(5);
      validators.forEach(validator => {
        expect(validator).toBeDefined();
      });
    });

    it('should handle performance metrics initialization edge cases', async () => {
      // Test metrics initialization by creating validators with different configurations
      const strictConfig = createTestValidatorConfiguration({
        type: 'compliance-check',
        parameters: {
          strictness: 'strict',
          timeout: 5000,
          retry: {
            maxAttempts: 1,
            delayMs: 100,
            backoffStrategy: 'linear',
            maxDelayMs: 1000
          },
          customRules: { strict: true }
        }
      });

      const validator = await factory.createValidator('compliance-check', strictConfig);
      expect(validator).toBeDefined();

      // Use validator to trigger metrics updates
      const rule = createTestGovernanceRule({ type: 'compliance-check' });
      const result = await validator.validateRule(rule, { test: true });
      expect(result.status).toBe('passed');
    });

    it('should test registry entry creation with all properties', async () => {
      // Register validator with comprehensive configuration
      await factory.registerValidator('custom-rule', MockCustomValidator);

      // Create validator to trigger registry entry usage
      const config = createTestValidatorConfiguration({
        type: 'custom-rule',
        parameters: {
          strictness: 'lenient',
          timeout: 60000,
          retry: {
            maxAttempts: 10,
            delayMs: 5000,
            backoffStrategy: 'exponential',
            maxDelayMs: 30000
          },
          customRules: {
            customParam1: 'value1',
            customParam2: 42,
            customParam3: true
          }
        },
        options: {
          caching: false,
          parallel: true,
          maxConcurrent: 5,
          detailedLogging: true
        },
        metadata: {
          version: '2.0.0',
          author: 'Test Suite',
          description: 'Comprehensive test validator'
        }
      });

      const validator = await factory.createValidator('custom-rule', config);
      expect(validator).toBeDefined();
      expect(validator).toBeInstanceOf(MockCustomValidator);
    });

    it('should handle cleanup interval startup edge cases', async () => {
      // Create a new factory to test initialization paths
      const newFactory = new GovernanceRuleValidatorFactory();

      // Initialize should start cleanup interval
      await newFactory.initialize();

      // Verify factory is operational
      const validation = await newFactory.validate();
      expect(validation.componentId).toBe('governance-rule-validator-factory');

      await newFactory.shutdown();
    });

    it('should test validator configuration validation edge cases', async () => {
      // Test with configuration containing null values - the implementation is lenient
      const configWithNulls = {
        type: 'compliance-check',
        parameters: {
          strictness: 'moderate' as const,
          timeout: 30000,
          retry: null as any,
          customRules: null as any
        },
        options: {
          caching: true,
          parallel: false,
          maxConcurrent: 1,
          detailedLogging: false
        },
        metadata: null as any
      };

      // The implementation is lenient and accepts this configuration
      const validator = await factory.createValidator('compliance-check', configWithNulls);
      expect(validator).toBeDefined();
    });

    it('should test built-in validator registration error handling', async () => {
      // Create factory without initialization to test registration failure paths
      const newFactory = new GovernanceRuleValidatorFactory();

      // Test initialization without mocking crypto (avoid property redefinition issues)
      await newFactory.initialize();

      // Verify initialization completed
      const availableTypes = await newFactory.getAvailableValidatorTypes();
      expect(availableTypes.length).toBeGreaterThan(0);

      await newFactory.shutdown();
    });

    it('should test error handling in doTrack method', async () => {
      // Test the tracking functionality
      const trackingData = {
        validatorCreated: true,
        validatorType: 'compliance-check',
        timestamp: new Date()
      };

      // Call track method (inherited from BaseTrackingService)
      await expect(factory.track(trackingData)).resolves.not.toThrow();
    });

    it('should test service name and version getters', async () => {
      // Verify service metadata through validation
      const validation = await factory.validate();

      expect(validation.componentId).toBe('governance-rule-validator-factory');
      expect(validation).toBeDefined();

      // Test that factory reports correct component type
      expect(validation.componentId).toMatch(/governance-rule-validator-factory/);
    });

    it('should handle maximum concurrent validations limit', async () => {
      // This tests the MAX_CONCURRENT_VALIDATIONS configuration
      const config = createTestValidatorConfiguration({ type: 'compliance-check' });

      // Create validator and test concurrent usage
      const validator = await factory.createValidator('compliance-check', config);
      const rule = createTestGovernanceRule({ type: 'compliance-check' });

      // Run multiple validations concurrently
      const validationPromises = [];
      for (let i = 0; i < 10; i++) {
        validationPromises.push(validator.validateRule(rule, { test: true, iteration: i }));
      }

      const results = await Promise.all(validationPromises);
      expect(results).toHaveLength(10);
      results.forEach(result => {
        expect(result.status).toBe('passed');
      });
    });

    it('should test cache health validation with large cache size', async () => {
      // Create a new factory to test cache validation
      const newFactory = new GovernanceRuleValidatorFactory();
      await newFactory.initialize();

      // Simulate large cache by creating many validators (indirect cache usage)
      const config = createTestValidatorConfiguration({ type: 'compliance-check' });
      for (let i = 0; i < 5; i++) {
        await newFactory.createValidator('compliance-check', config);
      }

      const validation = await newFactory.validate();
      expect(validation).toBeDefined();
      expect(validation.status).toBe('valid');

      await newFactory.shutdown();
    });

    it('should test performance metrics validation', async () => {
      // Create validators to generate performance metrics
      const config = createTestValidatorConfiguration({ type: 'compliance-check' });
      const validator = await factory.createValidator('compliance-check', config);

      // Use validator to generate metrics
      const rule = createTestGovernanceRule({ type: 'compliance-check' });
      await validator.validateRule(rule, { test: true });

      // Validate should include performance metrics validation
      const validation = await factory.validate();
      expect(validation.status).toBe('valid');
    });

    it('should test getMetrics method functionality', async () => {
      // Create validators to generate metrics
      const config = createTestValidatorConfiguration({ type: 'compliance-check' });
      const validator = await factory.createValidator('compliance-check', config);

      // Use validator to generate some activity
      const rule = createTestGovernanceRule({ type: 'compliance-check' });
      await validator.validateRule(rule, { test: true });

      // Test getMetrics method (indirect test through successful operation)
      const availableTypes = await factory.getAvailableValidatorTypes();
      expect(availableTypes).toContain('compliance-check');
    });

    it('should test cleanup interval and performance metrics initialization', async () => {
      // Test the _startCleanupInterval and _initializePerformanceMetrics methods
      const newFactory = new GovernanceRuleValidatorFactory();

      // Initialize should call both methods
      await newFactory.initialize();

      // Verify factory is operational (indirect test)
      const validation = await newFactory.validate();
      expect(validation.componentId).toBe('governance-rule-validator-factory');

      await newFactory.shutdown();
    });

    it('should test validator ID generation with crypto randomBytes', async () => {
      // Test the _generateValidatorId method by creating multiple validators
      const config = createTestValidatorConfiguration({ type: 'compliance-check' });

      const validators = [];
      for (let i = 0; i < 3; i++) {
        const validator = await factory.createValidator('compliance-check', config);
        validators.push(validator);
      }

      // All validators should be unique instances (indirect test of ID generation)
      expect(validators).toHaveLength(3);
      validators.forEach(validator => {
        expect(validator).toBeDefined();
        expect(typeof validator.validateRule).toBe('function');
      });
    });

    it('should test error handling in doValidate method', async () => {
      // Test validation with various factory states
      const validation = await factory.validate();

      expect(validation).toBeDefined();
      expect(validation.componentId).toBe('governance-rule-validator-factory');
      expect(validation.status).toBe('valid');
      expect(Array.isArray(validation.errors)).toBe(true);
      expect(Array.isArray(validation.warnings)).toBe(true);
    });

    it('should test comprehensive validator configuration edge cases', async () => {
      // Test with various configuration combinations
      const configs = [
        createTestValidatorConfiguration({
          type: 'authority-validation',
          parameters: {
            strictness: 'strict',
            timeout: 1000,
            retry: {
              maxAttempts: 1,
              delayMs: 100,
              backoffStrategy: 'linear',
              maxDelayMs: 500
            },
            customRules: { test: 'value' }
          }
        }),
        createTestValidatorConfiguration({
          type: 'security-policy',
          parameters: {
            strictness: 'lenient',
            timeout: 60000,
            retry: {
              maxAttempts: 10,
              delayMs: 5000,
              backoffStrategy: 'exponential',
              maxDelayMs: 30000
            },
            customRules: {}
          }
        })
      ];

      for (const config of configs) {
        const validator = await factory.createValidator(config.type, config);
        expect(validator).toBeDefined();

        // Test validator functionality
        const rule = createTestGovernanceRule({ type: config.type });
        const result = await validator.validateRule(rule, { test: true });
        expect(result.status).toBe('passed');
      }
    });
  });

  // ============================================================================
  // SURGICAL PRECISION COVERAGE ENHANCEMENT TESTS (95%+ TARGET)
  // ============================================================================

  describe('Surgical Precision Coverage Enhancement - Cache Validation', () => {
    it('should trigger cache size warning when cache exceeds 10,000 entries (Line 863)', async () => {
      // Create a new factory to manipulate cache directly
      const testFactory = new GovernanceRuleValidatorFactory();
      await testFactory.initialize();

      try {
        // Access private _validationCache using type assertion for surgical precision testing
        const privateFactory = testFactory as any;
        const validationCache = privateFactory._validationCache;

        // Create cache entries exceeding the 10,000 limit to trigger line 863
        const maxCacheSize = 10000;
        const excessEntries = 500; // Create 10,500 entries total

        for (let i = 0; i < maxCacheSize + excessEntries; i++) {
          const cacheEntry = {
            ruleId: `test-rule-${i}`,
            target: JSON.stringify({ test: `target-${i}` }),
            result: {
              validationId: `val-${i}`,
              criteriaId: `criteria-${i}`,
              status: 'passed' as const,
              score: 85,
              message: `Cache entry ${i}`,
              details: {
                expected: 'test',
                actual: 'test',
                operator: 'equals',
                context: { ruleId: `test-rule-${i}` }
              },
              timestamp: new Date()
            },
            createdAt: new Date(),
            expiresAt: new Date(Date.now() + 3600000), // 1 hour from now
            accessCount: 1,
            lastAccessed: new Date()
          };

          validationCache.set(`cache-key-${i}`, cacheEntry);
        }

        // Verify cache size exceeds limit
        expect(validationCache.size).toBeGreaterThan(maxCacheSize);

        // Trigger validation to hit line 863
        const validation = await testFactory.validate();

        // Verify warning was generated for large cache size
        expect(validation.warnings).toBeDefined();
        expect(Array.isArray(validation.warnings)).toBe(true);
        expect(validation.warnings.some((warning: string) =>
          warning.includes('Large cache size:') && warning.includes(`${validationCache.size}`)
        )).toBe(true);

      } finally {
        await testFactory.shutdown();
      }
    });

    it('should trigger expired cache entries warning when >100 expired entries exist (Lines 876-877, 882)', async () => {
      // Create a new factory to manipulate cache directly
      const testFactory = new GovernanceRuleValidatorFactory();
      await testFactory.initialize();

      try {
        // Access private _validationCache using type assertion for surgical precision testing
        const privateFactory = testFactory as any;
        const validationCache = privateFactory._validationCache;

        // Create expired cache entries to trigger lines 876-877 and 882
        const expiredEntryCount = 150; // More than 100 to trigger warning
        const pastDate = new Date(Date.now() - 7200000); // 2 hours ago (expired)

        for (let i = 0; i < expiredEntryCount; i++) {
          const expiredCacheEntry = {
            ruleId: `expired-rule-${i}`,
            target: JSON.stringify({ test: `expired-target-${i}` }),
            result: {
              validationId: `expired-val-${i}`,
              criteriaId: `expired-criteria-${i}`,
              status: 'passed' as const,
              score: 85,
              message: `Expired cache entry ${i}`,
              details: {
                expected: 'test',
                actual: 'test',
                operator: 'equals',
                context: { ruleId: `expired-rule-${i}` }
              },
              timestamp: pastDate
            },
            createdAt: pastDate,
            expiresAt: pastDate, // Already expired
            accessCount: 1,
            lastAccessed: pastDate
          };

          validationCache.set(`expired-cache-key-${i}`, expiredCacheEntry);
        }

        // Add some non-expired entries to ensure we're testing the specific condition
        for (let i = 0; i < 10; i++) {
          const validCacheEntry = {
            ruleId: `valid-rule-${i}`,
            target: JSON.stringify({ test: `valid-target-${i}` }),
            result: {
              validationId: `valid-val-${i}`,
              criteriaId: `valid-criteria-${i}`,
              status: 'passed' as const,
              score: 85,
              message: `Valid cache entry ${i}`,
              details: {
                expected: 'test',
                actual: 'test',
                operator: 'equals',
                context: { ruleId: `valid-rule-${i}` }
              },
              timestamp: new Date()
            },
            createdAt: new Date(),
            expiresAt: new Date(Date.now() + 3600000), // 1 hour from now
            accessCount: 1,
            lastAccessed: new Date()
          };

          validationCache.set(`valid-cache-key-${i}`, validCacheEntry);
        }

        // Verify we have the expected number of entries
        expect(validationCache.size).toBe(expiredEntryCount + 10);

        // Trigger validation to hit lines 876-877 and 882
        const validation = await testFactory.validate();

        // Verify warning was generated for many expired cache entries
        expect(validation.warnings).toBeDefined();
        expect(Array.isArray(validation.warnings)).toBe(true);
        expect(validation.warnings.some((warning: string) =>
          warning.includes('Many expired cache entries:') && warning.includes(`${expiredEntryCount}`)
        )).toBe(true);

      } finally {
        await testFactory.shutdown();
      }
    });

    it('should test cache health validation with mixed expired and valid entries', async () => {
      // Test edge case where expired count is exactly at the threshold
      const testFactory = new GovernanceRuleValidatorFactory();
      await testFactory.initialize();

      try {
        const privateFactory = testFactory as any;
        const validationCache = privateFactory._validationCache;

        // Create exactly 100 expired entries (should not trigger warning)
        const expiredEntryCount = 100;
        const pastDate = new Date(Date.now() - 3600000); // 1 hour ago

        for (let i = 0; i < expiredEntryCount; i++) {
          const expiredCacheEntry = {
            ruleId: `threshold-expired-rule-${i}`,
            target: JSON.stringify({ test: `threshold-target-${i}` }),
            result: {
              validationId: `threshold-val-${i}`,
              criteriaId: `threshold-criteria-${i}`,
              status: 'passed' as const,
              score: 85,
              message: `Threshold expired entry ${i}`,
              details: {
                expected: 'test',
                actual: 'test',
                operator: 'equals',
                context: { ruleId: `threshold-expired-rule-${i}` }
              },
              timestamp: pastDate
            },
            createdAt: pastDate,
            expiresAt: pastDate,
            accessCount: 1,
            lastAccessed: pastDate
          };

          validationCache.set(`threshold-expired-${i}`, expiredCacheEntry);
        }

        // Trigger validation - should NOT generate warning for exactly 100 expired entries
        const validation = await testFactory.validate();

        // Verify no warning for exactly 100 expired entries
        expect(validation.warnings).toBeDefined();
        expect(Array.isArray(validation.warnings)).toBe(true);
        expect(validation.warnings.some((warning: string) =>
          warning.includes('Many expired cache entries:')
        )).toBe(false);

      } finally {
        await testFactory.shutdown();
      }
    });
  });

  describe('Surgical Precision Coverage Enhancement - Performance Metrics', () => {
    it('should trigger high average validation time warning when >5000ms (Line 831)', async () => {
      // Create a new factory to manipulate performance metrics directly
      const testFactory = new GovernanceRuleValidatorFactory();
      await testFactory.initialize();

      try {
        // Access private _globalMetrics using type assertion for surgical precision testing
        const privateFactory = testFactory as any;

        // Manipulate global metrics to trigger high validation time warning
        privateFactory._globalMetrics.avgValidationTimeMs = 6500; // Exceeds 5000ms threshold
        privateFactory._globalMetrics.totalValidationsPerformed = 50;
        privateFactory._globalMetrics.cacheHitRate = 75; // Good cache hit rate

        // Trigger validation to hit line 831
        const validation = await testFactory.validate();

        // Verify warning was generated for high average validation time
        expect(validation.warnings).toBeDefined();
        expect(Array.isArray(validation.warnings)).toBe(true);
        expect(validation.warnings.some((warning: string) =>
          warning.includes('High average validation time:') && warning.includes('6500ms')
        )).toBe(true);

      } finally {
        await testFactory.shutdown();
      }
    });

    it('should trigger low cache hit rate warning when <50% and >100 validations (Line 842)', async () => {
      // Create a new factory to manipulate performance metrics directly
      const testFactory = new GovernanceRuleValidatorFactory();
      await testFactory.initialize();

      try {
        // Access private _globalMetrics using type assertion for surgical precision testing
        const privateFactory = testFactory as any;

        // Manipulate global metrics to trigger low cache hit rate warning
        privateFactory._globalMetrics.avgValidationTimeMs = 2000; // Good validation time
        privateFactory._globalMetrics.totalValidationsPerformed = 150; // More than 100
        privateFactory._globalMetrics.cacheHitRate = 35; // Less than 50%
        privateFactory._globalMetrics.totalValidationsCached = 52; // 35% of 150

        // Trigger validation to hit line 842
        const validation = await testFactory.validate();

        // Verify warning was generated for low cache hit rate
        expect(validation.warnings).toBeDefined();
        expect(Array.isArray(validation.warnings)).toBe(true);
        expect(validation.warnings.some((warning: string) =>
          warning.includes('Low cache hit rate:') && warning.includes('35%')
        )).toBe(true);

      } finally {
        await testFactory.shutdown();
      }
    });

    it('should not trigger cache hit rate warning when validations ≤100 (Edge case test)', async () => {
      // Test edge case where cache hit rate is low but total validations ≤ 100
      const testFactory = new GovernanceRuleValidatorFactory();
      await testFactory.initialize();

      try {
        const privateFactory = testFactory as any;

        // Set metrics where cache hit rate is low but validations ≤ 100
        privateFactory._globalMetrics.avgValidationTimeMs = 2000;
        privateFactory._globalMetrics.totalValidationsPerformed = 100; // Exactly 100
        privateFactory._globalMetrics.cacheHitRate = 25; // Very low, but shouldn't trigger warning
        privateFactory._globalMetrics.totalValidationsCached = 25;

        // Trigger validation
        const validation = await testFactory.validate();

        // Verify NO warning for low cache hit rate when validations ≤ 100
        expect(validation.warnings).toBeDefined();
        expect(Array.isArray(validation.warnings)).toBe(true);
        expect(validation.warnings.some((warning: string) =>
          warning.includes('Low cache hit rate:')
        )).toBe(false);

      } finally {
        await testFactory.shutdown();
      }
    });

    it('should test performance metrics edge cases with boundary values', async () => {
      // Test exact boundary conditions for performance metrics
      const testFactory = new GovernanceRuleValidatorFactory();
      await testFactory.initialize();

      try {
        const privateFactory = testFactory as any;

        // Test exact threshold values
        privateFactory._globalMetrics.avgValidationTimeMs = 5000; // Exactly at threshold
        privateFactory._globalMetrics.totalValidationsPerformed = 101; // Just over 100
        privateFactory._globalMetrics.cacheHitRate = 50; // Exactly at threshold
        privateFactory._globalMetrics.totalValidationsCached = 50;

        // Trigger validation
        const validation = await testFactory.validate();

        // Verify no warnings at exact thresholds
        expect(validation.warnings).toBeDefined();
        expect(Array.isArray(validation.warnings)).toBe(true);
        expect(validation.warnings.some((warning: string) =>
          warning.includes('High average validation time:') || warning.includes('Low cache hit rate:')
        )).toBe(false);

      } finally {
        await testFactory.shutdown();
      }
    });

    it('should test combined performance and cache warnings', async () => {
      // Test scenario where both performance warnings are triggered
      const testFactory = new GovernanceRuleValidatorFactory();
      await testFactory.initialize();

      try {
        const privateFactory = testFactory as any;

        // Set metrics to trigger both warnings
        privateFactory._globalMetrics.avgValidationTimeMs = 7500; // High validation time
        privateFactory._globalMetrics.totalValidationsPerformed = 200; // High validation count
        privateFactory._globalMetrics.cacheHitRate = 30; // Low cache hit rate
        privateFactory._globalMetrics.totalValidationsCached = 60;

        // Trigger validation
        const validation = await testFactory.validate();

        // Verify both warnings are generated
        expect(validation.warnings).toBeDefined();
        expect(Array.isArray(validation.warnings)).toBe(true);
        expect(validation.warnings.some((warning: string) =>
          warning.includes('High average validation time:') && warning.includes('7500ms')
        )).toBe(true);
        expect(validation.warnings.some((warning: string) =>
          warning.includes('Low cache hit rate:') && warning.includes('30%')
        )).toBe(true);

      } finally {
        await testFactory.shutdown();
      }
    });
  });

  describe('Surgical Precision Coverage Enhancement - Concurrent Operations', () => {
    it('should handle concurrent validator creation, registration, and unregistration', async () => {
      // Test complex concurrent operations to stress test the factory
      const testFactory = new GovernanceRuleValidatorFactory();
      await testFactory.initialize();

      try {
        const config = createTestValidatorConfiguration({ type: 'compliance-check' });

        // Create concurrent operations array
        const concurrentOperations = [];

        // Add validator creation operations
        for (let i = 0; i < 5; i++) {
          concurrentOperations.push(
            testFactory.createValidator('compliance-check', config)
          );
        }

        // Add validator registration operations
        for (let i = 0; i < 3; i++) {
          const dynamicValidatorClass = createDynamicValidatorClass(`concurrent-type-${i}` as any);
          concurrentOperations.push(
            testFactory.registerValidator(`concurrent-type-${i}` as any, dynamicValidatorClass)
          );
        }

        // Add validation operations
        concurrentOperations.push(testFactory.validate());
        concurrentOperations.push(testFactory.getAvailableValidatorTypes());
        concurrentOperations.push(testFactory.getMetrics());

        // Execute all operations concurrently
        const results = await Promise.all(concurrentOperations);

        // Verify all operations completed successfully
        expect(results).toHaveLength(11); // 5 + 3 + 3 operations

        // Verify validators were created
        for (let i = 0; i < 5; i++) {
          expect(results[i]).toBeDefined();
          expect(typeof (results[i] as any).validateRule).toBe('function');
        }

        // Verify registrations completed (no return value, just no errors)
        // Results 5, 6, 7 are registration operations

        // Verify other operations
        const validation = results[8] as any;
        expect(validation.componentId).toBe('governance-rule-validator-factory');

        const availableTypes = results[9] as string[];
        expect(availableTypes).toContain('compliance-check');
        expect(availableTypes).toContain('concurrent-type-0');

        const metrics = results[10] as any;
        expect(metrics).toBeDefined();

      } finally {
        await testFactory.shutdown();
      }
    });

    it('should maintain data consistency under high concurrent load', async () => {
      // Stress test with high concurrent validator operations
      const testFactory = new GovernanceRuleValidatorFactory();
      await testFactory.initialize();

      try {
        const config = createTestValidatorConfiguration({ type: 'compliance-check' });

        // Create high concurrent load
        const highLoadOperations = [];

        // Create many validators concurrently (within limits)
        for (let i = 0; i < 8; i++) {
          highLoadOperations.push(
            testFactory.createValidator('compliance-check', config)
          );
        }

        // Add multiple validation checks
        for (let i = 0; i < 5; i++) {
          highLoadOperations.push(testFactory.validate());
        }

        // Add multiple metrics requests
        for (let i = 0; i < 3; i++) {
          highLoadOperations.push(testFactory.getMetrics());
        }

        // Add multiple type requests
        for (let i = 0; i < 4; i++) {
          highLoadOperations.push(testFactory.getAvailableValidatorTypes());
        }

        // Execute all operations concurrently
        const startTime = Date.now();
        const results = await Promise.all(highLoadOperations);
        const endTime = Date.now();

        // Verify performance meets enterprise standards
        expect(endTime - startTime).toBeLessThan(5000); // <5 seconds

        // Verify all operations completed successfully
        expect(results).toHaveLength(20); // 8 + 5 + 3 + 4 operations

        // Verify validators were created successfully
        for (let i = 0; i < 8; i++) {
          expect(results[i]).toBeDefined();
          expect(typeof (results[i] as any).validateRule).toBe('function');
        }

        // Verify validation results are consistent
        for (let i = 8; i < 13; i++) {
          const validation = results[i] as any;
          expect(validation.componentId).toBe('governance-rule-validator-factory');
          expect(validation.status).toBe('valid');
        }

        // Verify metrics are consistent
        for (let i = 13; i < 16; i++) {
          const metrics = results[i] as any;
          expect(metrics).toBeDefined();
          // The metrics structure may vary, so just verify it's an object
          expect(typeof metrics).toBe('object');
        }

        // Verify available types are consistent
        for (let i = 16; i < 20; i++) {
          const types = results[i] as string[];
          expect(types).toContain('compliance-check');
          expect(types).toContain('authority-validation');
          expect(types).toContain('security-policy');
        }

      } finally {
        await testFactory.shutdown();
      }
    });

    it('should handle concurrent registration and unregistration operations', async () => {
      // Test concurrent registration and unregistration to test thread safety
      const testFactory = new GovernanceRuleValidatorFactory();
      await testFactory.initialize();

      try {
        // Register multiple custom validators concurrently
        const registrationPromises = [];
        for (let i = 0; i < 5; i++) {
          const dynamicValidatorClass = createDynamicValidatorClass(`stress-type-${i}` as any);
          registrationPromises.push(
            testFactory.registerValidator(`stress-type-${i}` as any, dynamicValidatorClass)
          );
        }

        await Promise.all(registrationPromises);

        // Verify all types were registered
        const typesAfterRegistration = await testFactory.getAvailableValidatorTypes();
        for (let i = 0; i < 5; i++) {
          expect(typesAfterRegistration).toContain(`stress-type-${i}`);
        }

        // Now unregister some concurrently while creating validators
        const mixedOperations = [];

        // Unregister some types
        for (let i = 0; i < 3; i++) {
          mixedOperations.push(
            testFactory.unregisterValidator(`stress-type-${i}` as any)
          );
        }

        // Create validators for remaining types
        const config3 = createTestValidatorConfiguration({ type: 'stress-type-3' as any });
        const config4 = createTestValidatorConfiguration({ type: 'stress-type-4' as any });
        mixedOperations.push(
          testFactory.createValidator('stress-type-3' as any, config3)
        );
        mixedOperations.push(
          testFactory.createValidator('stress-type-4' as any, config4)
        );

        // Execute mixed operations concurrently
        await Promise.all(mixedOperations);

        // Verify final state is consistent
        const finalTypes = await testFactory.getAvailableValidatorTypes();
        expect(finalTypes).not.toContain('stress-type-0');
        expect(finalTypes).not.toContain('stress-type-1');
        expect(finalTypes).not.toContain('stress-type-2');
        expect(finalTypes).toContain('stress-type-3');
        expect(finalTypes).toContain('stress-type-4');

      } finally {
        await testFactory.shutdown();
      }
    });
  });

  describe('Surgical Precision Coverage Enhancement - Advanced Edge Cases', () => {
    it('should test comprehensive cache and performance metrics integration', async () => {
      // Test integration of cache and performance metrics for complete coverage
      const testFactory = new GovernanceRuleValidatorFactory();
      await testFactory.initialize();

      try {
        const privateFactory = testFactory as any;

        // Create a scenario with both cache and performance issues
        // 1. Add large cache with many expired entries
        const validationCache = privateFactory._validationCache;
        const expiredCount = 120;
        const validCount = 9900; // Total will be 10,020 (over 10,000 limit)

        // Add expired entries
        for (let i = 0; i < expiredCount; i++) {
          const expiredEntry = {
            ruleId: `integrated-expired-${i}`,
            target: JSON.stringify({ test: `expired-${i}` }),
            result: {
              validationId: `exp-val-${i}`,
              criteriaId: `exp-criteria-${i}`,
              status: 'passed' as const,
              score: 85,
              message: `Expired integrated entry ${i}`,
              details: {
                expected: 'test',
                actual: 'test',
                operator: 'equals',
                context: { ruleId: `integrated-expired-${i}` }
              },
              timestamp: new Date(Date.now() - 7200000)
            },
            createdAt: new Date(Date.now() - 7200000),
            expiresAt: new Date(Date.now() - 3600000), // Expired 1 hour ago
            accessCount: 1,
            lastAccessed: new Date(Date.now() - 3600000)
          };
          validationCache.set(`integrated-expired-${i}`, expiredEntry);
        }

        // Add valid entries to exceed cache size limit
        for (let i = 0; i < validCount; i++) {
          const validEntry = {
            ruleId: `integrated-valid-${i}`,
            target: JSON.stringify({ test: `valid-${i}` }),
            result: {
              validationId: `val-${i}`,
              criteriaId: `criteria-${i}`,
              status: 'passed' as const,
              score: 85,
              message: `Valid integrated entry ${i}`,
              details: {
                expected: 'test',
                actual: 'test',
                operator: 'equals',
                context: { ruleId: `integrated-valid-${i}` }
              },
              timestamp: new Date()
            },
            createdAt: new Date(),
            expiresAt: new Date(Date.now() + 3600000),
            accessCount: 1,
            lastAccessed: new Date()
          };
          validationCache.set(`integrated-valid-${i}`, validEntry);
        }

        // 2. Set performance metrics to trigger warnings
        privateFactory._globalMetrics.avgValidationTimeMs = 8000; // High validation time
        privateFactory._globalMetrics.totalValidationsPerformed = 250;
        privateFactory._globalMetrics.cacheHitRate = 25; // Low cache hit rate
        privateFactory._globalMetrics.totalValidationsCached = 62;

        // Trigger comprehensive validation
        const validation = await testFactory.validate();

        // Verify all warnings are generated
        expect(validation.warnings).toBeDefined();
        expect(Array.isArray(validation.warnings)).toBe(true);

        // Should have cache size warning
        expect(validation.warnings.some((warning: string) =>
          warning.includes('Large cache size:')
        )).toBe(true);

        // Should have expired entries warning
        expect(validation.warnings.some((warning: string) =>
          warning.includes('Many expired cache entries:')
        )).toBe(true);

        // Should have high validation time warning
        expect(validation.warnings.some((warning: string) =>
          warning.includes('High average validation time:')
        )).toBe(true);

        // Should have low cache hit rate warning
        expect(validation.warnings.some((warning: string) =>
          warning.includes('Low cache hit rate:')
        )).toBe(true);

        // Verify we have at least 4 warnings
        expect(validation.warnings.length).toBeGreaterThanOrEqual(4);

      } finally {
        await testFactory.shutdown();
      }
    });

    it('should test factory state consistency during rapid operations', async () => {
      // Test factory state consistency with rapid create/validate/shutdown cycles
      const testFactory = new GovernanceRuleValidatorFactory();
      await testFactory.initialize();

      try {
        const config = createTestValidatorConfiguration({ type: 'compliance-check' });

        // Perform rapid operations to test state consistency
        for (let cycle = 0; cycle < 10; cycle++) {
          // Create validator
          const validator = await testFactory.createValidator('compliance-check', config);
          expect(validator).toBeDefined();

          // Use validator immediately
          const rule = createTestGovernanceRule({ type: 'compliance-check' });
          const result = await validator.validateRule(rule, { test: true, cycle });
          expect(result.status).toBe('passed');

          // Validate factory state
          const validation = await testFactory.validate();
          expect(validation.status).toBe('valid');

          // Get metrics
          const metrics = await testFactory.getMetrics();
          expect(metrics).toBeDefined();
          expect(typeof metrics).toBe('object');
        }

        // Verify final state is consistent
        const finalValidation = await testFactory.validate();
        expect(finalValidation.status).toBe('valid');

        const finalMetrics = await testFactory.getMetrics();
        expect(finalMetrics).toBeDefined();
        expect(typeof finalMetrics).toBe('object');

      } finally {
        await testFactory.shutdown();
      }
    });

    it('should test memory safety and resource cleanup under stress', async () => {
      // Test memory safety patterns under stress conditions
      const testFactory = new GovernanceRuleValidatorFactory();
      await testFactory.initialize();

      try {
        // Create multiple validator types and instances
        const memoryValidator1 = createDynamicValidatorClass('memory-test-1' as any);
        const memoryValidator2 = createDynamicValidatorClass('memory-test-2' as any);
        await testFactory.registerValidator('memory-test-1', memoryValidator1);
        await testFactory.registerValidator('memory-test-2', memoryValidator2);

        const configs = [
          createTestValidatorConfiguration({ type: 'compliance-check' }),
          createTestValidatorConfiguration({ type: 'authority-validation' }),
          createTestValidatorConfiguration({ type: 'security-policy' }),
          createTestValidatorConfiguration({ type: 'memory-test-1' as any }),
          createTestValidatorConfiguration({ type: 'memory-test-2' as any })
        ];

        // Create validators for each type
        const validators = [];
        for (const config of configs) {
          for (let i = 0; i < 2; i++) { // 2 validators per type
            const validator = await testFactory.createValidator(config.type, config);
            validators.push(validator);
          }
        }

        expect(validators).toHaveLength(10); // 5 types × 2 validators

        // Use all validators to generate activity
        const rule = createTestGovernanceRule();
        const validationPromises = validators.map(async (validator, index) => {
          const customRule = createTestGovernanceRule({
            type: validator.getSupportedTypes()[0],
            ruleId: `stress-rule-${index}`
          });
          return validator.validateRule(customRule, { test: true, index });
        });

        const results = await Promise.all(validationPromises);
        expect(results).toHaveLength(10);
        results.forEach(result => {
          expect(result.status).toBe('passed');
        });

        // Verify factory health under stress
        const stressValidation = await testFactory.validate();
        expect(stressValidation.status).toBe('valid');

        // Test cleanup of some validators
        await testFactory.unregisterValidator('memory-test-1' as any);
        await testFactory.unregisterValidator('memory-test-2' as any);

        // Verify cleanup was successful
        const typesAfterCleanup = await testFactory.getAvailableValidatorTypes();
        expect(typesAfterCleanup).not.toContain('memory-test-1');
        expect(typesAfterCleanup).not.toContain('memory-test-2');

      } finally {
        await testFactory.shutdown();
      }
    });

    it('should test comprehensive error recovery and resilience', async () => {
      // Test factory resilience to various error conditions
      const testFactory = new GovernanceRuleValidatorFactory();
      await testFactory.initialize();

      try {
        // Test recovery from registration errors
        class FailingValidator implements IGovernanceRuleValidator {
          constructor() {
            // Sometimes fail, sometimes succeed
            if (Math.random() > 0.5) {
              throw new Error('Random registration failure');
            }
          }

          public async validateRule(rule: TGovernanceRule, target: unknown): Promise<TRuleValidationResult> {
            return {
              validationId: 'failing-val',
              criteriaId: 'failing-criteria',
              status: 'passed',
              score: 85,
              message: 'Failing validator passed',
              details: {
                expected: 'test',
                actual: 'test',
                operator: 'equals',
                context: { ruleId: rule.ruleId }
              },
              timestamp: new Date()
            };
          }

          public getSupportedTypes(): TGovernanceRuleType[] {
            return ['failing-validator' as TGovernanceRuleType];
          }

          public async configure(configuration: TValidatorConfiguration): Promise<void> {
            // Configuration implementation
          }
        }

        // Attempt multiple registrations (some may fail)
        let successfulRegistrations = 0;
        for (let i = 0; i < 10; i++) {
          try {
            await testFactory.registerValidator(`resilience-test-${i}` as any, FailingValidator);
            successfulRegistrations++;
          } catch (error) {
            // Expected - some registrations will fail
            expect(error).toBeDefined();
          }
        }

        // Verify factory remains operational despite some failures
        const validation = await testFactory.validate();
        expect(validation.status).toBe('valid');

        // Verify successful registrations are available
        const availableTypes = await testFactory.getAvailableValidatorTypes();
        const resilientTypes = availableTypes.filter(type => type.startsWith('resilience-test-'));
        expect(resilientTypes.length).toBe(successfulRegistrations);

        // Test factory can still create validators for built-in types
        const config = createTestValidatorConfiguration({ type: 'compliance-check' });
        const validator = await testFactory.createValidator('compliance-check', config);
        expect(validator).toBeDefined();

      } finally {
        await testFactory.shutdown();
      }
    });
  });
});
