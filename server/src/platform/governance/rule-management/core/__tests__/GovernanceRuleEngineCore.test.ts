/**
 * @file Governance Rule Engine Core Test Suite - Enterprise Grade
 * @filepath server/src/platform/governance/rule-management/core/__tests__/GovernanceRuleEngineCore.test.ts
 * @component governance-rule-engine-core-test
 * @tier T1
 * @context foundation-context
 * @category Foundation Testing
 * @created 2025-08-28
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-status approved
 * @governance-compliance authority-validated
 *
 * 🚨 MEMORY SAFETY: BaseTrackingService inheritance and resource management testing
 * 🚨 ANTI-SIMPLIFICATION: Complete functionality testing with enhanced business value
 * 🚨 SURGICAL PRECISION: Line-by-line coverage targeting for 95%+ coverage
 * 🚨 OA FRAMEWORK: Enterprise standards compliance with proven methodologies
 */

import { GovernanceRuleEngineCore } from '../GovernanceRuleEngineCore';
import {
  TGovernanceRuleSet,
  TProcessingContext,
  TRuleProcessingR<PERSON>ult,
  TGovernanceRule,
  TGovernanceRuleType,
  TRuleExecutionResult,
  TRuleV<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TRuleExecutionStatus
} from '../../../../../../../shared/src/types/platform/governance/rule-management-types';
import {
  IGovernanceRuleEngineCore,
  IGovernanceService
} from '../../../../../../../shared/src/types/platform/governance/governance-interfaces';

// 🚨 JEST COMPATIBILITY: Import Jest compatibility utilities
import { JestCompatibilityUtils } from '../../../../../../../shared/src/base/utils/JestCompatibilityUtils';

// Mock dependencies for isolated testing
jest.mock('../../../../../../../shared/src/base/TimerCoordinationService', () => ({
  getTimerCoordinator: jest.fn(() => ({
    createCoordinatedInterval: jest.fn((callback, interval, name) => {
      const timerId = setInterval(callback, interval);
      return { timerId, name };
    }),
    createCoordinatedTimeout: jest.fn((callback, timeout, name) => {
      const timerId = setTimeout(callback, timeout);
      return { timerId, name };
    }),
    clearServiceTimers: jest.fn(),
    clearAllTimers: jest.fn(),
    getServiceTimers: jest.fn(() => []),
    isHealthy: jest.fn(() => true)
  }))
}));

jest.mock('crypto', () => ({
  createHash: jest.fn(() => ({
    update: jest.fn().mockReturnThis(),
    digest: jest.fn(() => 'mock-hash')
  })),
  randomBytes: jest.fn((size) => ({
    toString: jest.fn(() => 'mock-random-hex')
  }))
}));

// Set Jest timeout for complex operations
jest.setTimeout(120000); // 2 minutes for complex rule processing operations

describe('Enterprise GovernanceRuleEngineCore Test Suite', () => {
  let ruleEngineCore: GovernanceRuleEngineCore;
  let mockRuleSet: TGovernanceRuleSet;
  let mockProcessingContext: TProcessingContext;
  let mockGovernanceRule: TGovernanceRule;

  // 🚨 MEMORY SAFETY: Enhanced monitoring variables
  let initialMemoryUsage: NodeJS.MemoryUsage;
  let testStartTime: number;

  // 🚨 JEST COMPATIBILITY: Enhanced test utilities
  const flushPromises = () => new Promise(resolve => setImmediate(resolve));
  const waitForAsync = (ms: number = 10) => new Promise(resolve => setTimeout(resolve, ms));

  beforeEach(async () => {
    // 🚨 JEST COMPATIBILITY: Configure Jest compatibility for GovernanceRuleEngineCore tests
    JestCompatibilityUtils.configure({
      forceTestMode: true,
      maxDelaySteps: 10,
      baseStepDuration: 1
    });

    // 🚨 MEMORY SAFETY: Memory monitoring setup
    testStartTime = Date.now();
    initialMemoryUsage = process.memoryUsage();

    // Initialize test data
    mockGovernanceRule = {
      ruleId: 'test-rule-001',
      name: 'Test Governance Rule',
      description: 'Test rule for validation',
      type: 'validation' as TGovernanceRuleType,
      version: '1.0.0',
      status: 'active',
      priority: 1,
      configuration: {
        conditions: ['test.condition'],
        actions: ['test.action'],
        dependencies: [],
        timeout: 30000,
        retryPolicy: {
          maxRetries: 3,
          retryDelay: 1000,
          backoffMultiplier: 2
        }
      },
      metadata: {
        createdBy: 'test-system',
        createdAt: new Date().toISOString(),
        tags: ['test', 'validation']
      }
    };

    mockRuleSet = {
      ruleSetId: 'test-ruleset-001',
      name: 'Test Rule Set',
      description: 'Test rule set for validation',
      version: '1.0.0',
      rules: [mockGovernanceRule],
      configuration: {
        executionMode: 'sequential',
        timeout: 60000,
        retryPolicy: {
          maxRetries: 2,
          retryDelay: 500,
          backoffMultiplier: 1.5
        }
      },
      metadata: {
        createdBy: 'test-system',
        createdAt: new Date().toISOString(),
        environment: 'test'
      }
    };

    mockProcessingContext = {
      processingId: 'test-processing-001',
      environment: 'test',
      targetData: { test: 'data' },
      configuration: {
        timeout: 30000,
        parallel: false,
        validateOnly: false
      },
      metadata: {
        requestId: 'test-request-001',
        userId: 'test-user',
        timestamp: new Date().toISOString()
      }
    };

    // Create fresh instance for each test
    ruleEngineCore = new GovernanceRuleEngineCore();
    await ruleEngineCore.initialize();
    await flushPromises();
  });

  afterEach(async () => {
    // 🚨 MEMORY SAFETY: Cleanup and memory monitoring
    if (ruleEngineCore) {
      await ruleEngineCore.shutdown();
      await flushPromises();
    }

    // Memory usage validation
    const finalMemoryUsage = process.memoryUsage();
    const memoryGrowth = finalMemoryUsage.heapUsed - initialMemoryUsage.heapUsed;
    const testDuration = Date.now() - testStartTime;

    // Log memory usage for monitoring
    if (memoryGrowth > 10 * 1024 * 1024) { // > 10MB growth
      console.warn(`High memory growth detected: ${Math.round(memoryGrowth / 1024 / 1024)}MB in ${testDuration}ms`);
    }
  });

  // ============================================================================
  // ENTERPRISE INSTANTIATION & CONFIGURATION TESTS
  // ============================================================================

  describe('Enterprise Instantiation & Configuration', () => {
    test('should instantiate with enterprise-grade configuration', () => {
      expect(ruleEngineCore).toBeInstanceOf(GovernanceRuleEngineCore);
      expect(ruleEngineCore).toBeDefined();
      
      // Verify interface compliance
      const engineInterface: IGovernanceRuleEngineCore = ruleEngineCore;
      const serviceInterface: IGovernanceService = ruleEngineCore;
      expect(engineInterface).toBeDefined();
      expect(serviceInterface).toBeDefined();
    });

    test('should initialize with default performance metrics', () => {
      // Access private metrics through type assertion for testing
      const metrics = (ruleEngineCore as any)._performanceMetrics;
      expect(metrics).toBeDefined();
      expect(metrics.totalRulesProcessed).toBe(0);
      expect(metrics.totalRuleSetsProcessed).toBe(0);
      expect(metrics.successRate).toBe(0);
      expect(metrics.errorRate).toBe(0);
    });

    test('should validate configuration constants', () => {
      const config = (ruleEngineCore as any)._engineConfig;
      expect(config.MAX_RULES_PER_SET).toBe(100);
      expect(config.DEFAULT_RULE_TIMEOUT_MS).toBe(30000);
      expect(config.MAX_CONCURRENT_RULES).toBe(20);
      expect(config.RULE_CACHE_TTL_MS).toBe(3600000);
    });
  });

  // ============================================================================
  // ENTERPRISE INITIALIZATION TESTS
  // ============================================================================

  describe('Enterprise Initialization', () => {
    test('should initialize successfully with valid configuration', async () => {
      const freshEngine = new GovernanceRuleEngineCore();
      await expect(freshEngine.initialize()).resolves.not.toThrow();

      // Verify initialization state through service status
      const status = await freshEngine.getServiceStatus();
      expect(status).toBeDefined();
      expect(status.isInitialized).toBe(true);

      await freshEngine.shutdown();
    });

    test('should handle initialization with BaseTrackingService inheritance', async () => {
      const freshEngine = new GovernanceRuleEngineCore();
      await freshEngine.initialize();
      
      // Verify BaseTrackingService methods are available
      expect(typeof freshEngine.track).toBe('function');
      expect(typeof freshEngine.getMetrics).toBe('function');
      expect(typeof freshEngine.validate).toBe('function');
      
      await freshEngine.shutdown();
    });

    test('should not allow double initialization', async () => {
      const freshEngine = new GovernanceRuleEngineCore();
      await freshEngine.initialize();
      
      // Second initialization should not throw but should be handled gracefully
      await expect(freshEngine.initialize()).resolves.not.toThrow();
      
      await freshEngine.shutdown();
    });
  });

  // ============================================================================
  // ENTERPRISE RULE PROCESSING TESTS
  // ============================================================================

  describe('Enterprise Rule Processing', () => {
    beforeEach(async () => {
      await ruleEngineCore.initialize();
      await flushPromises();
    });

    test('should process rule set successfully', async () => {
      const result = await ruleEngineCore.processRuleSet(mockRuleSet, mockProcessingContext);

      expect(result).toBeDefined();
      expect(result.processingId).toBe(mockProcessingContext.processingId);
      expect(result.ruleSetId).toBe(mockRuleSet.ruleSetId);
      expect(result.status).toBeDefined();
      expect(result.results).toBeDefined();
      expect(Array.isArray(result.results)).toBe(true);
    });

    test('should validate rule set inputs before processing', async () => {
      const invalidRuleSet = { ...mockRuleSet, ruleSetId: '' };

      await expect(ruleEngineCore.processRuleSet(invalidRuleSet, mockProcessingContext))
        .rejects.toThrow();
    });

    test('should handle empty rule set gracefully', async () => {
      const emptyRuleSet = { ...mockRuleSet, rules: [] };

      // Implementation validates that rule set must contain at least one rule
      await expect(ruleEngineCore.processRuleSet(emptyRuleSet, mockProcessingContext))
        .rejects.toThrow('Rule set must contain at least one rule');
    });

    test('should create and manage processing sessions', async () => {
      // Start processing to create session
      const processingPromise = ruleEngineCore.processRuleSet(mockRuleSet, mockProcessingContext);

      // Allow session creation
      await flushPromises();

      // Verify session exists
      const activeSessions = (ruleEngineCore as any)._activeSessions;
      expect(activeSessions.size).toBeGreaterThan(0);

      // Complete processing
      await processingPromise;

      // Verify session cleanup
      await flushPromises();
      expect(activeSessions.size).toBe(0);
    });

    test('should build dependency graph for rule execution', async () => {
      const ruleWithDependency = {
        ...mockGovernanceRule,
        ruleId: 'dependent-rule',
        configuration: {
          ...mockGovernanceRule.configuration,
          dependencies: ['test-rule-001']
        }
      };

      const ruleSetWithDependencies = {
        ...mockRuleSet,
        rules: [mockGovernanceRule, ruleWithDependency]
      };

      const result = await ruleEngineCore.processRuleSet(ruleSetWithDependencies, mockProcessingContext);
      expect(result.results).toHaveLength(2);
    });

    test('should compile rules during processing', async () => {
      await ruleEngineCore.processRuleSet(mockRuleSet, mockProcessingContext);

      // Verify rule compilation
      const compiledRules = (ruleEngineCore as any)._compiledRules;
      expect(compiledRules.size).toBeGreaterThan(0);
      expect(compiledRules.has(mockGovernanceRule.ruleId)).toBe(true);
    });

    test('should update performance metrics after processing', async () => {
      const initialMetrics = (ruleEngineCore as any)._performanceMetrics;
      const initialProcessed = initialMetrics.totalRuleSetsProcessed;

      await ruleEngineCore.processRuleSet(mockRuleSet, mockProcessingContext);

      const updatedMetrics = (ruleEngineCore as any)._performanceMetrics;
      expect(updatedMetrics.totalRuleSetsProcessed).toBe(initialProcessed + 1);
      expect(updatedMetrics.lastProcessingTime).toBeInstanceOf(Date);
    });

    test('should handle processing errors gracefully', async () => {
      // Mock error in validation
      const originalValidate = (ruleEngineCore as any)._validateRuleSetInputs;
      (ruleEngineCore as any)._validateRuleSetInputs = jest.fn().mockRejectedValue(new Error('Validation failed'));

      await expect(ruleEngineCore.processRuleSet(mockRuleSet, mockProcessingContext))
        .rejects.toThrow('Validation failed');

      // Restore original method
      (ruleEngineCore as any)._validateRuleSetInputs = originalValidate;
    });
  });

  // ============================================================================
  // ENTERPRISE RULE MANAGEMENT TESTS
  // ============================================================================

  describe('Enterprise Rule Management', () => {
    beforeEach(async () => {
      await ruleEngineCore.initialize();
      await flushPromises();
    });

    test('should add rule successfully', async () => {
      await expect(ruleEngineCore.addRule(mockGovernanceRule)).resolves.not.toThrow();

      // Verify rule is stored
      const rules = (ruleEngineCore as any)._rules;
      expect(rules.has(mockGovernanceRule.ruleId)).toBe(true);
      expect(rules.get(mockGovernanceRule.ruleId)).toEqual(mockGovernanceRule);
    });

    test('should validate rule before adding', async () => {
      const invalidRule = { ...mockGovernanceRule, ruleId: '' };

      await expect(ruleEngineCore.addRule(invalidRule))
        .rejects.toThrow();
    });

    test('should enforce rule limits when adding', async () => {
      // Mock the rules map to simulate limit reached
      const rules = (ruleEngineCore as any)._rules;
      const originalSize = rules.size;

      // Mock size property to simulate limit
      Object.defineProperty(rules, 'size', {
        get: () => 1001, // Exceeds MAX_RULES_PER_SET * 10 = 1000
        configurable: true
      });

      await expect(ruleEngineCore.addRule(mockGovernanceRule))
        .rejects.toThrow('Maximum rules limit exceeded');

      // Restore original size
      Object.defineProperty(rules, 'size', {
        get: () => originalSize,
        configurable: true
      });
    });

    test('should compile rule when adding', async () => {
      await ruleEngineCore.addRule(mockGovernanceRule);

      // Verify rule compilation
      const compiledRules = (ruleEngineCore as any)._compiledRules;
      expect(compiledRules.has(mockGovernanceRule.ruleId)).toBe(true);

      const compilationResult = compiledRules.get(mockGovernanceRule.ruleId);
      expect(compilationResult.ruleId).toBe(mockGovernanceRule.ruleId);
      expect(typeof compilationResult.compiledExpression).toBe('function');
    });

    test('should remove rule successfully', async () => {
      // First add a rule
      await ruleEngineCore.addRule(mockGovernanceRule);

      // Then remove it
      await expect(ruleEngineCore.removeRule(mockGovernanceRule.ruleId)).resolves.not.toThrow();

      // Verify rule is removed
      const rules = (ruleEngineCore as any)._rules;
      expect(rules.has(mockGovernanceRule.ruleId)).toBe(false);
    });

    test('should handle removing non-existent rule', async () => {
      // Implementation handles non-existent rules gracefully without throwing
      await expect(ruleEngineCore.removeRule('non-existent-rule'))
        .resolves.not.toThrow();
    });

    test('should update existing rule successfully', async () => {
      // First add a rule
      await ruleEngineCore.addRule(mockGovernanceRule);

      const updates = {
        name: 'Updated Rule Name',
        description: 'Updated description',
        priority: 5
      };

      await expect(ruleEngineCore.updateRule(mockGovernanceRule.ruleId, updates))
        .resolves.not.toThrow();

      // Verify rule is updated
      const rules = (ruleEngineCore as any)._rules;
      const updatedRule = rules.get(mockGovernanceRule.ruleId);
      expect(updatedRule.name).toBe(updates.name);
      expect(updatedRule.description).toBe(updates.description);
      expect(updatedRule.priority).toBe(updates.priority);
    });

    test('should handle updating non-existent rule', async () => {
      const updates = { name: 'Updated Name' };

      await expect(ruleEngineCore.updateRule('non-existent-rule', updates))
        .rejects.toThrow('Rule not found');
    });

    test('should recompile rule after update', async () => {
      // First add a rule
      await ruleEngineCore.addRule(mockGovernanceRule);

      const updates = { name: 'Updated Rule Name' };
      await ruleEngineCore.updateRule(mockGovernanceRule.ruleId, updates);

      // Verify rule is recompiled
      const compiledRules = (ruleEngineCore as any)._compiledRules;
      expect(compiledRules.has(mockGovernanceRule.ruleId)).toBe(true);
    });
  });

  // ============================================================================
  // ENTERPRISE ERROR HANDLING & EDGE CASES TESTS
  // ============================================================================

  describe('Enterprise Error Handling & Edge Cases', () => {
    beforeEach(async () => {
      await ruleEngineCore.initialize();
      await flushPromises();
    });

    test('should handle rule compilation errors gracefully', async () => {
      // Mock crypto to throw error during compilation
      const crypto = require('crypto');
      const originalCreateHash = crypto.createHash;
      crypto.createHash = jest.fn().mockImplementation(() => {
        throw new Error('Crypto error');
      });

      await expect(ruleEngineCore.addRule(mockGovernanceRule))
        .rejects.toThrow();

      // Restore original function
      crypto.createHash = originalCreateHash;
    });

    test('should handle invalid rule types', async () => {
      const invalidRule = {
        ...mockGovernanceRule,
        type: 'invalid-type' as TGovernanceRuleType
      };

      await expect(ruleEngineCore.addRule(invalidRule))
        .rejects.toThrow();
    });

    test('should handle missing rule configuration', async () => {
      const ruleWithoutConfig = {
        ...mockGovernanceRule,
        configuration: undefined as any
      };

      await expect(ruleEngineCore.addRule(ruleWithoutConfig))
        .rejects.toThrow();
    });

    test('should handle circular dependencies in rule set', async () => {
      const rule1 = {
        ...mockGovernanceRule,
        ruleId: 'rule-1',
        configuration: {
          ...mockGovernanceRule.configuration,
          dependencies: ['rule-2']
        }
      };

      const rule2 = {
        ...mockGovernanceRule,
        ruleId: 'rule-2',
        configuration: {
          ...mockGovernanceRule.configuration,
          dependencies: ['rule-1']
        }
      };

      const circularRuleSet = {
        ...mockRuleSet,
        rules: [rule1, rule2]
      };

      await expect(ruleEngineCore.processRuleSet(circularRuleSet, mockProcessingContext))
        .rejects.toThrow();
    });

    test('should handle timeout during rule processing', async () => {
      const timeoutContext = {
        ...mockProcessingContext,
        configuration: {
          ...mockProcessingContext.configuration,
          timeout: 1 // Very short timeout
        }
      };

      // Mock a slow rule execution
      const originalExecute = (ruleEngineCore as any)._executeRuleSet;
      (ruleEngineCore as any)._executeRuleSet = jest.fn().mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 100)); // Longer than timeout
        return { status: 'completed', results: [] };
      });

      await expect(ruleEngineCore.processRuleSet(mockRuleSet, timeoutContext))
        .rejects.toThrow();

      // Restore original method
      (ruleEngineCore as any)._executeRuleSet = originalExecute;
    });

    test('should handle memory pressure during processing', async () => {
      // Simulate memory pressure by creating large rule set
      const largeRuleSet = {
        ...mockRuleSet,
        rules: Array(1000).fill(null).map((_, index) => ({
          ...mockGovernanceRule,
          ruleId: `rule-${index}`
        }))
      };

      // This should either succeed or fail gracefully without crashing
      try {
        await ruleEngineCore.processRuleSet(largeRuleSet, mockProcessingContext);
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
      }
    });

    test('should handle shutdown during active processing', async () => {
      // Start processing
      const processingPromise = ruleEngineCore.processRuleSet(mockRuleSet, mockProcessingContext);

      // Shutdown immediately
      await ruleEngineCore.shutdown();

      // Processing should handle shutdown gracefully
      try {
        await processingPromise;
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
      }
    });

    test('should handle invalid processing context', async () => {
      const invalidContext = {
        ...mockProcessingContext,
        processingId: '' // Invalid empty ID
      };

      await expect(ruleEngineCore.processRuleSet(mockRuleSet, invalidContext))
        .rejects.toThrow();
    });
  });

  // ============================================================================
  // ENTERPRISE PERFORMANCE & MONITORING TESTS
  // ============================================================================

  describe('Enterprise Performance & Monitoring', () => {
    beforeEach(async () => {
      await ruleEngineCore.initialize();
      await flushPromises();
    });

    test('should track performance metrics during processing', async () => {
      const initialMetrics = (ruleEngineCore as any)._performanceMetrics;
      const initialProcessed = initialMetrics.totalRulesProcessed;

      await ruleEngineCore.processRuleSet(mockRuleSet, mockProcessingContext);

      const updatedMetrics = (ruleEngineCore as any)._performanceMetrics;
      expect(updatedMetrics.totalRulesProcessed).toBeGreaterThan(initialProcessed);
      expect(updatedMetrics.avgProcessingTimeMs).toBeGreaterThan(0);
      expect(updatedMetrics.lastProcessingTime).toBeInstanceOf(Date);
    });

    test('should calculate success rate correctly', async () => {
      const metrics = (ruleEngineCore as any)._performanceMetrics;

      // Process successful rule set
      await ruleEngineCore.processRuleSet(mockRuleSet, mockProcessingContext);

      expect(metrics.successRate).toBeGreaterThan(0);
      expect(metrics.successRate).toBeLessThanOrEqual(1);
    });

    test('should track cache hit rate', async () => {
      // Add rule to cache
      await ruleEngineCore.addRule(mockGovernanceRule);

      // Process rule set multiple times to test cache
      await ruleEngineCore.processRuleSet(mockRuleSet, mockProcessingContext);
      await ruleEngineCore.processRuleSet(mockRuleSet, mockProcessingContext);

      const metrics = (ruleEngineCore as any)._performanceMetrics;
      expect(metrics.cacheHitRate).toBeGreaterThanOrEqual(0);
      expect(metrics.cacheHitRate).toBeLessThanOrEqual(1);
    });

    test('should monitor current and peak load', async () => {
      const metrics = (ruleEngineCore as any)._performanceMetrics;

      // Process multiple rule sets concurrently
      const promises = Array(5).fill(null).map(() =>
        ruleEngineCore.processRuleSet(mockRuleSet, {
          ...mockProcessingContext,
          processingId: `concurrent-${Math.random()}`
        })
      );

      await Promise.all(promises);

      expect(metrics.peakLoad).toBeGreaterThanOrEqual(metrics.currentLoad);
    });

    test('should calculate error rate correctly', async () => {
      const metrics = (ruleEngineCore as any)._performanceMetrics;
      const initialErrorRate = metrics.errorRate;

      // Cause an error
      try {
        await ruleEngineCore.processRuleSet({ ...mockRuleSet, ruleSetId: '' }, mockProcessingContext);
      } catch (error) {
        // Expected error
      }

      expect(metrics.errorRate).toBeGreaterThanOrEqual(initialErrorRate);
    });

    test('should provide service metrics through BaseTrackingService', async () => {
      const metrics = await ruleEngineCore.getMetrics();

      expect(metrics).toBeDefined();
      expect(metrics.operations).toBeDefined();
      expect(metrics.performance).toBeDefined();
      expect(metrics.errors).toBeDefined();
    });

    test('should handle memory usage monitoring', async () => {
      // Process rule set to generate memory usage
      await ruleEngineCore.processRuleSet(mockRuleSet, mockProcessingContext);

      const metrics = await ruleEngineCore.getMetrics();
      expect(metrics.performance.memoryUsage).toBeGreaterThan(0);
    });
  });

  // ============================================================================
  // ENTERPRISE INTERFACE COMPLIANCE & INTEGRATION TESTS
  // ============================================================================

  describe('Enterprise Interface Compliance & Integration', () => {
    beforeEach(async () => {
      await ruleEngineCore.initialize();
      await flushPromises();
    });

    test('should implement IGovernanceRuleEngineCore interface completely', () => {
      const engineInterface: IGovernanceRuleEngineCore = ruleEngineCore;

      // Verify all required methods exist
      expect(typeof engineInterface.processRuleSet).toBe('function');
      expect(typeof engineInterface.addRule).toBe('function');
      expect(typeof engineInterface.removeRule).toBe('function');
      expect(typeof engineInterface.updateRule).toBe('function');
    });

    test('should implement IGovernanceService interface completely', () => {
      const serviceInterface: IGovernanceService = ruleEngineCore;

      // Verify service interface methods
      expect(typeof serviceInterface.initialize).toBe('function');
      expect(typeof serviceInterface.shutdown).toBe('function');
      expect(typeof serviceInterface.getServiceStatus).toBe('function');
      expect(typeof serviceInterface.validate).toBe('function');
    });

    test('should integrate with BaseTrackingService properly', async () => {
      // Test tracking functionality
      const trackingData = {
        id: 'test-tracking',
        type: 'rule-processing',
        data: { ruleId: 'test-rule' },
        timestamp: new Date().toISOString(),
        metadata: { source: 'test' }
      };

      await expect(ruleEngineCore.track(trackingData)).resolves.not.toThrow();
    });

    test('should provide service status information', async () => {
      const status = await ruleEngineCore.getServiceStatus();

      expect(status).toBeDefined();
      expect(status.isInitialized).toBeDefined();
      expect(status.timestamp).toBeDefined();
      expect(status.serviceName).toBeDefined();
    });

    test('should validate service configuration', async () => {
      const validationResult = await ruleEngineCore.validate();

      expect(validationResult).toBeDefined();
      expect(validationResult.status).toBeDefined();
      expect(validationResult.validationId).toBeDefined();
    });

    test('should handle service lifecycle properly', async () => {
      const freshEngine = new GovernanceRuleEngineCore();

      // Test initialization
      await expect(freshEngine.initialize()).resolves.not.toThrow();
      const initStatus = await freshEngine.getServiceStatus();
      expect(initStatus.isInitialized).toBe(true);

      // Test shutdown
      await expect(freshEngine.shutdown()).resolves.not.toThrow();
      const shutdownStatus = await freshEngine.getServiceStatus();
      expect(shutdownStatus.isShuttingDown).toBe(true);
    });
  });

  // ============================================================================
  // SURGICAL PRECISION TESTS - HARD-TO-REACH CODE PATHS
  // ============================================================================

  describe('Surgical Precision Tests - Hard-to-Reach Code Paths', () => {
    beforeEach(async () => {
      await ruleEngineCore.initialize();
      await flushPromises();
    });

    test('should handle rule validation edge cases', async () => {
      // Test rule with null configuration
      const ruleWithNullConfig = {
        ...mockGovernanceRule,
        configuration: null as any
      };

      await expect(ruleEngineCore.addRule(ruleWithNullConfig))
        .rejects.toThrow();
    });

    test('should handle dependency resolution failures', async () => {
      const ruleWithMissingDependency = {
        ...mockGovernanceRule,
        configuration: {
          ...mockGovernanceRule.configuration,
          dependencies: ['non-existent-rule']
        }
      };

      const ruleSetWithMissingDep = {
        ...mockRuleSet,
        rules: [ruleWithMissingDependency]
      };

      await expect(ruleEngineCore.processRuleSet(ruleSetWithMissingDep, mockProcessingContext))
        .rejects.toThrow();
    });

    test('should handle rule compilation with invalid expressions', async () => {
      // Mock Function constructor to throw error
      const originalFunction = global.Function;
      global.Function = jest.fn().mockImplementation(() => {
        throw new Error('Invalid expression');
      });

      await expect(ruleEngineCore.addRule(mockGovernanceRule))
        .rejects.toThrow();

      // Restore original Function
      global.Function = originalFunction;
    });

    test('should handle session cleanup failures', async () => {
      // Mock cleanup method to throw error
      const originalCleanup = (ruleEngineCore as any)._cleanupProcessingSession;
      (ruleEngineCore as any)._cleanupProcessingSession = jest.fn().mockRejectedValue(new Error('Cleanup failed'));

      // Processing should still complete despite cleanup failure
      await expect(ruleEngineCore.processRuleSet(mockRuleSet, mockProcessingContext))
        .rejects.toThrow('Cleanup failed');

      // Restore original method
      (ruleEngineCore as any)._cleanupProcessingSession = originalCleanup;
    });

    test('should handle performance metrics update failures', async () => {
      // Mock metrics update to throw error
      const originalUpdate = (ruleEngineCore as any)._updateEnginePerformanceMetrics;
      (ruleEngineCore as any)._updateEnginePerformanceMetrics = jest.fn().mockRejectedValue(new Error('Metrics update failed'));

      // Processing should handle metrics failure gracefully
      await expect(ruleEngineCore.processRuleSet(mockRuleSet, mockProcessingContext))
        .rejects.toThrow('Metrics update failed');

      // Restore original method
      (ruleEngineCore as any)._updateEnginePerformanceMetrics = originalUpdate;
    });

    test('should handle rule execution with undefined compiled expression', async () => {
      // Add rule first
      await ruleEngineCore.addRule(mockGovernanceRule);

      // Corrupt compiled rule
      const compiledRules = (ruleEngineCore as any)._compiledRules;
      const compilationResult = compiledRules.get(mockGovernanceRule.ruleId);
      compilationResult.compiledExpression = undefined;

      await expect(ruleEngineCore.processRuleSet(mockRuleSet, mockProcessingContext))
        .rejects.toThrow();
    });

    test('should handle concurrent processing session conflicts', async () => {
      const context1 = { ...mockProcessingContext, processingId: 'concurrent-1' };
      const context2 = { ...mockProcessingContext, processingId: 'concurrent-2' };

      // Start concurrent processing
      const promise1 = ruleEngineCore.processRuleSet(mockRuleSet, context1);
      const promise2 = ruleEngineCore.processRuleSet(mockRuleSet, context2);

      // Both should complete successfully
      const results = await Promise.all([promise1, promise2]);
      expect(results).toHaveLength(2);
      expect(results[0].processingId).toBe('concurrent-1');
      expect(results[1].processingId).toBe('concurrent-2');
    });

    test('should handle rule update with partial configuration', async () => {
      // Add rule first
      await ruleEngineCore.addRule(mockGovernanceRule);

      // Update with partial configuration
      const partialUpdate = {
        configuration: {
          timeout: 60000
          // Missing other configuration properties
        }
      };

      await expect(ruleEngineCore.updateRule(mockGovernanceRule.ruleId, partialUpdate))
        .resolves.not.toThrow();

      // Verify partial update was applied
      const rules = (ruleEngineCore as any)._rules;
      const updatedRule = rules.get(mockGovernanceRule.ruleId);
      expect(updatedRule.configuration.timeout).toBe(60000);
    });
  });

  // ============================================================================
  // SURGICAL PRECISION TESTS - 95%+ COVERAGE TARGET
  // ============================================================================

  describe('Surgical Precision Tests - 95%+ Coverage Target', () => {
    beforeEach(async () => {
      await ruleEngineCore.initialize();
      await flushPromises();
    });

    // Target uncovered functions: getRule, getAllRules (lines 451-488)
    test('should get individual rule by ID', async () => {
      // Add rule first
      await ruleEngineCore.addRule(mockGovernanceRule);

      // Test getRule function
      const retrievedRule = await ruleEngineCore.getRule(mockGovernanceRule.ruleId);
      expect(retrievedRule).toEqual(mockGovernanceRule);
      expect(retrievedRule?.ruleId).toBe(mockGovernanceRule.ruleId);
    });

    test('should return null for non-existent rule', async () => {
      const retrievedRule = await ruleEngineCore.getRule('non-existent-rule');
      expect(retrievedRule).toBeNull();
    });

    test('should get all rules', async () => {
      // Add multiple rules
      await ruleEngineCore.addRule(mockGovernanceRule);
      const secondRule = { ...mockGovernanceRule, ruleId: 'test-rule-002', name: 'Second Rule' };
      await ruleEngineCore.addRule(secondRule);

      // Test getAllRules function
      const allRules = await ruleEngineCore.getAllRules();
      expect(allRules).toHaveLength(2);
      expect(allRules.map(r => r.ruleId)).toContain(mockGovernanceRule.ruleId);
      expect(allRules.map(r => r.ruleId)).toContain(secondRule.ruleId);
    });

    test('should handle getRule error path', async () => {
      // Mock logOperation to throw error
      const originalLogOperation = (ruleEngineCore as any).logOperation;
      (ruleEngineCore as any).logOperation = jest.fn().mockImplementation(() => {
        throw new Error('Logging error');
      });

      await expect(ruleEngineCore.getRule('test-rule'))
        .rejects.toThrow('Logging error');

      // Restore original method
      (ruleEngineCore as any).logOperation = originalLogOperation;
    });

    test('should handle getAllRules error path', async () => {
      // Mock Array.from to throw error
      const originalArrayFrom = Array.from;
      Array.from = jest.fn().mockImplementation(() => {
        throw new Error('Array conversion error');
      });

      await expect(ruleEngineCore.getAllRules())
        .rejects.toThrow('Array conversion error');

      // Restore original method
      Array.from = originalArrayFrom;
    });

    // Target uncovered getMetrics error path (lines 522-523)
    test('should handle getMetrics error path', async () => {
      // Mock performance metrics access to throw error
      const originalMetrics = (ruleEngineCore as any)._performanceMetrics;
      Object.defineProperty(ruleEngineCore, '_performanceMetrics', {
        get: () => {
          throw new Error('Metrics access error');
        },
        configurable: true
      });

      await expect(ruleEngineCore.getMetrics())
        .rejects.toThrow('Metrics access error');

      // Restore original metrics
      Object.defineProperty(ruleEngineCore, '_performanceMetrics', {
        value: originalMetrics,
        configurable: true
      });
    });

    // Target validation result formatting (lines 567-569)
    test('should format validation results with warnings and errors', async () => {
      // Create rule with validation issues
      const problematicRule = {
        ...mockGovernanceRule,
        configuration: {
          ...mockGovernanceRule.configuration,
          conditions: [], // Empty conditions should trigger warnings
          actions: [] // Empty actions should trigger warnings
        }
      };

      // Mock validation to return warnings and errors
      const originalValidateRule = (ruleEngineCore as any)._validateRule;
      (ruleEngineCore as any)._validateRule = jest.fn().mockResolvedValue({
        isValid: false,
        warnings: [
          { message: 'No conditions specified', severity: 'warning' },
          { message: 'No actions specified', severity: 'warning' }
        ],
        errors: [
          { message: 'Rule configuration incomplete', severity: 'error' }
        ]
      });

      const validationResult = await ruleEngineCore.validate();
      expect(validationResult.recommendations).toHaveLength(2);
      expect(validationResult.warnings).toHaveLength(2);
      expect(validationResult.errors).toHaveLength(1);
      expect(validationResult.recommendations[0]).toBe('No conditions specified');
      expect(validationResult.warnings[0]).toBe('No conditions specified');
      expect(validationResult.errors[0]).toBe('Rule configuration incomplete');

      // Restore original method
      (ruleEngineCore as any)._validateRule = originalValidateRule;
    });

    // Target parallel execution path (lines 814-821)
    test('should execute rules in parallel mode', async () => {
      // Create rule set with parallel execution
      const parallelRuleSet = {
        ...mockRuleSet,
        configuration: {
          ...mockRuleSet.configuration,
          executionMode: 'parallel' as const
        },
        rules: [
          mockGovernanceRule,
          { ...mockGovernanceRule, ruleId: 'parallel-rule-2', name: 'Parallel Rule 2' }
        ]
      };

      const parallelContext = {
        ...mockProcessingContext,
        configuration: {
          ...mockProcessingContext.configuration,
          parallel: true
        }
      };

      const result = await ruleEngineCore.processRuleSet(parallelRuleSet, parallelContext);
      expect(result.ruleResults).toHaveLength(2);
      expect(result.status).toBe('completed');
    });

    // Target rule execution failure path (lines 881-883)
    test('should handle rule execution failures', async () => {
      // Mock rule execution to fail
      const originalExecuteRule = (ruleEngineCore as any)._executeRule;
      (ruleEngineCore as any)._executeRule = jest.fn().mockRejectedValue(new Error('Rule execution failed'));

      // Process rule set should handle the failure
      const result = await ruleEngineCore.processRuleSet(mockRuleSet, mockProcessingContext);
      expect(result.status).toBe('completed'); // Should complete despite failures

      // Restore original method
      (ruleEngineCore as any)._executeRule = originalExecuteRule;
    });

    // Target session failure path (lines 805-808)
    test('should handle session execution failures', async () => {
      // Mock executeRuleSet to throw error
      const originalExecuteRuleSet = (ruleEngineCore as any)._executeRuleSet;
      (ruleEngineCore as any)._executeRuleSet = jest.fn().mockRejectedValue(new Error('Session execution failed'));

      await expect(ruleEngineCore.processRuleSet(mockRuleSet, mockProcessingContext))
        .rejects.toThrow('Session execution failed');

      // Restore original method
      (ruleEngineCore as any)._executeRuleSet = originalExecuteRuleSet;
    });

    // Target dependency graph edge cases
    test('should handle complex dependency graphs', async () => {
      const rule1 = { ...mockGovernanceRule, ruleId: 'rule-1' };
      const rule2 = {
        ...mockGovernanceRule,
        ruleId: 'rule-2',
        configuration: {
          ...mockGovernanceRule.configuration,
          dependencies: ['rule-1']
        }
      };
      const rule3 = {
        ...mockGovernanceRule,
        ruleId: 'rule-3',
        configuration: {
          ...mockGovernanceRule.configuration,
          dependencies: ['rule-1', 'rule-2']
        }
      };

      const complexRuleSet = {
        ...mockRuleSet,
        rules: [rule1, rule2, rule3]
      };

      const result = await ruleEngineCore.processRuleSet(complexRuleSet, mockProcessingContext);
      expect(result.ruleResults).toHaveLength(3);
      expect(result.status).toBe('completed');
    });

    // Target rule compilation edge cases
    test('should handle rule compilation with complex expressions', async () => {
      const complexRule = {
        ...mockGovernanceRule,
        configuration: {
          ...mockGovernanceRule.configuration,
          conditions: [
            'data.value > 100',
            'data.type === "premium"',
            'data.status !== "inactive"'
          ],
          actions: [
            'result.approved = true',
            'result.priority = "high"'
          ]
        }
      };

      await expect(ruleEngineCore.addRule(complexRule)).resolves.not.toThrow();

      // Verify compilation
      const compiledRules = (ruleEngineCore as any)._compiledRules;
      expect(compiledRules.has(complexRule.ruleId)).toBe(true);
    });

    // Target performance metrics edge cases
    test('should handle performance metrics calculation edge cases', async () => {
      const metrics = (ruleEngineCore as any)._performanceMetrics;

      // Test with zero values
      metrics.totalRulesProcessed = 0;
      metrics.totalProcessingTimeMs = 0;

      const result = await ruleEngineCore.getMetrics();
      expect(result.performance.avgProcessingTimeMs).toBe(0);
      expect(result.performance.successRate).toBe(0);
    });

    // Target memory pressure scenarios
    test('should handle memory pressure during large rule processing', async () => {
      // Create large rule set
      const largeRules = Array(50).fill(null).map((_, index) => ({
        ...mockGovernanceRule,
        ruleId: `large-rule-${index}`,
        name: `Large Rule ${index}`
      }));

      const largeRuleSet = {
        ...mockRuleSet,
        rules: largeRules
      };

      // Should handle large processing without crashing
      const result = await ruleEngineCore.processRuleSet(largeRuleSet, mockProcessingContext);
      expect(result.ruleResults).toHaveLength(50);
      expect(result.status).toBe('completed');
    });

    // Target specific error handling paths
    test('should handle rule validation with null values', async () => {
      const nullRule = {
        ...mockGovernanceRule,
        name: null as any,
        description: null as any
      };

      await expect(ruleEngineCore.addRule(nullRule))
        .rejects.toThrow();
    });

    test('should handle rule update with invalid data', async () => {
      // Add rule first
      await ruleEngineCore.addRule(mockGovernanceRule);

      // Try to update with invalid data
      const invalidUpdate = {
        type: 'invalid-type' as any,
        status: 'invalid-status' as any
      };

      await expect(ruleEngineCore.updateRule(mockGovernanceRule.ruleId, invalidUpdate))
        .rejects.toThrow();
    });

    test('should handle processing context validation edge cases', async () => {
      const invalidContext = {
        ...mockProcessingContext,
        targetData: null as any,
        configuration: null as any
      };

      await expect(ruleEngineCore.processRuleSet(mockRuleSet, invalidContext))
        .rejects.toThrow();
    });

    test('should handle rule compilation with syntax errors', async () => {
      const syntaxErrorRule = {
        ...mockGovernanceRule,
        configuration: {
          ...mockGovernanceRule.configuration,
          conditions: ['invalid syntax here !!!'],
          actions: ['also invalid syntax !!!']
        }
      };

      // Mock Function constructor to simulate syntax error
      const originalFunction = global.Function;
      global.Function = jest.fn().mockImplementation(() => {
        throw new SyntaxError('Unexpected token');
      });

      await expect(ruleEngineCore.addRule(syntaxErrorRule))
        .rejects.toThrow('Unexpected token');

      // Restore original Function
      global.Function = originalFunction;
    });

    test('should handle concurrent rule modifications', async () => {
      // Add initial rule
      await ruleEngineCore.addRule(mockGovernanceRule);

      // Perform concurrent operations
      const operations = [
        ruleEngineCore.updateRule(mockGovernanceRule.ruleId, { priority: 5 }),
        ruleEngineCore.getRule(mockGovernanceRule.ruleId),
        ruleEngineCore.processRuleSet(mockRuleSet, mockProcessingContext)
      ];

      // All operations should complete without interference
      const results = await Promise.allSettled(operations);
      expect(results.every(r => r.status === 'fulfilled')).toBe(true);
    });

    test('should handle rule execution timeout scenarios', async () => {
      const timeoutRule = {
        ...mockGovernanceRule,
        configuration: {
          ...mockGovernanceRule.configuration,
          timeout: 1 // Very short timeout
        }
      };

      const timeoutRuleSet = {
        ...mockRuleSet,
        rules: [timeoutRule]
      };

      // Mock rule execution to be slow
      const originalExecuteRule = (ruleEngineCore as any)._executeRule;
      (ruleEngineCore as any)._executeRule = jest.fn().mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 100)); // Longer than timeout
        return { status: 'completed', result: {} };
      });

      const result = await ruleEngineCore.processRuleSet(timeoutRuleSet, mockProcessingContext);
      expect(result.status).toBe('completed'); // Should handle timeout gracefully

      // Restore original method
      (ruleEngineCore as any)._executeRule = originalExecuteRule;
    });

    test('should handle cache invalidation scenarios', async () => {
      // Add rule to populate cache
      await ruleEngineCore.addRule(mockGovernanceRule);

      // Process to cache compilation
      await ruleEngineCore.processRuleSet(mockRuleSet, mockProcessingContext);

      // Update rule should invalidate cache
      await ruleEngineCore.updateRule(mockGovernanceRule.ruleId, { priority: 10 });

      // Process again should recompile
      const result = await ruleEngineCore.processRuleSet(mockRuleSet, mockProcessingContext);
      expect(result.status).toBe('completed');
    });

    test('should handle service shutdown during rule processing', async () => {
      // Start long-running processing
      const longRuleSet = {
        ...mockRuleSet,
        rules: Array(10).fill(null).map((_, i) => ({
          ...mockGovernanceRule,
          ruleId: `long-rule-${i}`
        }))
      };

      const processingPromise = ruleEngineCore.processRuleSet(longRuleSet, mockProcessingContext);

      // Shutdown service during processing
      setTimeout(() => ruleEngineCore.shutdown(), 10);

      // Processing should handle shutdown gracefully
      try {
        await processingPromise;
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
      }
    });
  });
});
