# IDE AI Assistant Implementation Prompt

## TASK: Create IDE AI Assistant Training System for 100% Test Coverage

I need you to create a complete training system to help you (AI assistant) solve test coverage issues systematically instead of spending hours debugging. Create the following file structure and content:

## STEP 1: Create Directory Structure

Create this directory structure in the project root:

```
./docs/lessons/
├── testing-patterns/
├── templates/
├── prompts/
├── workflows/
└── feedback/
```

## STEP 2: Create Pattern Library Files

### File: `./docs/lessons/testing-patterns/jest-mocking-patterns.md`

Create this file with this exact content:

```markdown
# Jest Mocking Patterns for Catch Block Coverage

## PATTERN: Constructor Failure in Catch Blocks
**When to use:** Lines uncovered in catch blocks during initialization (ranges like 209-228, 295)

**Problem:** Catch blocks in constructors/initialization methods not covered because mocks applied after class import

**Template:**
```typescript
// ❌ WRONG - Mocks after import (doesn't work)
const { ClassName } = require('./module');
jest.spyOn(Module, 'Constructor').mockImplementation(() => throw error);

// ✅ RIGHT - Mock before import (works)
jest.doMock('./dependency', () => ({
  Constructor: jest.fn().mockImplementation(() => {
    throw new Error('Forced failure');
  })
}));
jest.resetModules();
const { ClassName } = await import('./module');
```

**Trigger Conditions:**
- Lines like 209-228, 295, 551 uncovered
- Catch blocks in constructors/initialization
- Methods like _initializeResilientTimingSync, doInitialize
- Error: "Cannot read properties of undefined"

**Complete Working Example:**
```typescript
describe('Constructor Catch Block Coverage', () => {
  it('should hit lines 209-228: constructor failure', async () => {
    // Step 1: Mock before import
    jest.doMock('../../utils/ResilientTiming', () => ({
      ResilientTimer: jest.fn().mockImplementation(() => {
        throw new Error('Forced constructor failure');
      })
    }));
    
    jest.doMock('../../utils/ResilientMetrics', () => ({
      ResilientMetricsCollector: jest.fn().mockImplementation(() => {
        throw new Error('Forced constructor failure');
      })
    }));

    // Step 2: Clean module state
    jest.resetModules();

    // Step 3: Dynamic import with mocks active
    const { TemplateValidator } = await import('../TemplateValidation');

    // Step 4: Create instance - triggers catch block
    const validator = new TemplateValidator();
    expect(validator).toBeDefined();

    // Step 5: Test fallback behavior
    await validator.initialize();
    const template = createTestTemplate({ operations: [/* basic operation */] });
    const result = await validator.validateTemplate(template);
    expect(result.performanceMetrics.validationTime).toBeGreaterThanOrEqual(0);

    // Step 6: Cleanup
    await validator.shutdown();
    jest.dontMock('../../utils/ResilientTiming');
    jest.dontMock('../../utils/ResilientMetrics');
    jest.resetModules();
  });
});
```

## PATTERN: Processing Method Catch Blocks
**When to use:** Lines uncovered in processing/validation methods (ranges like 551, 600-650)

**Template:**
```typescript
jest.doMock('./processing-dependency', () => ({
  ProcessorClass: jest.fn().mockImplementation(function() {
    this.processMethod = jest.fn().mockImplementation(() => {
      throw new Error('Processing failure');
    });
  })
}));
```

## PATTERN: Reconfiguration Catch Blocks  
**When to use:** Lines uncovered in setup/reconfiguration methods (like line 295)

**Key:** First constructor succeeds, second fails

**Template:**
```typescript
let callCount = 0;
jest.doMock('./dependency', () => ({
  Constructor: jest.fn().mockImplementation(() => {
    callCount++;
    if (callCount === 1) {
      return { /* working implementation */ };
    } else {
      throw new Error('Reconfiguration failure');
    }
  })
}));
```

## PATTERN: Runtime Condition Catch Blocks
**When to use:** Lines in runtime conditions/evaluations (ranges like 933-940)

**Template:**
```typescript
// Create conditions that naturally trigger errors
const contextWithNull = { targetComponents: null };
expect(() => evaluateCondition(condition, contextWithNull)).toThrow();
```
```

### File: `./docs/lessons/testing-patterns/coverage-diagnostics.md`

```markdown
# Coverage Diagnostic Checklist

## STEP 1: Run Coverage Analysis
```bash
npm test -- --coverage
```

## STEP 2: Identify Line Range Patterns

**Common Patterns:**
- **200-230 range:** Constructor/Initialization catch blocks → Use jest.doMock constructor pattern
- **290-300 range:** Setup/Reconfiguration catch blocks → Use call-count pattern  
- **550-600 range:** Processing/Validation catch blocks → Use processing failure pattern
- **900+ range:** Runtime condition catch blocks → Use natural error conditions

## STEP 3: Pattern Matching Rules

### Constructor Catch Blocks (209-228, 295)
```typescript
// Pattern: jest.doMock + dynamic import
jest.doMock('dependency', () => ({ Class: mockFailingConstructor }));
jest.resetModules();
const { TargetClass } = await import('target-module');
```

### Processing Catch Blocks (551)  
```typescript
// Pattern: Mock processing dependencies
jest.doMock('./processing-module', () => ({ 
  ProcessorClass: mockFailingProcessor 
}));
```

### Runtime Catch Blocks (933-940)
```typescript
// Pattern: Create conditions that trigger runtime errors
const invalidContext = { targetComponents: null };
expect(() => evaluateCondition(condition, invalidContext)).toThrow();
```

## STEP 4: Implementation Verification
- [ ] Run coverage after each test
- [ ] Verify specific lines are now covered  
- [ ] Ensure no regression in existing coverage
- [ ] Time spent: Should be 15-30 minutes, not hours
```

## STEP 3: Create Templates

### File: `./docs/lessons/templates/catch-block-coverage.template.ts`

```typescript
// TEMPLATE: Catch Block Coverage Test
// Usage: Copy and replace PLACEHOLDER values with actual values

describe('CATCH_BLOCK_DESCRIPTION Coverage', () => {
  afterEach(() => {
    jest.resetModules();
    jest.resetAllMocks();
  });

  it('should hit TARGET_LINE_RANGE: TARGET_METHOD_NAME catch block', async () => {
    // Step 1: Mock failing dependency BEFORE import
    jest.doMock('DEPENDENCY_PATH', () => ({
      DEPENDENCY_CLASS: jest.fn().mockImplementation(() => {
        throw new Error('TARGET_ERROR_MESSAGE');
      })
    }));

    // Add additional mocks if needed
    // jest.doMock('ADDITIONAL_DEPENDENCY_PATH', () => ({ ... }));

    // Step 2: Clean module state
    jest.resetModules();

    // Step 3: Dynamic import with mocks active
    const { TARGET_CLASS } = await import('TARGET_MODULE_PATH');

    // Step 4: Create instance/trigger the catch block
    const instance = new TARGET_CLASS(CONSTRUCTOR_ARGS);

    // Step 5: Verify fallback behavior works
    expect(instance).toBeDefined();
    
    // Step 6: Test functionality with fallback infrastructure
    await instance.initialize();
    const testInput = TEST_INPUT_OBJECT;
    const result = await instance.TARGET_METHOD(testInput);
    
    // Step 7: Verify fallback produces valid results
    expect(result).toBeDefined();
    expect(result.EXPECTED_PROPERTY).toBeGreaterThanOrEqual(0);

    // Step 8: Cleanup
    await instance.shutdown();
    jest.dontMock('DEPENDENCY_PATH');
    jest.resetModules();
  });
});

/*
REPLACEMENT GUIDE:
- CATCH_BLOCK_DESCRIPTION → "Constructor Failure" | "Processing Error" | "Setup Error"
- TARGET_LINE_RANGE → "Lines 209-228" | "Line 295" | "Line 551"  
- TARGET_METHOD_NAME → "_initializeResilientTimingSync" | "doInitialize" | "_validateDependencies"
- DEPENDENCY_PATH → '../../utils/ResilientTiming' | './TemplateDependencies'
- DEPENDENCY_CLASS → 'ResilientTimer' | 'DependencyGraph'
- TARGET_ERROR_MESSAGE → 'Forced constructor failure for lines 209-228'
- TARGET_CLASS → 'TemplateValidator' | 'CleanupManager'
- TARGET_MODULE_PATH → '../TemplateValidation' | '../CleanupManager'
- CONSTRUCTOR_ARGS → config object or {} if no args needed
- TARGET_METHOD → 'validateTemplate' | 'executeCleanup'
- TEST_INPUT_OBJECT → template object | cleanup configuration
- EXPECTED_PROPERTY → 'performanceMetrics.validationTime' | 'result.success'
*/
```

## STEP 4: Create AI Prompts

### File: `./docs/lessons/prompts/test-coverage-prompt.md`

```markdown
# Test Coverage AI Assistant Decision Prompt

## BEFORE WRITING ANY TESTS - FOLLOW THIS CHECKLIST:

### 1. COVERAGE ANALYSIS
Ask user: "Please run: npm test -- --coverage and paste the uncovered lines"

Analyze patterns:
- Lines 200-230 → Constructor catch blocks
- Lines 290-300 → Setup/reconfiguration catch blocks  
- Lines 550-600 → Processing/validation catch blocks
- Lines 900+ → Runtime condition catch blocks

### 2. PATTERN MATCHING
Match uncovered lines to patterns from ./docs/lessons/testing-patterns/jest-mocking-patterns.md:

**If lines 200-230 (like 209-228):**
- Use jest.doMock constructor failure pattern
- Target: Constructor/initialization catch blocks
- Method: _initializeResilientTimingSync, constructor

**If line 295 or similar:**
- Use jest.doMock reconfiguration failure pattern
- Target: Setup/reconfiguration catch blocks  
- Method: doInitialize, configure

**If line 551 or 550-600 range:**
- Use jest.doMock processing failure pattern
- Target: Processing/validation catch blocks
- Method: _validateDependencies, _processData

**If lines 900+ (like 933-940):**
- Use natural error conditions pattern
- Target: Runtime condition catch blocks
- Method: evaluateCondition, checkState

### 3. IMPLEMENTATION PROCESS
1. Copy ./docs/lessons/templates/catch-block-coverage.template.ts
2. Replace PLACEHOLDER values with actual values
3. Use jest.doMock + dynamic import pattern
4. Ensure proper cleanup

### 4. VERIFICATION CHECKLIST
- [ ] Mock dependencies BEFORE import using jest.doMock()
- [ ] Use jest.resetModules() for clean state
- [ ] Use dynamic import: await import()
- [ ] Test fallback behavior works correctly
- [ ] Cleanup: jest.dontMock() + jest.resetModules()
- [ ] Verify lines are covered after test runs

### 5. SUCCESS CRITERIA
- Coverage increases to target lines
- Test completes in 15-30 minutes (not hours)
- Fallback behavior is verified to work correctly
- No existing test regressions
```

## STEP 5: Create Workflows

### File: `./docs/lessons/workflows/coverage-workflow.md`

```markdown
# Coverage Workflow - Step by Step Process

## PHASE 1: Diagnosis (5 minutes)
```bash
# 1. Run coverage to identify gaps
npm test -- --coverage

# 2. Identify uncovered line patterns
echo "Analyzing uncovered lines..."
echo "Lines 200-230: Constructor catch blocks"
echo "Lines 290-300: Setup catch blocks"  
echo "Lines 550-600: Processing catch blocks"
echo "Lines 900+: Runtime catch blocks"
```

## PHASE 2: Pattern Selection (2 minutes)
**AI Prompt:** 
"Uncovered lines: [PASTE_LINE_NUMBERS]
Match to pattern from ./docs/lessons/testing-patterns/jest-mocking-patterns.md
Provide pattern name and template to use."

## PHASE 3: Implementation (10-15 minutes)
**AI Prompt:**
"Use ./docs/lessons/templates/catch-block-coverage.template.ts
Replace placeholders for:
- Target lines: [LINE_NUMBERS]
- Target method: [METHOD_NAME]  
- Dependency path: [DEPENDENCY_PATH]
Provide complete working test code."

## PHASE 4: Verification (5 minutes)
```bash
# Run single test to verify
npm test -- --testNamePattern="TARGET_TEST_NAME" --coverage

# Check specific lines are now covered
echo "Verify lines [TARGET_LINES] are now covered"
```

## PHASE 5: Documentation (2 minutes)
Record result in ./docs/lessons/feedback/pattern-effectiveness.md

## TOTAL TIME TARGET: 15-30 minutes (not hours)
```

## STEP 6: Create Feedback Tracking

### File: `./docs/lessons/feedback/pattern-effectiveness.md`

```markdown
# Pattern Effectiveness Tracking

## Template for Recording Results
```
Date: YYYY-MM-DD
Issue: Lines X-Y uncovered in [METHOD_NAME]
Pattern Used: [PATTERN_NAME from jest-mocking-patterns.md]
Template Used: [TEMPLATE_FILE_NAME]
Result: ✅ Success | ❌ Failed
Time Spent: [DURATION]
Notes: [WHAT_WORKED_OR_WHAT_FAILED]
Coverage Before: [X]%
Coverage After: [Y]%
```

## Success Examples:
```
Date: 2025-01-15
Issue: Lines 209-228 uncovered in _initializeResilientTimingSync
Pattern Used: Constructor failure pattern (jest.doMock + dynamic import)
Template Used: catch-block-coverage.template.ts
Result: ✅ Success
Time Spent: 18 minutes
Notes: Key was jest.resetModules() + dynamic import timing. Mock must be before import.
Coverage Before: 97.08%
Coverage After: 100%
```

## Failed Attempts (Learn from these):
```
Date: 2025-01-14
Issue: Lines 209-228 uncovered 
Pattern Used: Constructor spying (wrong approach)
Result: ❌ Failed
Time Spent: 4+ hours
Notes: Spying after import doesn't work for constructor catch blocks. Need jest.doMock before import.
```
```

## STEP 7: Create Quick Reference

### File: `./docs/lessons/quick-reference.md`

```markdown
# Quick Reference - Coverage Problem Solving

## 1. IMMEDIATE DIAGNOSTIC
```bash
npm test -- --coverage | grep "Uncovered Line"
```

## 2. PATTERN MATCHING
- **Lines 200-230** → Constructor pattern → jest.doMock + dynamic import
- **Lines 290-300** → Setup pattern → call-count mock pattern  
- **Lines 550-600** → Processing pattern → mock processing dependencies
- **Lines 900+** → Runtime pattern → natural error conditions

## 3. TEMPLATE SELECTION
Use: `./docs/lessons/templates/catch-block-coverage.template.ts`

## 4. AI PROMPT TEMPLATE
"Coverage gaps: [LINES]
Apply pattern from ./docs/lessons/testing-patterns/jest-mocking-patterns.md
Use template from ./docs/lessons/templates/catch-block-coverage.template.ts
Target: 100% coverage in 15-30 minutes"

## 5. SUCCESS VERIFICATION
✅ Lines covered  
✅ Time under 30 minutes  
✅ No test regressions  
✅ Fallback behavior verified
```

## VERIFICATION STEP

After creating all files, verify the structure exists:

```bash
find ./docs/lessons/ -type f -name "*.md" -o -name "*.ts" | sort
```

Expected output:
```
./docs/lessons/feedback/pattern-effectiveness.md
./docs/lessons/prompts/test-coverage-prompt.md
./docs/lessons/quick-reference.md
./docs/lessons/templates/catch-block-coverage.template.ts
./docs/lessons/testing-patterns/coverage-diagnostics.md
./docs/lessons/testing-patterns/jest-mocking-patterns.md
./docs/lessons/workflows/coverage-workflow.md
```

Confirm you have created all these files with the exact content specified above.
