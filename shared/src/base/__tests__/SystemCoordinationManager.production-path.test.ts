/**
 * SystemCoordinationManager – Production Branch Coverage via isolateModules
 */

describe('SystemCoordinationManager – Production Branch Coverage', () => {
  it('executes production setTimeout branch (606-611) including default strategy path', async () => {
    const realNODE = process.env.NODE_ENV;
    const realJest = (global as any).jest;
    const realJestWorker = process.env.JEST_WORKER_ID;

    try {
      // Configure environment to look like production without jest
      process.env.NODE_ENV = 'production';
      delete process.env.JEST_WORKER_ID;
      // Important: set global.jest to undefined so typeof jest === 'undefined'
      (global as any).jest = undefined;

      jest.isolateModules(() => {
        // Import inside isolation so module sees modified globals
        // eslint-disable-next-line @typescript-eslint/no-var-requires
        const { SystemCoordinationManager } = require('../memory-safety-manager/modules/SystemCoordinationManager');
        const mgr = new SystemCoordinationManager({} as any);

        // Use fake timers to deterministically drive setTimeout
        jest.useFakeTimers();

        const p1 = (mgr as any)._shutdownComponent('X', 'emergency'); // 10ms
        const p2 = (mgr as any)._shutdownComponent('Y', 'priority');  // 50ms
        const p3 = (mgr as any)._shutdownComponent('Z', 'other');     // 100ms default

        jest.advanceTimersByTime(100);

        return Promise.all([p1, p2, p3]);
      });
    } finally {
      // Restore environment
      process.env.NODE_ENV = realNODE;
      if (typeof realJest !== 'undefined') {
        (global as any).jest = realJest;
      }
      if (typeof realJestWorker !== 'undefined') {
        process.env.JEST_WORKER_ID = realJestWorker;
      }
      jest.useRealTimers();
    }
  });
});

