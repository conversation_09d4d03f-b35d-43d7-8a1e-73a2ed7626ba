import { getMemorySafetyManager, resetMemorySafetyManager, MemorySafetyManager, ShutdownPhase } from '../MemorySafetyManager';
import { getEventHandlerRegistry, resetEventHandlerRegistry } from '../EventHandlerRegistry';
import { getTimerCoordinator } from '../TimerCoordinationService';
import { getCleanupCoordinator, resetCleanupCoordinator, CleanupOperationType, CleanupPriority } from '../CleanupCoordinatorEnhanced';

// Scope: shared/src only

describe('MemorySafetyManager – Priority Coverage', () => {
  beforeEach(() => {
    resetMemorySafetyManager();
    resetEventHandlerRegistry();
    resetCleanupCoordinator();
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    resetMemorySafetyManager();
    resetEventHandlerRegistry();
    resetCleanupCoordinator();
    jest.clearAllTimers();
  });

  function createManager(overrides: Partial<any> = {}) {
    const mgr = getMemorySafetyManager({
      resourceManagerConfig: { maxIntervals: 5, maxTimeouts: 5, maxCacheSize: 1024 * 1024, memoryThresholdMB: 50 },
      cleanupCoordinatorConfig: { maxConcurrentOperations: 2, defaultTimeout: 2000, maxRetries: 1, conflictDetectionEnabled: true, testMode: true },
      timerCoordinationConfig: { maxConcurrentTimers: 10, defaultTimeoutMs: 1000, cleanupIntervalMs: 5000 },
      shutdownTimeoutMs: 2000,
      emergencyCleanupEnabled: true,
      performanceMonitoringEnabled: true,
      memoryLeakDetectionEnabled: true,
      ...overrides,
    } as any);
    return mgr as any;
  }

  it('initializes components, sets up coordination, starts monitors, and reports metrics', async () => {
    const mgr: any = createManager();

    await mgr.initialize();

    // Metrics
    const metrics = await mgr.getSystemMetrics();
    expect(metrics).toHaveProperty('resources');
    expect(metrics).toHaveProperty('timers');
    expect(metrics).toHaveProperty('cleanup');

    // Ensure periodic monitoring intervals created (tracked by base)
    const rm = mgr.getResourceMetrics();
    expect(rm.activeIntervals).toBeGreaterThanOrEqual(1);
  });

  it('shutdown executes phases including force cleanup and validation', async () => {
    const mgr: any = createManager();
    await mgr.initialize();

    await expect(mgr.shutdown()).resolves.toBeUndefined();
  });

  it('doShutdown error path: simulate failure in FORCE_CLEANUP phase and expect rethrow (string error)', async () => {
    const mgr: any = createManager();
    await mgr.initialize();

    jest.spyOn(mgr as any, '_executeShutdownPhase').mockImplementation(async (phase: any) => {
      if (phase === ShutdownPhase.FORCE_CLEANUP) throw 'force-fail';
      return undefined as any;
    });

    await expect((mgr as any).doShutdown()).rejects.toBe('force-fail');
  });

  it('forceSystemCleanup triggers force cleanup sequence', async () => {
    const mgr: any = createManager();
    await mgr.initialize();

    await expect(mgr.forceSystemCleanup()).resolves.toBeUndefined();
  });

  it('memory leak detection warning when total exceeds threshold', async () => {
    const mgr: any = createManager();
    await mgr.initialize();

    // Spy on metrics to simulate high memory usage
    const metricsSpy = jest.spyOn(mgr, 'getSystemMetrics').mockResolvedValueOnce({
      eventHandlers: { totalHandlers: 0, activeClients: 0, memoryUsageBytes: 0 },
      resources: { activeIntervals: 0, activeTimeouts: 0, cacheSize: 0, memoryUsageBytes: 60 * 1024 * 1024 },
      timers: { activeTimers: 0, coordinatedOperations: 0, memoryUsageBytes: 0 },
      cleanup: { totalOperations: 0, runningOperations: 0, conflictsPrevented: 0, averageExecutionTime: 0 },
      totalMemoryUsageBytes: 60 * 1024 * 1024,
      systemHealthScore: 100,
      lastFullCleanup: null,
      performanceOverhead: 0
    } as any);

    const warnSpy = jest.spyOn(mgr as any, 'logWarning');
    await (mgr as any)['_performMemoryLeakDetection']();
    expect(warnSpy).toHaveBeenCalledWith('Memory usage exceeds threshold', expect.any(Object));

    metricsSpy.mockRestore();
  });

  it('calculate total memory usage honors test mode cap and production path', async () => {
    const mgr: any = createManager();
    await mgr.initialize();

    // Test mode branch (Jest env returns true)
    const testTotal = (mgr as any)['_calculateTotalMemoryUsage'](
      { memoryUsageBytes: 2 * 1024 * 1024 },
      { memoryUsageBytes: 2 * 1024 * 1024 },
      { memoryUsageBytes: 2 * 1024 * 1024 }
    );
    expect(testTotal).toBeLessThanOrEqual(900 * 1024);

    // Production branch via module isolation and mocking isTestEnvironment()
    let prodTotal: number;
    await jest.isolateModulesAsync(async () => {
      jest.doMock('../utils/JestCompatibilityUtils', () => ({
        JestCompatibilityUtils: { isTestEnvironment: () => false }
      }));
      const { MemorySafetyManager: MSM } = await import('../MemorySafetyManager');
      const inst: any = new MSM({ performanceMonitoringEnabled: false, memoryLeakDetectionEnabled: false });
      prodTotal = (inst as any)['_calculateTotalMemoryUsage'](
        { memoryUsageBytes: 2 * 1024 * 1024 },
        { memoryUsageBytes: 2 * 1024 * 1024 },
        { memoryUsageBytes: 2 * 1024 * 1024 }
      );
    });
    expect(prodTotal!).toBe(6 * 1024 * 1024);
  });

  it('health score responds to load in test mode and production mode', async () => {
    const mgr: any = createManager();
    await mgr.initialize();

    process.env.DEBUG = '1';
    const testScore = (mgr as any)['_calculateSystemHealthScore']({
      eventHandlerMetrics: { totalHandlers: 25, orphanedHandlers: 0 },
      resourceMetrics: { activeIntervals: 0, activeTimeouts: 0 },
      timerMetrics: { activeTimers: 0 },
      cleanupMetrics: { runningOperations: 0, conflictsPrevented: 0 }
    });
    expect(testScore).toBeGreaterThanOrEqual(51);
    expect(testScore).toBeLessThan(100);
    delete process.env.DEBUG;

    const prodScore = (mgr as any)['_calculateSystemHealthScore']({
      eventHandlerMetrics: { totalHandlers: 35, orphanedHandlers: 0 },
      resourceMetrics: { activeIntervals: 6, activeTimeouts: 6 },
      timerMetrics: { activeTimers: 25 },
      cleanupMetrics: { runningOperations: 4, conflictsPrevented: 6 }
    });
    expect(prodScore).toBeGreaterThanOrEqual(0);
    expect(prodScore).toBeLessThan(100);
  });

  it('resource usage calculations respect test-mode adjustments and production values', async () => {
    const mgr: any = createManager();
    await mgr.initialize();

    // Test mode (Jest env)
    expect((mgr as any)['_calculateEventHandlerMemoryUsage'](10)).toBe(1000);
    expect((mgr as any)['_calculateResourceMemoryUsage']({ memoryUsageMB: 1 })).toBe(900 * 1024);
    expect((mgr as any)['_calculateTimerMemoryUsage'](10)).toBe(500);
    expect((mgr as any)['_calculateCacheSize']()).toBe(1024);

    // Production via isolation mocking
    await jest.isolateModulesAsync(async () => {
      jest.doMock('../utils/JestCompatibilityUtils', () => ({
        JestCompatibilityUtils: { isTestEnvironment: () => false }
      }));
      const { MemorySafetyManager: MSM } = await import('../MemorySafetyManager');
      const inst: any = new MSM({ performanceMonitoringEnabled: false, memoryLeakDetectionEnabled: false });
      expect((inst as any)['_calculateEventHandlerMemoryUsage'](10)).toBe(10 * 1024);
      expect((inst as any)['_calculateResourceMemoryUsage']({ memoryUsageMB: 2 })).toBe(2 * 1024 * 1024);
      expect((inst as any)['_calculateTimerMemoryUsage'](10)).toBe(10 * 512);
      expect((inst as any)['_calculateCacheSize']()).toBe(10 * 1024 * 1024);
    });
  });

  it('validateCleanup warns when resources remain and passes when none remain', async () => {
    const mgr: any = createManager();
    await mgr.initialize();

    const warnSpy = jest.spyOn(mgr as any, 'logWarning');
    const infoSpy = jest.spyOn(mgr as any, 'logInfo');

    // hasRemainingResources = true
    jest.spyOn(mgr, 'getSystemMetrics').mockResolvedValueOnce({
      resources: { activeIntervals: 1, activeTimeouts: 0 },
      timers: { activeTimers: 0 },
      cleanup: { runningOperations: 0 }
    } as any);
    await (mgr as any)['_validateCleanup']();
    expect(warnSpy).toHaveBeenCalledWith('Cleanup validation found remaining resources', expect.any(Object));

    // hasRemainingResources = false
    jest.spyOn(mgr, 'getSystemMetrics').mockResolvedValueOnce({
      resources: { activeIntervals: 0, activeTimeouts: 0 },
      timers: { activeTimers: 0 },
      cleanup: { runningOperations: 0 }
    } as any);
    await (mgr as any)['_validateCleanup']();
    expect(infoSpy).toHaveBeenCalledWith('Cleanup validation passed - no remaining resources');
  });

  it('completeRunningOperations logs timeout warning on Promise.race timeout', async () => {
    const mgr: any = createManager({ shutdownTimeoutMs: 10 });
    await mgr.initialize();

    const warnSpy = jest.spyOn(mgr as any, 'logWarning');
    const cc = (mgr as any)['_cleanupCoordinator'];
    jest.spyOn(cc, 'waitForCompletion').mockImplementation(() => new Promise(() => {}));
    const p = (mgr as any)['_completeRunningOperations']();
    jest.advanceTimersByTime(15);
    await p;
    expect(warnSpy).toHaveBeenCalledWith('Timeout waiting for operations to complete, proceeding to force cleanup', expect.any(Object));
  });

  it('setupCoordination schedules performance and memory leak detection when enabled', async () => {
    const mgr: any = createManager({ performanceMonitoringEnabled: true, memoryLeakDetectionEnabled: true });
    await mgr.initialize();

    const cc = (mgr as any)['_cleanupCoordinator'];
    const spy = jest.spyOn(cc, 'scheduleCleanup');

    await (mgr as any)['_setupCoordination']();

    // Two scheduled cleanups: memory-leak-detection and performance-monitoring
    const calls = spy.mock.calls.map((c) => c[1]);
    expect(calls).toEqual(expect.arrayContaining(['memory-leak-detection', 'performance-monitoring']));
  });

  it('completeRunningOperations success path logs completion (no timeout)', async () => {
    const mgr: any = createManager({ shutdownTimeoutMs: 5000 });
    await mgr.initialize();

    const infoSpy = jest.spyOn(mgr as any, 'logInfo');
    const cc = (mgr as any)['_cleanupCoordinator'];
    jest.spyOn(cc, 'waitForCompletion').mockResolvedValueOnce(undefined);

    const p = (mgr as any)['_completeRunningOperations']();
    // Fast-forward to let the internal 1000ms delay resolve
    jest.advanceTimersByTime(1000);
    await p;
    // Advance again to ensure any microtasks flush
    jest.advanceTimersByTime(1);

    expect(infoSpy).toHaveBeenCalledWith('All running operations completed successfully');
  });

  it('stopNewOperations sets shutting down flag via shutdown phase', async () => {
    const mgr: any = createManager();
    await mgr.initialize();

    await (mgr as any)['_executeShutdownPhase']((global as any).ShutdownPhase?.STOPPING_NEW_OPERATIONS ?? (await import('../MemorySafetyManager')).ShutdownPhase.STOPPING_NEW_OPERATIONS);

    expect((mgr as any)['_isShuttingDown']).toBe(true);
  });

  it('getSystemMetrics transforms inputs and computes activeClients and timers', async () => {
    const mgr: any = createManager();
    await mgr.initialize();

    const ehr = (mgr as any)['_eventHandlerRegistry'];
    jest.spyOn(ehr, 'getMetrics').mockReturnValueOnce({
      totalHandlers: 3,
      handlersByClient: { a: [1], b: [2] },
      orphanedHandlers: 1
    } as any);

    const tcs = (mgr as any)['_timerCoordinationService'];
    jest.spyOn(tcs, 'getTimerStatistics').mockReturnValueOnce({ totalTimers: 7 } as any);

    const metrics = await mgr.getSystemMetrics();
    expect(metrics.eventHandlers.activeClients).toBe(2);
    expect(metrics.timers.activeTimers).toBe(7);
  });

  it('production health score floors at 0 with heavy load (isolate production path)', async () => {
    let score: number = -1;
    await jest.isolateModulesAsync(async () => {
      jest.doMock('../utils/JestCompatibilityUtils', () => ({ JestCompatibilityUtils: { isTestEnvironment: () => false } }));
      const { MemorySafetyManager: MSM } = await import('../MemorySafetyManager');
      const inst: any = new MSM({ performanceMonitoringEnabled: false, memoryLeakDetectionEnabled: false });

      score = (inst as any)['_calculateSystemHealthScore']({
        eventHandlerMetrics: { totalHandlers: 100, orphanedHandlers: 5 },
        resourceMetrics: { activeIntervals: 100, activeTimeouts: 100 },
        timerMetrics: { activeTimers: 100 },
        cleanupMetrics: { runningOperations: 10, conflictsPrevented: 100 }
      });
    });
    expect(score).toBe(0);
  });

  it('forceCleanup invokes shutdown on all components', async () => {
    const mgr: any = createManager();
    await mgr.initialize();

    const ehr = (mgr as any)['_eventHandlerRegistry'];
    const tcs = (mgr as any)['_timerCoordinationService'];
    const cc = (mgr as any)['_cleanupCoordinator'];

    const ehrSpy = jest.spyOn(ehr, 'shutdown').mockResolvedValue(undefined);
    const tcsSpy = jest.spyOn(tcs, 'shutdown').mockResolvedValue(undefined);
    const ccSpy = jest.spyOn(cc, 'shutdown').mockResolvedValue(undefined);

    await (mgr as any)['_forceCleanup']();

    expect(ehrSpy).toHaveBeenCalled();
    expect(tcsSpy).toHaveBeenCalled();
    expect(ccSpy).toHaveBeenCalled();
  });


  it('logDebug outputs only in dev/DEBUG env', async () => {
    const mgr: any = createManager();
    await mgr.initialize();
    const debugSpy = jest.spyOn(console, 'debug').mockImplementation(() => undefined);

    // Not in dev or DEBUG -> no call
    delete process.env.NODE_ENV; delete process.env.DEBUG;
    (mgr as any).logDebug('no-op');
    expect(debugSpy).not.toHaveBeenCalled();

    // Enable via NODE_ENV
    process.env.NODE_ENV = 'development';
    (mgr as any).logDebug('dev');
    // Enable via DEBUG
    process.env.NODE_ENV = 'test'; process.env.DEBUG = '1';
    (mgr as any).logDebug('dbg');

    expect(debugSpy).toHaveBeenCalledTimes(2);

    debugSpy.mockRestore();
    delete process.env.DEBUG; delete process.env.NODE_ENV;
  });

  it('updatePerformanceMetrics warns when overhead exceeds 5% (production mode)', async () => {
    await jest.isolateModulesAsync(async () => {
      jest.doMock('../utils/JestCompatibilityUtils', () => ({
        JestCompatibilityUtils: { isTestEnvironment: () => false }
      }));
      const { MemorySafetyManager: MSM } = await import('../MemorySafetyManager');
      const inst: any = new MSM({ performanceMonitoringEnabled: false, memoryLeakDetectionEnabled: false });
      // Baseline and current to produce >5% overhead
      (inst as any)['_performanceBaseline'] = 100;
      jest.spyOn(inst as any, '_measurePerformanceBaseline').mockReturnValue(110);
      const warnSpy = jest.spyOn(inst as any, 'logWarning');
      await (inst as any)['_updatePerformanceMetrics']();
      expect(warnSpy).toHaveBeenCalledWith('Performance overhead exceeds 5%', { overhead: expect.any(Number) });
    });
  });

  it('explicitly starts performance and leak detection intervals', async () => {
  });

  it('explicitly triggers created intervals to execute callbacks (cover arrow lines)', async () => {
    const mgr: any = createManager({ performanceMonitoringEnabled: false, memoryLeakDetectionEnabled: false });
    await mgr.initialize();

    const before = mgr.getResourceMetrics().activeIntervals;
    (mgr as any)['_startPerformanceMonitoring']();
    (mgr as any)['_startMemoryLeakDetection']();

    // Fast-forward to ensure setInterval callbacks execute at least once
    jest.advanceTimersByTime(60000);
    jest.advanceTimersByTime(300000);

    const after = mgr.getResourceMetrics().activeIntervals;
    expect(after).toBeGreaterThanOrEqual(before + 2);
  });

  it('updateMetrics triggers component metric updates', async () => {
    const mgr: any = createManager();
    await mgr.initialize();

    const ehr = (mgr as any)['_eventHandlerRegistry'];
    const cc = (mgr as any)['_cleanupCoordinator'];
    const ehrSpy = jest.spyOn(ehr, 'getMetrics');
    const ccSpy = jest.spyOn(cc, 'updateMetrics');

    mgr.updateMetrics();

    expect(ehrSpy).toHaveBeenCalled();
    expect(ccSpy).toHaveBeenCalled();
  });

  it('executeShutdownPhase covers all phases quickly and default error path', async () => {
    const mgr: any = createManager();
    await mgr.initialize();

    // Make the potentially long-running phase complete immediately
    jest.spyOn(mgr as any, '_completeRunningOperations').mockResolvedValueOnce(undefined);

    await (mgr as any)['_executeShutdownPhase'](ShutdownPhase.STOPPING_NEW_OPERATIONS);
    await (mgr as any)['_executeShutdownPhase'](ShutdownPhase.COMPLETING_RUNNING_OPERATIONS);
    await (mgr as any)['_executeShutdownPhase'](ShutdownPhase.FORCE_CLEANUP);
    await (mgr as any)['_executeShutdownPhase'](ShutdownPhase.VALIDATION);

    await expect((mgr as any)['_executeShutdownPhase']('unknown')).rejects.toThrow('Unknown shutdown phase');
  });


  it('doInitialize error path logs and rethrows', async () => {
    const mgr: any = createManager();
    const err = new Error('init-fail');
    const spy = jest.spyOn(mgr as any, '_initializeComponents').mockRejectedValueOnce(err);
    const errSpy = jest.spyOn(mgr as any, 'logError');
    await expect(mgr.initialize()).rejects.toThrow('init-fail');
    expect(spy).toHaveBeenCalled();
    expect(errSpy).toHaveBeenCalledWith('Failed to initialize Memory Safety System', err, expect.any(Object));
  });

  it('shutdown success reaches COMPLETE phase (skips FORCE_CLEANUP when disabled)', async () => {
    const mgr: any = createManager({ emergencyCleanupEnabled: false });
    await mgr.initialize();
    await mgr.shutdown();
    expect((mgr as any)['_shutdownPhase']).toBe(ShutdownPhase.COMPLETE);
  });

  it('shutdown completion logs include final metrics (line coverage)', async () => {
    const mgr: any = createManager({ emergencyCleanupEnabled: false });
    await mgr.initialize();
    // Avoid timer-based delay in _completeRunningOperations to prevent hanging under jest timer mocks
    jest.spyOn(mgr as any, '_completeRunningOperations').mockResolvedValue(undefined as any);
    const cspy = jest.spyOn(console, 'log');
    await (mgr as any).doShutdown();
    const calls = cspy.mock.calls.filter((c) => typeof c[0] === 'string' && c[0].includes('Memory Safety System shutdown completed'));
    expect(calls.length).toBe(1);
    expect(typeof calls[0][1]).toBe('string');
    expect(calls[0][1]).toContain('"finalMetrics"');
  });

  it('doShutdown early-exit when already shutting down', async () => {
    const mgr: any = createManager();
    await mgr.initialize();
    (mgr as any)['_isShuttingDown'] = true;
    const warnSpy = jest.spyOn(mgr as any, 'logWarning');
    await (mgr as any).doShutdown();
    expect(warnSpy).toHaveBeenCalledWith('Shutdown already in progress');
  });

  it('setupCoordination with flags disabled does not schedule any operations', async () => {
    const mgr: any = createManager({ performanceMonitoringEnabled: false, memoryLeakDetectionEnabled: false });
    await mgr.initialize();
    const cc = (mgr as any)['_cleanupCoordinator'];
    const spy = jest.spyOn(cc, 'scheduleCleanup');
    await (mgr as any)['_setupCoordination']();
    expect(spy).not.toHaveBeenCalled();
  });

  it('calculatePerformanceOverhead returns 0 when baseline is 0 (production path)', async () => {
    await jest.isolateModulesAsync(async () => {
      jest.doMock('../utils/JestCompatibilityUtils', () => ({ JestCompatibilityUtils: { isTestEnvironment: () => false } }));
      const { MemorySafetyManager: MSM } = await import('../MemorySafetyManager');
      const inst: any = new MSM({ performanceMonitoringEnabled: false, memoryLeakDetectionEnabled: false });
      (inst as any)['_performanceBaseline'] = 0;
      const val = (inst as any)['_calculatePerformanceOverhead']();
      expect(val).toBe(0);
    });
  });

  it('invokes scheduled callbacks from setupCoordination to cover async wrappers', async () => {
    const mgr: any = createManager({ performanceMonitoringEnabled: true, memoryLeakDetectionEnabled: true });
    await mgr.initialize();
    const cc = (mgr as any)['_cleanupCoordinator'];
    const spy = jest.spyOn(cc, 'scheduleCleanup');
    await (mgr as any)['_setupCoordination']();

    // Execute the registered callbacks (3rd arg)
    for (const call of spy.mock.calls) {
      const fn = call[2] as Function;
      await fn();
    }

    expect(spy).toHaveBeenCalled();
  });

  it('exposes service version via getServiceVersion()', async () => {
    const mgr: any = createManager();
    await mgr.initialize();
    expect((mgr as any).getServiceVersion()).toBe('1.0.0');
  });


  it('production health score stays 100 with no load (false branches)', async () => {
    await jest.isolateModulesAsync(async () => {
      jest.doMock('../utils/JestCompatibilityUtils', () => ({ JestCompatibilityUtils: { isTestEnvironment: () => false } }));
      const { MemorySafetyManager: MSM } = await import('../MemorySafetyManager');
      const inst: any = new MSM({ performanceMonitoringEnabled: false, memoryLeakDetectionEnabled: false });
      const score = (inst as any)['_calculateSystemHealthScore']({
        eventHandlerMetrics: { totalHandlers: 0, orphanedHandlers: 0 },
        resourceMetrics: { activeIntervals: 0, activeTimeouts: 0 },
        timerMetrics: { activeTimers: 0 },
        cleanupMetrics: { runningOperations: 0, conflictsPrevented: 0 }
      });
      expect(score).toBe(100);
    });
  });

  it('completeRunningOperations does not await coordinator when testMode=false', async () => {
    const mgr: any = createManager({ cleanupCoordinatorConfig: { testMode: false } as any });
    await mgr.initialize();
    const cc = (mgr as any)['_cleanupCoordinator'];
    const spy = jest.spyOn(cc, 'waitForCompletion');

    const p = (mgr as any)['_completeRunningOperations']();
    jest.advanceTimersByTime(1000);
    await p;

    expect(spy).not.toHaveBeenCalled();
  });

  it('updatePerformanceMetrics does not warn when overhead <= 5% (production path)', async () => {
    await jest.isolateModulesAsync(async () => {
      jest.doMock('../utils/JestCompatibilityUtils', () => ({ JestCompatibilityUtils: { isTestEnvironment: () => false } }));
      const { MemorySafetyManager: MSM } = await import('../MemorySafetyManager');
      const inst: any = new MSM({ performanceMonitoringEnabled: false, memoryLeakDetectionEnabled: false });
      (inst as any)['_performanceBaseline'] = 100;
      jest.spyOn(inst as any, '_measurePerformanceBaseline').mockReturnValue(104);
      const warnSpy = jest.spyOn(inst as any, 'logWarning');
      await (inst as any)['_updatePerformanceMetrics']();
      expect(warnSpy).not.toHaveBeenCalled();
    });
  });

  it('performance overhead calculation clamps to 10% max (production path)', async () => {
    await jest.isolateModulesAsync(async () => {
      jest.doMock('../utils/JestCompatibilityUtils', () => ({
        JestCompatibilityUtils: { isTestEnvironment: () => false }
      }));
      const { MemorySafetyManager: MSM } = await import('../MemorySafetyManager');
      const inst: any = new MSM({ performanceMonitoringEnabled: false, memoryLeakDetectionEnabled: false });
      (inst as any)['_performanceBaseline'] = 100;
      jest.spyOn(inst as any, '_measurePerformanceBaseline').mockReturnValue(1300);
      const val = (inst as any)['_calculatePerformanceOverhead']();
      expect(val).toBe(10);
    });
  });

  it('resource/timer/event/cache calculations cover test-mode branches', async () => {
    const mgr: any = createManager();
    await mgr.initialize();
    expect((mgr as any)['_calculateResourceMemoryUsage']({ memoryUsageMB: 1 })).toBe(900 * 1024);
    expect((mgr as any)['_calculateTimerMemoryUsage'](3)).toBe(150);
    expect((mgr as any)['_calculateEventHandlerMemoryUsage'](4)).toBe(400);
    expect((mgr as any)['_calculateCacheSize']()).toBe(1024);
  });

  it('resource/timer/event/cache calculations cover production branches', async () => {
    await jest.isolateModulesAsync(async () => {
      jest.doMock('../utils/JestCompatibilityUtils', () => ({ JestCompatibilityUtils: { isTestEnvironment: () => false } }));
      const { MemorySafetyManager: MSM } = await import('../MemorySafetyManager');
      const inst: any = new MSM({ performanceMonitoringEnabled: false, memoryLeakDetectionEnabled: false });
      expect((inst as any)['_calculateResourceMemoryUsage']({ memoryUsageMB: 2 })).toBe(2 * 1024 * 1024);
      expect((inst as any)['_calculateTimerMemoryUsage'](2)).toBe(1024);
      expect((inst as any)['_calculateEventHandlerMemoryUsage'](3)).toBe(3 * 1024);
      expect((inst as any)['_calculateCacheSize']()).toBe(10 * 1024 * 1024);
    });
  });

  it('production health score thresholds and penalties (production path)', async () => {
    await jest.isolateModulesAsync(async () => {
      jest.doMock('../utils/JestCompatibilityUtils', () => ({ JestCompatibilityUtils: { isTestEnvironment: () => false } }));
      const { MemorySafetyManager: MSM } = await import('../MemorySafetyManager');
      const inst: any = new MSM({ performanceMonitoringEnabled: false, memoryLeakDetectionEnabled: false });
      const base: any = { resourceMetrics: { activeIntervals: 0, activeTimeouts: 0 }, timerMetrics: { activeTimers: 0 }, cleanupMetrics: { runningOperations: 0, conflictsPrevented: 0 }, eventHandlerMetrics: { orphanedHandlers: 0 } };
      let score = (inst as any)['_calculateSystemHealthScore']({ ...base, eventHandlerMetrics: { totalHandlers: 11, orphanedHandlers: 0 } });
      expect(score).toBeLessThan(100);
      score = (inst as any)['_calculateSystemHealthScore']({ ...base, eventHandlerMetrics: { totalHandlers: 21, orphanedHandlers: 0 } });
      expect(score).toBeLessThan(100);
      score = (inst as any)['_calculateSystemHealthScore']({ ...base, eventHandlerMetrics: { totalHandlers: 31, orphanedHandlers: 0 } });
      expect(score).toBeLessThan(100);
      score = (inst as any)['_calculateSystemHealthScore']({ eventHandlerMetrics: { totalHandlers: 0, orphanedHandlers: 1 }, resourceMetrics: { activeIntervals: 6, activeTimeouts: 6 }, timerMetrics: { activeTimers: 21 }, cleanupMetrics: { runningOperations: 4, conflictsPrevented: 6 } });
      expect(score).toBeGreaterThanOrEqual(0);
    });
  });

  // Target specific uncovered lines with enhanced precision
  it('covers constructor execution path with complete config (line 172)', () => {
    // Line 172: constructor execution - need to actually instantiate with full config
    const mgr = new (MemorySafetyManager as any)({
      eventHandlerConfig: {
        maxHandlersPerClient: 50,
        maxGlobalHandlers: 5000,
        cleanupIntervalMs: 60000
      },
      resourceManagerConfig: {
        maxIntervals: 15,
        maxTimeouts: 15,
        maxCacheSize: 25 * 1024 * 1024,
        memoryThresholdMB: 75
      },
      timerCoordinationConfig: {
        maxConcurrentTimers: 30,
        defaultTimeoutMs: 15000,
        cleanupIntervalMs: 120000
      },
      cleanupCoordinatorConfig: {
        maxConcurrentOperations: 3,
        defaultTimeout: 15000,
        maxRetries: 2,
        conflictDetectionEnabled: false,
        testMode: true
      },
      shutdownTimeoutMs: 15000,
      emergencyCleanupEnabled: false,
      performanceMonitoringEnabled: true,
      memoryLeakDetectionEnabled: true
    });
    expect(mgr).toBeDefined();
    expect((mgr as any)._config.shutdownTimeoutMs).toBe(15000);
  });

  it('covers logError method execution (line 250)', async () => {
    const mgr: any = createManager();
    const errorSpy = jest.spyOn(console, 'error').mockImplementation();

    // Line 250: logError execution
    (mgr as any).logError('Test error', new Error('test'), { detail: 'value' });
    expect(errorSpy).toHaveBeenCalledWith(
      expect.stringContaining('[ERROR] MemorySafetyManager: Test error - test'),
      expect.stringContaining('"detail"')
    );

    errorSpy.mockRestore();
  });

  it('covers logDebug in development environment (line 258)', async () => {
    const mgr: any = createManager();
    const originalEnv = process.env.NODE_ENV;
    const debugSpy = jest.spyOn(console, 'debug').mockImplementation();

    try {
      process.env.NODE_ENV = 'development';
      // Line 258: logDebug execution in development
      (mgr as any).logDebug('Debug message', { debug: 'data' });
      expect(debugSpy).toHaveBeenCalledWith(
        expect.stringContaining('[DEBUG] MemorySafetyManager: Debug message'),
        expect.stringContaining('"debug"')
      );
    } finally {
      process.env.NODE_ENV = originalEnv;
      debugSpy.mockRestore();
    }
  });

  it('covers timeout error handling with non-Error object (line 520)', async () => {
    const mgr: any = createManager({ shutdownTimeoutMs: 50 });
    await mgr.initialize();

    const warnSpy = jest.spyOn(mgr as any, 'logWarning');

    // Mock Promise.race to throw non-Error object to trigger String(error) path
    const originalRace = Promise.race;
    Promise.race = jest.fn().mockRejectedValue('timeout string error');

    try {
      await (mgr as any)._completeRunningOperations();
      // Line 520: error instanceof Error ? error.message : String(error)
      // This should trigger the String(error) branch
      expect(warnSpy).toHaveBeenCalledWith(
        'Timeout waiting for operations to complete, proceeding to force cleanup',
        expect.objectContaining({ error: 'timeout string error' })
      );
    } finally {
      Promise.race = originalRace;
    }
  });

  it('covers activeClients calculation with null handlersByClient (line 600)', async () => {
    const mgr: any = createManager();
    await mgr.initialize();

    // Test both null and undefined handlersByClient to trigger the || {} fallback
    const mockRegistry1 = {
      getMetrics: () => ({
        totalHandlers: 5,
        handlersByClient: null, // This should trigger || {} fallback
        orphanedHandlers: 0
      })
    };
    (mgr as any)._eventHandlerRegistry = mockRegistry1;

    let metrics = await mgr.getSystemMetrics();
    // Line 600: Object.keys(eventHandlerMetrics?.handlersByClient || {}).length
    expect(metrics.eventHandlers.activeClients).toBe(0);

    // Test with actual handlersByClient data
    const mockRegistry2 = {
      getMetrics: () => ({
        totalHandlers: 8,
        handlersByClient: { client1: ['handler1'], client2: ['handler2'], client3: ['handler3'] },
        orphanedHandlers: 1
      })
    };
    (mgr as any)._eventHandlerRegistry = mockRegistry2;

    metrics = await mgr.getSystemMetrics();
    expect(metrics.eventHandlers.activeClients).toBe(3);
  });

  it('covers test-mode health score deduction branches (lines 696, 703-707)', async () => {
    const mgr: any = createManager();
    await mgr.initialize();

    // Ensure test mode
    jest.spyOn(mgr as any, '_isTestMode').mockReturnValue(true);

    const mockMetrics = {
      eventHandlerMetrics: { totalHandlers: 45, orphanedHandlers: 2 }, // Line 696: > 40
      resourceMetrics: { activeIntervals: 25, activeTimeouts: 25 },     // Lines 703-704: > 20
      timerMetrics: { activeTimers: 55 },                               // Line 705: > 50
      cleanupMetrics: { runningOperations: 15, conflictsPrevented: 15 } // Lines 706-707: > 10
    };

    const score = (mgr as any)._calculateSystemHealthScore(mockMetrics);
    expect(score).toBeLessThan(100); // All deduction branches triggered
    expect(score).toBeGreaterThanOrEqual(51); // Test mode minimum
  });

  it('covers memory leak detection threshold with null config (line 785)', async () => {
    // Create manager with null resourceManagerConfig to test the || 100 fallback
    const mgr: any = createManager({
      resourceManagerConfig: null // This should trigger the || 100 fallback
    });
    await mgr.initialize();

    // Mock getSystemMetrics to return high memory usage
    jest.spyOn(mgr, 'getSystemMetrics').mockResolvedValue({
      totalMemoryUsageBytes: 150 * 1024 * 1024 // 150MB > 100MB default
    } as any);

    const warnSpy = jest.spyOn(mgr as any, 'logWarning');

    await (mgr as any)._performMemoryLeakDetection();
    // Line 785: const threshold = this._config.resourceManagerConfig.memoryThresholdMB || 100;
    expect(warnSpy).toHaveBeenCalledWith(
      'Memory usage exceeds threshold',
      expect.objectContaining({
        threshold: 100 * 1024 * 1024 // 100MB default fallback
      })
    );

    // Also test with explicit memoryThresholdMB set to 0 to trigger || 100
    const mgr2: any = createManager({
      resourceManagerConfig: { memoryThresholdMB: 0 } // 0 is falsy, should trigger || 100
    });
    await mgr2.initialize();

    jest.spyOn(mgr2, 'getSystemMetrics').mockResolvedValue({
      totalMemoryUsageBytes: 150 * 1024 * 1024
    } as any);

    const warnSpy2 = jest.spyOn(mgr2 as any, 'logWarning');
    await (mgr2 as any)._performMemoryLeakDetection();
    expect(warnSpy2).toHaveBeenCalledWith(
      'Memory usage exceeds threshold',
      expect.objectContaining({
        threshold: 100 * 1024 * 1024 // Should still be 100MB default
      })
    );
  });

  it('covers resource memory calculation with null/undefined values (lines 832, 838)', async () => {
    const mgr: any = createManager();
    await mgr.initialize();

    // Test line 832 in test mode with null resourceMetrics
    jest.spyOn(mgr as any, '_isTestMode').mockReturnValue(true);

    // Line 832: const baseMemory = resourceMetrics.memoryUsageMB || 0;
    let result = (mgr as any)._calculateResourceMemoryUsage({ memoryUsageMB: null });
    expect(result).toBe(0); // null || 0 = 0

    result = (mgr as any)._calculateResourceMemoryUsage({ memoryUsageMB: undefined });
    expect(result).toBe(0); // undefined || 0 = 0

    result = (mgr as any)._calculateResourceMemoryUsage({});
    expect(result).toBe(0); // missing property || 0 = 0

    // Test the special case baseMemory === 1
    result = (mgr as any)._calculateResourceMemoryUsage({ memoryUsageMB: 1 });
    expect(result).toBe(900 * 1024); // 900KB special case

    // Test negative value with Math.max(0, baseMemory)
    result = (mgr as any)._calculateResourceMemoryUsage({ memoryUsageMB: -5 });
    expect(result).toBe(0); // Math.max(0, -5) = 0

    // Test production mode path (line 838)
    jest.spyOn(mgr as any, '_isTestMode').mockReturnValue(false);

    // Line 838: return (resourceMetrics.memoryUsageMB || 0) * 1024 * 1024;
    result = (mgr as any)._calculateResourceMemoryUsage({ memoryUsageMB: null });
    expect(result).toBe(0); // null || 0 = 0

    result = (mgr as any)._calculateResourceMemoryUsage({ memoryUsageMB: 3 });
    expect(result).toBe(3 * 1024 * 1024); // 3MB

    result = (mgr as any)._calculateResourceMemoryUsage({});
    expect(result).toBe(0); // missing property || 0 = 0
  });

  it('covers remaining uncovered lines with direct instantiation', () => {
    // Direct constructor call to ensure line 172 is covered
    const config = {
      performanceMonitoringEnabled: undefined, // Test falsy to trigger ?? true (line 208)
      memoryLeakDetectionEnabled: null         // Test falsy to trigger ?? true (line 209)
    };

    const mgr = new (MemorySafetyManager as any)(config);
    expect(mgr).toBeDefined();
    expect((mgr as any)._config.performanceMonitoringEnabled).toBe(true);
    expect((mgr as any)._config.memoryLeakDetectionEnabled).toBe(true);
  });

  it('covers logError with null details (line 250)', () => {
    const mgr: any = createManager();
    const errorSpy = jest.spyOn(console, 'error').mockImplementation();

    // Line 250: Test the ternary operator details ? JSON.stringify(...) : ''
    (mgr as any).logError('Test error', new Error('test'), null);
    expect(errorSpy).toHaveBeenCalledWith(
      expect.stringContaining('[ERROR] MemorySafetyManager: Test error - test'),
      '' // null details should result in empty string
    );

    errorSpy.mockRestore();
  });

  it('covers memory leak detection with explicit threshold (line 785)', async () => {
    // Test with explicit memoryThresholdMB to ensure the || 100 is not triggered
    const mgr: any = createManager({
      resourceManagerConfig: { memoryThresholdMB: 50 } // Explicit value
    });
    await mgr.initialize();

    jest.spyOn(mgr, 'getSystemMetrics').mockResolvedValue({
      totalMemoryUsageBytes: 60 * 1024 * 1024 // 60MB > 50MB threshold
    } as any);

    const warnSpy = jest.spyOn(mgr as any, 'logWarning');

    await (mgr as any)._performMemoryLeakDetection();
    // Line 785: Should use the explicit 50MB threshold, not the default
    expect(warnSpy).toHaveBeenCalledWith(
      'Memory usage exceeds threshold',
      expect.objectContaining({
        threshold: 50 * 1024 * 1024 // 50MB explicit threshold
      })
    );
  });

});
