/** Additional surgical tests to close remaining branches for MemorySafeResourceManager */
import { MemorySafeResourceManager } from '../MemorySafeResourceManager';

class T extends MemorySafeResourceManager {
  protected async doInitialize(): Promise<void> {}
  protected async doShutdown(): Promise<void> {}
}

describe('MSRM – Close remaining branches', () => {
  beforeEach(() => {
    jest.useFakeTimers();
    process.env.NODE_ENV = 'test';
    (global as any).JEST_WORKER_ID = '1';
  });
  afterEach(() => jest.useRealTimers());

  it('createSharedResource reuse existing path (366-371)', () => {
    const m = new T();
    // First create
    const first = (m as any).createSharedResource(() => ({ x: 1 }), () => {}, 'reuse');
    // Second call should reuse existing
    const second = (m as any).createSharedResource(() => ({ x: 2 }), () => {}, 'reuse');
    expect(first.resource).toBe(second.resource);
    // Release via returned function exercises release path indirectly
    (second as any).releaseRef();
  });

  it('createSharedResource returns object with releaseRef and returns created resource (403-407)', () => {
    const m = new T();
    const r = (m as any).createSharedResource(() => ({ y: 1 }), () => {}, 'foo');
    expect(typeof r.releaseRef).toBe('function');
    expect(r.resource).toEqual({ y: 1 });
  });
});

