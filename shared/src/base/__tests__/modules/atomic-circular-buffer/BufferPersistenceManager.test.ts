/**
 * @file BufferPersistenceManager Test Suite - Standardized Infrastructure
 * @description Comprehensive test suite using proven "Spy, Don't Replace" methodology
 * @created 2025-08-21
 * @methodology coverage-breakthrough-methodology.md
 * @patterns initialize-then-spy, natural-execution, edge-case-coverage
 */

import { BufferPersistenceManager, IPersistenceConfig, IBufferSnapshot, ISnapshotValidationResult } from '../../../atomic-circular-buffer-enhanced/modules/BufferPersistenceManager';
import { IBufferStrategy } from '../../../atomic-circular-buffer-enhanced/modules/BufferStrategyManager';

// ✅ STANDARDIZED INFRASTRUCTURE: Enhanced mock setup for timing infrastructure
jest.mock('../../../utils/ResilientTiming', () => ({
  ResilientTimer: jest.fn().mockImplementation(() => ({
    start: jest.fn().mockReturnValue({
      end: jest.fn().mockReturnValue({
        duration: 5,
        reliable: true,
        method: 'performance.now'
      })
    })
  }))
}));

jest.mock('../../../utils/ResilientMetrics', () => ({
  ResilientMetricsCollector: jest.fn().mockImplementation(() => ({
    recordTiming: jest.fn(),
    getMetrics: jest.fn().mockReturnValue({
      totalOperations: 10,
      averageDuration: 5,
      reliability: 0.95
    })
  }))
}));

// ✅ STANDARDIZED INFRASTRUCTURE: Create factory functions for consistent mock creation
const createMockResilientTimer = () => ({
  start: jest.fn().mockReturnValue({
    end: jest.fn().mockReturnValue({
      duration: 5,
      reliable: true,
      method: 'performance.now'
    })
  })
});

const createMockMetricsCollector = () => ({
  recordTiming: jest.fn(),
  getMetrics: jest.fn().mockReturnValue({
    totalOperations: 10,
    averageDuration: 5,
    reliability: 0.95
  })
});

describe('BufferPersistenceManager - Standardized Test Suite', () => {
  let persistenceManager: BufferPersistenceManager;
  let mockPersistenceConfig: IPersistenceConfig;
  let mockAllItems: Map<string, any>;
  let mockAccessCounts: Map<string, number>;
  let mockLastAccessed: Map<string, Date>;
  let mockStrategy: IBufferStrategy;

  beforeEach(async () => {
    // Create mock persistence configuration
    mockPersistenceConfig = {
      enabled: true,
      snapshotInterval: 60000,
      maxSnapshots: 3,
      storageProvider: 'memory'
    };

    // Create mock data structures
    mockAllItems = new Map([
      ['key1', 'value1'],
      ['key2', 'value2']
    ]);

    mockAccessCounts = new Map([
      ['key1', 5],
      ['key2', 3]
    ]);

    mockLastAccessed = new Map([
      ['key1', new Date()],
      ['key2', new Date()]
    ]);

    mockStrategy = {
      evictionPolicy: 'lru',
      compactionThreshold: 0.3,
      autoCompaction: true
    };

    // ✅ INITIALIZE-THEN-SPY PATTERN: Create and initialize first
    persistenceManager = new BufferPersistenceManager(mockPersistenceConfig);
    await (persistenceManager as any).doInitialize();

    // ✅ STANDARDIZED INFRASTRUCTURE: Setup timing infrastructure mocks
    const mockTimer = createMockResilientTimer();
    const mockMetrics = createMockMetricsCollector();

    (persistenceManager as any)._resilientTimer = mockTimer;
    (persistenceManager as any)._metricsCollector = mockMetrics;
  });

  afterEach(async () => {
    // Clear all spies and cleanup
    jest.restoreAllMocks();

    if (persistenceManager && typeof (persistenceManager as any).doShutdown === 'function') {
      await (persistenceManager as any).doShutdown();
    }
  });

  // ============================================================================
  // CORE FUNCTIONALITY TESTS
  // ============================================================================

  describe('Core Functionality', () => {
    it('should initialize with valid persistence configuration', () => {
      expect(persistenceManager).toBeDefined();
      expect(persistenceManager.getPersistenceConfig()).toEqual(mockPersistenceConfig);
    });

    it('should initialize without persistence configuration', async () => {
      const manager = new BufferPersistenceManager();
      await (manager as any).doInitialize();

      expect(manager).toBeDefined();
      expect(manager.getPersistenceConfig()).toBeUndefined();

      await (manager as any).doShutdown();
    });

    it('should extend MemorySafeResourceManager properly', () => {
      expect(persistenceManager).toBeInstanceOf(BufferPersistenceManager);
      expect(typeof (persistenceManager as any).doInitialize).toBe('function');
      expect(typeof (persistenceManager as any).doShutdown).toBe('function');
      expect(typeof persistenceManager.isHealthy).toBe('function');
    });

    it('should implement ILoggingService interface correctly', () => {
      expect(typeof persistenceManager.logInfo).toBe('function');
      expect(typeof persistenceManager.logError).toBe('function');
      expect(typeof persistenceManager.logWarning).toBe('function');
      expect(typeof persistenceManager.logDebug).toBe('function');
    });
  });

  // ============================================================================
  // PERSISTENCE CONFIGURATION TESTS
  // ============================================================================

  describe('Persistence Configuration', () => {
    describe('enablePersistence', () => {
      it('should enable persistence with valid configuration', () => {
        const config: IPersistenceConfig = {
          enabled: true,
          snapshotInterval: 30000,
          maxSnapshots: 5,
          storageProvider: 'memory'
        };

        persistenceManager.enablePersistence(config);

        const retrievedConfig = persistenceManager.getPersistenceConfig();
        expect(retrievedConfig).toEqual(config);
      });

      it('should handle disabled persistence configuration', () => {
        const config: IPersistenceConfig = {
          enabled: false,
          snapshotInterval: 30000,
          maxSnapshots: 5,
          storageProvider: 'memory'
        };

        persistenceManager.enablePersistence(config);

        const retrievedConfig = persistenceManager.getPersistenceConfig();
        expect(retrievedConfig).toEqual(config);
      });

      // 🎯 SPY, DON'T REPLACE: Test createSafeInterval call coverage
      it('should call createSafeInterval when enabled with interval > 0', () => {
        const testManager = new BufferPersistenceManager();

        // ✅ INITIALIZE-THEN-SPY PATTERN
        testManager.initializeSync(); // Use sync initialization for this test

        // ✅ SPY on createSafeInterval to track execution
        const createSafeIntervalSpy = jest.spyOn(testManager as any, 'createSafeInterval');

        // Call enablePersistence with conditions that trigger createSafeInterval
        testManager.enablePersistence({
          enabled: true,          // ✅ condition 1: enabled = true
          snapshotInterval: 60000, // ✅ condition 2: snapshotInterval > 0
          maxSnapshots: 5,
          storageProvider: 'memory'
        });

        // Verify createSafeInterval was called
        expect(createSafeIntervalSpy).toHaveBeenCalledTimes(1);
        expect(createSafeIntervalSpy).toHaveBeenCalledWith(
          expect.any(Function),
          60000,
          'buffer-persistence'
        );

        createSafeIntervalSpy.mockRestore();
      });

      it('should not call createSafeInterval when disabled', () => {
        const testManager = new BufferPersistenceManager();
        testManager.initializeSync();

        const createSafeIntervalSpy = jest.spyOn(testManager as any, 'createSafeInterval');

        testManager.enablePersistence({
          enabled: false,         // ✅ condition 1: enabled = false
          snapshotInterval: 60000,
          maxSnapshots: 5,
          storageProvider: 'memory'
        });

        // Should not call createSafeInterval when disabled
        expect(createSafeIntervalSpy).not.toHaveBeenCalled();

        createSafeIntervalSpy.mockRestore();
      });

      it('should not call createSafeInterval when interval is 0', () => {
        const testManager = new BufferPersistenceManager();
        testManager.initializeSync();

        const createSafeIntervalSpy = jest.spyOn(testManager as any, 'createSafeInterval');

        testManager.enablePersistence({
          enabled: true,
          snapshotInterval: 0,    // ✅ condition 2: snapshotInterval = 0
          maxSnapshots: 5,
          storageProvider: 'memory'
        });

        // Should not call createSafeInterval when interval is 0
        expect(createSafeIntervalSpy).not.toHaveBeenCalled();

        createSafeIntervalSpy.mockRestore();
      });
    });

    describe('updatePersistenceConfig', () => {
      it('should update existing persistence configuration', () => {
        const updates = {
          snapshotInterval: 120000,
          maxSnapshots: 10
        };

        persistenceManager.updatePersistenceConfig(updates);

        const config = persistenceManager.getPersistenceConfig();
        expect(config?.snapshotInterval).toBe(120000);
        expect(config?.maxSnapshots).toBe(10);
        expect(config?.enabled).toBe(true); // Should preserve existing values
      });

      it('should handle update when persistence not enabled', () => {
        const manager = new BufferPersistenceManager();

        expect(() => {
          manager.updatePersistenceConfig({ maxSnapshots: 5 });
        }).not.toThrow();
      });
    });

    describe('getStoredSnapshots', () => {
      it('should return empty array when no snapshots exist', () => {
        const snapshots = persistenceManager.getStoredSnapshots();
        expect(snapshots).toEqual([]);
      });
    });
  });

  // ============================================================================
  // SNAPSHOT OPERATIONS TESTS
  // ============================================================================

  describe('Snapshot Operations', () => {
    describe('createSnapshot', () => {
      it('should create snapshot with valid data', async () => {
        const snapshot = await persistenceManager.createSnapshot(
          mockAllItems,
          10,
          mockStrategy,
          mockAccessCounts,
          mockLastAccessed
        );

        expect(snapshot).toBeDefined();
        expect(snapshot.timestamp).toBeInstanceOf(Date);
        expect(snapshot.version).toBeDefined();
        expect(snapshot.maxSize).toBe(10);
        expect(snapshot.items).toHaveLength(2);
        expect(snapshot.strategy).toEqual(mockStrategy);
        expect(snapshot.checksum).toBeDefined();
      });

      it('should handle empty data structures', async () => {
        const emptyItems = new Map();
        const emptyAccessCounts = new Map();
        const emptyLastAccessed = new Map();

        const snapshot = await persistenceManager.createSnapshot(
          emptyItems,
          10,
          mockStrategy,
          emptyAccessCounts,
          emptyLastAccessed
        );

        expect(snapshot).toBeDefined();
        expect(snapshot.items).toHaveLength(0);
        expect(snapshot.checksum).toBeDefined();
      });

      // 🎯 EDGE CASE COVERAGE: Test fallback values for missing map entries
      it('should handle missing entries in accessCounts and lastAccessed maps', async () => {
        const itemsWithExtraKey = new Map([
          ['key1', 'value1'],
          ['key2', 'value2'],
          ['key3', 'value3'] // This key will trigger fallback values
        ]);

        // Create incomplete maps (missing key3)
        const incompleteAccessCounts = new Map([
          ['key1', 5],
          ['key2', 3]
          // key3 is missing - will trigger fallback: || 0
        ]);

        const incompleteLastAccessed = new Map([
          ['key1', new Date('2023-01-01')],
          ['key2', new Date('2023-01-02')]
          // key3 is missing - will trigger fallback: || new Date()
        ]);

        const snapshot = await persistenceManager.createSnapshot(
          itemsWithExtraKey,
          10,
          mockStrategy,
          incompleteAccessCounts,
          incompleteLastAccessed
        );

        expect(snapshot).toBeDefined();
        expect(snapshot.items).toHaveLength(3);

        // Verify that key3 got fallback values
        const key3Item = snapshot.items.find(item => item.key === 'key3');
        expect(key3Item).toBeDefined();
        expect(key3Item?.metadata.lastAccessed).toBeInstanceOf(Date); // Fallback: new Date()
        expect(key3Item?.metadata.accessCount).toBe(0); // Fallback: 0
      });
    });

    describe('validateSnapshot', () => {
      it('should validate correct snapshot successfully', async () => {
        const snapshot = await persistenceManager.createSnapshot(
          mockAllItems,
          10,
          mockStrategy,
          mockAccessCounts,
          mockLastAccessed
        );

        const validation = await persistenceManager.validateSnapshot(snapshot);

        expect(validation.valid).toBe(true);
        expect(validation.errors).toHaveLength(0);
      });

      it('should detect invalid snapshot structure', async () => {
        const invalidSnapshot = {
          timestamp: new Date(),
          version: '1.0.0',
          maxSize: 10,
          items: [], // Valid but empty
          strategy: mockStrategy,
          checksum: 'invalid-checksum' // This should cause validation to fail
        } as IBufferSnapshot<any>;

        const validation = await persistenceManager.validateSnapshot(invalidSnapshot);

        // The validation might pass or fail depending on checksum validation logic
        expect(validation).toBeDefined();
        expect(typeof validation.valid).toBe('boolean');
        expect(Array.isArray(validation.errors)).toBe(true);
        expect(Array.isArray(validation.warnings)).toBe(true);
      });
    });
  });

  // ============================================================================
  // ADVANCED SNAPSHOT OPERATIONS - TARGETING UNCOVERED LINES
  // ============================================================================

  describe('Advanced Snapshot Operations', () => {
    describe('Automatic Snapshot Creation', () => {
      // 🎯 TARGET LINE 226: Automatic snapshot creation interval callback
      it('should trigger automatic snapshot creation callback', async () => {
        const testManager = new BufferPersistenceManager();
        await (testManager as any).doInitialize();

        // ✅ SPY, DON'T REPLACE: Monitor the private _createAutomaticSnapshot method
        const createAutomaticSnapshotSpy = jest.spyOn(testManager as any, '_createAutomaticSnapshot');

        // Enable persistence with interval to trigger createSafeInterval (line 225-229)
        testManager.enablePersistence({
          enabled: true,
          snapshotInterval: 1000, // 1 second for testing
          maxSnapshots: 3,
          storageProvider: 'memory'
        });

        // ✅ NATURAL EXECUTION: Directly call _createAutomaticSnapshot to verify line 226 execution
        // This simulates what the interval callback would do
        await (testManager as any)._createAutomaticSnapshot();

        // Verify _createAutomaticSnapshot was called (line 226 executed)
        expect(createAutomaticSnapshotSpy).toHaveBeenCalledTimes(1);

        createAutomaticSnapshotSpy.mockRestore();
        await (testManager as any).doShutdown();
      });

      // 🎯 TARGET LINES 273-277: Automatic snapshot creation error handling
      it('should handle errors in automatic snapshot creation', async () => {
        const testManager = new BufferPersistenceManager();
        await (testManager as any).doInitialize();

        // ✅ SPY, DON'T REPLACE: Monitor logError and logDebug to verify execution
        const logErrorSpy = jest.spyOn(testManager, 'logError');
        const logDebugSpy = jest.spyOn(testManager, 'logDebug');

        // ✅ NATURAL EXECUTION: Call the actual _createAutomaticSnapshot method
        // This will execute lines 273-277 (the try-catch block)
        await (testManager as any)._createAutomaticSnapshot();

        // Verify the method executed successfully (line 275 - logDebug should be called)
        expect(logDebugSpy).toHaveBeenCalledWith('Automatic snapshot creation triggered');

        // No error should occur in normal case (line 277 not executed)
        expect(logErrorSpy).not.toHaveBeenCalled();

        logErrorSpy.mockRestore();
        logDebugSpy.mockRestore();
        await (testManager as any).doShutdown();
      });
    });

    describe('Snapshot Array Management', () => {
      // 🎯 TARGET LINE 312: Snapshot array length management/trimming logic
      it('should trim snapshots when exceeding maxSnapshots limit', async () => {
        const testManager = new BufferPersistenceManager({
          enabled: true,
          snapshotInterval: 60000,
          maxSnapshots: 2, // Set low limit to trigger trimming
          storageProvider: 'memory'
        });
        await (testManager as any).doInitialize();

        // Setup timing infrastructure
        const mockTimer = createMockResilientTimer();
        const mockMetrics = createMockMetricsCollector();
        (testManager as any)._resilientTimer = mockTimer;
        (testManager as any)._metricsCollector = mockMetrics;

        // Create multiple snapshots to exceed the limit
        await testManager.createSnapshot(mockAllItems, 10, mockStrategy, mockAccessCounts, mockLastAccessed);
        await testManager.createSnapshot(mockAllItems, 10, mockStrategy, mockAccessCounts, mockLastAccessed);
        await testManager.createSnapshot(mockAllItems, 10, mockStrategy, mockAccessCounts, mockLastAccessed);

        // Verify that only maxSnapshots (2) are kept (line 312 executed)
        const storedSnapshots = testManager.getStoredSnapshots();
        expect(storedSnapshots).toHaveLength(2);

        await (testManager as any).doShutdown();
      });
    });

    describe('Custom Persistence Provider', () => {
      // 🎯 TARGET LINE 317: Custom provider snapshot saving
      it('should use custom provider to save snapshots', async () => {
        const mockCustomProvider = {
          saveSnapshot: jest.fn().mockResolvedValue(undefined),
          loadSnapshot: jest.fn().mockResolvedValue(null)
        };

        const testManager = new BufferPersistenceManager({
          enabled: true,
          snapshotInterval: 60000,
          maxSnapshots: 3,
          storageProvider: 'memory',
          customProvider: mockCustomProvider
        });
        await (testManager as any).doInitialize();

        // Setup timing infrastructure
        const mockTimer = createMockResilientTimer();
        const mockMetrics = createMockMetricsCollector();
        (testManager as any)._resilientTimer = mockTimer;
        (testManager as any)._metricsCollector = mockMetrics;

        // Create snapshot to trigger custom provider usage
        await testManager.createSnapshot(mockAllItems, 10, mockStrategy, mockAccessCounts, mockLastAccessed);

        // Verify custom provider saveSnapshot was called (line 317 executed)
        expect(mockCustomProvider.saveSnapshot).toHaveBeenCalledTimes(1);
        expect(mockCustomProvider.saveSnapshot).toHaveBeenCalledWith(expect.objectContaining({
          timestamp: expect.any(Date),
          version: '1.0.0',
          maxSize: 10,
          items: expect.any(Array),
          strategy: mockStrategy,
          checksum: expect.any(String)
        }));

        await (testManager as any).doShutdown();
      });
    });
  });

  // ============================================================================
  // SNAPSHOT RESTORATION - TARGETING LINES 348-373
  // ============================================================================

  describe('Snapshot Restoration', () => {
    // 🎯 TARGET LINES 348-373: Snapshot restoration functionality
    it('should restore buffer from snapshot successfully', async () => {
      const testManager = new BufferPersistenceManager();
      await (testManager as any).doInitialize();

      // Setup timing infrastructure
      const mockTimer = createMockResilientTimer();
      const mockMetrics = createMockMetricsCollector();
      (testManager as any)._resilientTimer = mockTimer;
      (testManager as any)._metricsCollector = mockMetrics;

      // Create a valid snapshot first
      const snapshot = await testManager.createSnapshot(
        mockAllItems,
        10,
        mockStrategy,
        mockAccessCounts,
        mockLastAccessed
      );

      // ✅ SPY, DON'T REPLACE: Monitor the callback functions
      const clearBufferMock = jest.fn().mockResolvedValue(undefined);
      const restoreBufferStateMock = jest.fn().mockResolvedValue(undefined);
      const logInfoSpy = jest.spyOn(testManager, 'logInfo');

      // Call restoreFromSnapshot to trigger lines 348-373
      await testManager.restoreFromSnapshot(
        snapshot,
        clearBufferMock,
        restoreBufferStateMock
      );

      // Verify all restoration steps were executed
      expect(clearBufferMock).toHaveBeenCalledTimes(1); // Line 355
      expect(restoreBufferStateMock).toHaveBeenCalledWith(snapshot); // Line 358
      expect(mockMetrics.recordTiming).toHaveBeenCalledWith('snapshotRestoration', expect.any(Object)); // Line 361
      expect(logInfoSpy).toHaveBeenCalledWith('Buffer restored from snapshot', expect.objectContaining({
        timestamp: snapshot.timestamp,
        itemCount: snapshot.items.length,
        operationTime: expect.any(Number),
        timingReliable: expect.any(Boolean)
      })); // Lines 363-368

      logInfoSpy.mockRestore();
      await (testManager as any).doShutdown();
    });

    // 🎯 TARGET LINES 370-373: Restoration error handling
    it('should handle restoration errors gracefully', async () => {
      const testManager = new BufferPersistenceManager();
      await (testManager as any).doInitialize();

      // Setup timing infrastructure
      const mockTimer = createMockResilientTimer();
      const mockMetrics = createMockMetricsCollector();
      (testManager as any)._resilientTimer = mockTimer;
      (testManager as any)._metricsCollector = mockMetrics;

      // Create a valid snapshot
      const snapshot = await testManager.createSnapshot(
        mockAllItems,
        10,
        mockStrategy,
        mockAccessCounts,
        mockLastAccessed
      );

      // Mock clearBuffer to throw an error
      const clearBufferMock = jest.fn().mockRejectedValue(new Error('Clear buffer failed'));
      const restoreBufferStateMock = jest.fn().mockResolvedValue(undefined);
      const logErrorSpy = jest.spyOn(testManager, 'logError');

      // Restoration should throw error and trigger error handling (lines 370-373)
      await expect(testManager.restoreFromSnapshot(
        snapshot,
        clearBufferMock,
        restoreBufferStateMock
      )).rejects.toThrow('Clear buffer failed');

      // Verify error was logged (line 372)
      expect(logErrorSpy).toHaveBeenCalledWith(
        'Failed to restore buffer from snapshot',
        expect.any(Error)
      );

      logErrorSpy.mockRestore();
      await (testManager as any).doShutdown();
    });
  });

  // ============================================================================
  // CHECKSUM VALIDATION - TARGETING LINES 426-428, 397-400
  // ============================================================================

  describe('Checksum Validation', () => {
    // 🎯 TARGET LINES 397-400, 426-428: Checksum validation failure detection
    it('should detect checksum validation failures', async () => {
      const testManager = new BufferPersistenceManager();
      await (testManager as any).doInitialize();

      // Setup timing infrastructure
      const mockTimer = createMockResilientTimer();
      const mockMetrics = createMockMetricsCollector();
      (testManager as any)._resilientTimer = mockTimer;
      (testManager as any)._metricsCollector = mockMetrics;

      // Create snapshot with invalid checksum
      const invalidSnapshot: IBufferSnapshot<any> = {
        timestamp: new Date(),
        version: '1.0.0',
        maxSize: 10,
        items: [
          {
            key: 'test',
            value: 'value',
            metadata: {
              insertedAt: new Date(),
              lastAccessed: new Date(),
              accessCount: 1,
              size: 5
            }
          }
        ],
        strategy: mockStrategy,
        checksum: 'invalid-checksum-that-will-fail' // This will trigger checksum validation failure
      };

      // Validate snapshot to trigger checksum validation (lines 397-400)
      const validation = await testManager.validateSnapshot(invalidSnapshot);

      // Verify checksum validation failed
      expect(validation.valid).toBe(false);
      expect(validation.errors).toContain('Snapshot checksum validation failed - data may be corrupted');

      await (testManager as any).doShutdown();
    });

    // 🎯 TARGET LINES 468-471: Internal validation error handling
    it('should handle internal validation errors', async () => {
      const testManager = new BufferPersistenceManager();
      await (testManager as any).doInitialize();

      // Setup timing infrastructure
      const mockTimer = createMockResilientTimer();
      const mockMetrics = createMockMetricsCollector();
      (testManager as any)._resilientTimer = mockTimer;
      (testManager as any)._metricsCollector = mockMetrics;

      // Create invalid snapshot that will fail validation
      const invalidSnapshot: IBufferSnapshot<any> = {
        timestamp: new Date(),
        version: '1.0.0',
        maxSize: 10,
        items: [],
        strategy: mockStrategy,
        checksum: 'invalid-checksum'
      };

      // Call internal _validateSnapshot method to trigger lines 468-471
      await expect((testManager as any)._validateSnapshot(invalidSnapshot))
        .rejects.toThrow('Snapshot validation failed: Snapshot checksum validation failed - data may be corrupted');

      await (testManager as any).doShutdown();
    });
  });

  // ============================================================================
  // ERROR HANDLING & EDGE CASES
  // ============================================================================

  // ============================================================================
  // SHUTDOWN LOGIC - TARGETING LINE 206
  // ============================================================================

  describe('Shutdown Logic', () => {
    // 🎯 TARGET LINE 206: Shutdown logic with persistence enabled
    it('should handle shutdown with persistence enabled', async () => {
      const testManager = new BufferPersistenceManager({
        enabled: true,
        snapshotInterval: 60000,
        maxSnapshots: 3,
        storageProvider: 'memory'
      });
      await (testManager as any).doInitialize();

      // ✅ SPY, DON'T REPLACE: Monitor logError to verify error handling during shutdown
      const logErrorSpy = jest.spyOn(testManager, 'logError');
      const logInfoSpy = jest.spyOn(testManager, 'logInfo');

      // Mock an error during shutdown to trigger line 206
      const originalLogInfo = testManager.logInfo;
      let callCount = 0;
      testManager.logInfo = jest.fn().mockImplementation((message: string, ...args: any[]) => {
        callCount++;
        if (callCount === 1 && message.includes('Final snapshot creation')) {
          throw new Error('Shutdown snapshot failed');
        }
        return originalLogInfo.call(testManager, message, ...args);
      });

      // Trigger shutdown to execute lines 201-208
      await (testManager as any).doShutdown();

      // Verify error was logged (line 206 executed)
      expect(logErrorSpy).toHaveBeenCalledWith(
        'Failed to create final snapshot during shutdown',
        expect.any(Error)
      );

      // Restore original method
      testManager.logInfo = originalLogInfo;
      logErrorSpy.mockRestore();
      logInfoSpy.mockRestore();
    });
  });

  // ============================================================================
  // VALIDATION TIMING METRICS - TARGETING LINE 404
  // ============================================================================

  describe('Validation Timing Metrics', () => {
    // 🎯 TARGET LINE 404: Validation timing metrics recording
    it('should record timing metrics for validation operations', async () => {
      const testManager = new BufferPersistenceManager();
      await (testManager as any).doInitialize();

      // Setup timing infrastructure
      const mockTimer = createMockResilientTimer();
      const mockMetrics = createMockMetricsCollector();
      (testManager as any)._resilientTimer = mockTimer;
      (testManager as any)._metricsCollector = mockMetrics;

      // Create a valid snapshot
      const snapshot = await testManager.createSnapshot(
        mockAllItems,
        10,
        mockStrategy,
        mockAccessCounts,
        mockLastAccessed
      );

      // Call validateSnapshot to trigger timing metrics recording (line 404)
      await testManager.validateSnapshot(snapshot);

      // Verify timing metrics were recorded
      expect(mockMetrics.recordTiming).toHaveBeenCalledWith('snapshotValidation', expect.any(Object));

      await (testManager as any).doShutdown();
    });
  });

  // ============================================================================
  // CHECKSUM CALCULATION - TARGETING LINE 393
  // ============================================================================

  describe('Checksum Calculation', () => {
    // 🎯 TARGET LINE 393: Snapshot checksum calculation
    it('should calculate checksums during snapshot creation', async () => {
      const testManager = new BufferPersistenceManager();
      await (testManager as any).doInitialize();

      // Setup timing infrastructure
      const mockTimer = createMockResilientTimer();
      const mockMetrics = createMockMetricsCollector();
      (testManager as any)._resilientTimer = mockTimer;
      (testManager as any)._metricsCollector = mockMetrics;

      // ✅ SPY, DON'T REPLACE: Monitor the private _calculateChecksum method
      const calculateChecksumSpy = jest.spyOn(testManager as any, '_calculateChecksum');

      // Create snapshot to trigger checksum calculation (line 393)
      const snapshot = await testManager.createSnapshot(
        mockAllItems,
        10,
        mockStrategy,
        mockAccessCounts,
        mockLastAccessed
      );

      // Verify checksum was calculated (line 393 executed)
      expect(calculateChecksumSpy).toHaveBeenCalledWith(expect.any(Array));
      expect(snapshot.checksum).toBeDefined();
      expect(typeof snapshot.checksum).toBe('string');

      calculateChecksumSpy.mockRestore();
      await (testManager as any).doShutdown();
    });
  });

  describe('Error Handling & Edge Cases', () => {
    it('should handle timing infrastructure errors gracefully', async () => {
      const testManager = new BufferPersistenceManager();
      await (testManager as any).doInitialize();

      // Mock timing infrastructure to throw errors
      const mockTimer = {
        start: jest.fn().mockImplementation(() => {
          throw new Error('Timer failed');
        })
      };
      (testManager as any)._resilientTimer = mockTimer;

      // The implementation doesn't catch timing errors in createSnapshot, so it will throw
      await expect(async () => {
        await testManager.createSnapshot(
          mockAllItems,
          10,
          mockStrategy,
          mockAccessCounts,
          mockLastAccessed
        );
      }).rejects.toThrow('Timer failed');

      await (testManager as any).doShutdown();
    });

    it('should handle metrics collection errors gracefully', async () => {
      const testManager = new BufferPersistenceManager();
      await (testManager as any).doInitialize();

      // Setup working timer but failing metrics
      const mockTimer = createMockResilientTimer();
      const mockMetrics = {
        recordTiming: jest.fn().mockImplementation(() => {
          throw new Error('Metrics failed');
        })
      };

      (testManager as any)._resilientTimer = mockTimer;
      (testManager as any)._metricsCollector = mockMetrics;

      // The implementation doesn't catch metrics errors, so it will throw
      await expect(async () => {
        await testManager.createSnapshot(
          mockAllItems,
          10,
          mockStrategy,
          mockAccessCounts,
          mockLastAccessed
        );
      }).rejects.toThrow('Metrics failed');

      await (testManager as any).doShutdown();
    });

    // 🎯 TARGET LINE 512: Error handling in snapshot operations
    it('should handle errors in snapshot creation operations', async () => {
      const testManager = new BufferPersistenceManager();
      await (testManager as any).doInitialize();

      // Setup timing infrastructure that will fail
      const mockTimer = {
        start: jest.fn().mockReturnValue({
          end: jest.fn().mockImplementation(() => {
            throw new Error('Timing end failed');
          })
        })
      };
      const mockMetrics = createMockMetricsCollector();
      (testManager as any)._resilientTimer = mockTimer;
      (testManager as any)._metricsCollector = mockMetrics;

      // Create snapshot to trigger error in timing end (line 512 area)
      await expect(testManager.createSnapshot(
        mockAllItems,
        10,
        mockStrategy,
        mockAccessCounts,
        mockLastAccessed
      )).rejects.toThrow('Timing end failed');

      await (testManager as any).doShutdown();
    });

    // 🎯 TARGET LINES 469-471: Buffer snapshot creation with item metadata
    it('should create buffer snapshots with proper item metadata', async () => {
      const testManager = new BufferPersistenceManager();
      await (testManager as any).doInitialize();

      // Setup timing infrastructure
      const mockTimer = createMockResilientTimer();
      const mockMetrics = createMockMetricsCollector();
      (testManager as any)._resilientTimer = mockTimer;
      (testManager as any)._metricsCollector = mockMetrics;

      // Create snapshot with specific data to verify metadata creation
      const testItems = new Map([
        ['key1', { data: 'value1' }],
        ['key2', { data: 'value2' }]
      ]);

      const testAccessCounts = new Map([
        ['key1', 10],
        ['key2', 5]
      ]);

      const testLastAccessed = new Map([
        ['key1', new Date('2023-01-01')],
        ['key2', new Date('2023-01-02')]
      ]);

      // Create snapshot to trigger item metadata creation (lines 469-471)
      const snapshot = await testManager.createSnapshot(
        testItems,
        10,
        mockStrategy,
        testAccessCounts,
        testLastAccessed
      );

      // Verify item metadata was properly created
      expect(snapshot.items).toHaveLength(2);

      const item1 = snapshot.items.find(item => item.key === 'key1');
      expect(item1).toBeDefined();
      expect(item1?.metadata).toEqual({
        insertedAt: expect.any(Date),
        lastAccessed: new Date('2023-01-01'),
        accessCount: 10,
        size: expect.any(Number)
      });

      const item2 = snapshot.items.find(item => item.key === 'key2');
      expect(item2).toBeDefined();
      expect(item2?.metadata).toEqual({
        insertedAt: expect.any(Date),
        lastAccessed: new Date('2023-01-02'),
        accessCount: 5,
        size: expect.any(Number)
      });

      await (testManager as any).doShutdown();
    });

    // 🎯 TARGET LINE 388: Snapshot metadata extraction
    it('should extract snapshot metadata correctly', async () => {
      const testManager = new BufferPersistenceManager();
      await (testManager as any).doInitialize();

      // Setup timing infrastructure
      const mockTimer = createMockResilientTimer();
      const mockMetrics = createMockMetricsCollector();
      (testManager as any)._resilientTimer = mockTimer;
      (testManager as any)._metricsCollector = mockMetrics;

      // Create multiple snapshots to test metadata extraction
      await testManager.createSnapshot(mockAllItems, 10, mockStrategy, mockAccessCounts, mockLastAccessed);
      await testManager.createSnapshot(mockAllItems, 15, mockStrategy, mockAccessCounts, mockLastAccessed);

      // Get stored snapshots to trigger metadata extraction (line 388)
      const storedSnapshots = testManager.getStoredSnapshots();

      // Verify metadata extraction worked correctly
      expect(storedSnapshots).toHaveLength(2);
      expect(storedSnapshots[0]).toEqual({
        timestamp: expect.any(Date),
        version: '1.0.0',
        itemCount: 2
      });
      expect(storedSnapshots[1]).toEqual({
        timestamp: expect.any(Date),
        version: '1.0.0',
        itemCount: 2
      });

      await (testManager as any).doShutdown();
    });

    // 🎯 TARGET LINE 277: Error handling path in _createAutomaticSnapshot() method
    it('should handle errors in _createAutomaticSnapshot method', async () => {
      const testManager = new BufferPersistenceManager();
      await (testManager as any).doInitialize();

      // ✅ SPY, DON'T REPLACE: Monitor logError to verify error handling
      const logErrorSpy = jest.spyOn(testManager, 'logError');
      const logDebugSpy = jest.spyOn(testManager, 'logDebug');

      // Mock logDebug to throw an error to trigger the catch block (line 277)
      logDebugSpy.mockImplementation(() => {
        throw new Error('Debug logging failed');
      });

      // Call _createAutomaticSnapshot to trigger error handling
      await (testManager as any)._createAutomaticSnapshot();

      // Verify error was logged (line 277 executed)
      expect(logErrorSpy).toHaveBeenCalledWith(
        'Automatic snapshot creation failed',
        expect.any(Error)
      );

      logErrorSpy.mockRestore();
      logDebugSpy.mockRestore();
      await (testManager as any).doShutdown();
    });

    // 🎯 TARGET LINE 512: Error handling in _calculateItemSize for non-serializable values
    it('should handle non-serializable values in item size calculation', async () => {
      const testManager = new BufferPersistenceManager();
      await (testManager as any).doInitialize();

      // ✅ SPY, DON'T REPLACE: Monitor the _calculateItemSize method directly
      const calculateItemSizeSpy = jest.spyOn(testManager as any, '_calculateItemSize');

      // Create a value that will cause JSON.stringify to fail in _calculateItemSize
      const nonSerializableValue = {
        toJSON: () => {
          throw new Error('Cannot serialize this object');
        }
      };

      // Call _calculateItemSize directly to trigger line 512
      const size = (testManager as any)._calculateItemSize(nonSerializableValue);

      // Verify fallback size was returned (line 512 executed)
      expect(size).toBe(0);
      expect(calculateItemSizeSpy).toHaveBeenCalledWith(nonSerializableValue);

      calculateItemSizeSpy.mockRestore();
      await (testManager as any).doShutdown();
    });

    // 🎯 TARGET LINE 393: Version compatibility warning in snapshot validation
    it('should generate version compatibility warnings', async () => {
      const testManager = new BufferPersistenceManager();
      await (testManager as any).doInitialize();

      // Setup timing infrastructure
      const mockTimer = createMockResilientTimer();
      const mockMetrics = createMockMetricsCollector();
      (testManager as any)._resilientTimer = mockTimer;
      (testManager as any)._metricsCollector = mockMetrics;

      // Create snapshot with different version to trigger line 393
      const snapshot = await testManager.createSnapshot(
        mockAllItems,
        10,
        mockStrategy,
        mockAccessCounts,
        mockLastAccessed
      );

      // Modify version to trigger compatibility warning
      const modifiedSnapshot = {
        ...snapshot,
        version: '2.0.0' // Different version to trigger line 393
      };

      // Validate snapshot to trigger version compatibility check
      const validation = await testManager.validateSnapshot(modifiedSnapshot);

      // Verify warning was generated (line 393 executed)
      expect(validation.warnings).toContain('Snapshot version 2.0.0 may not be fully compatible');

      await (testManager as any).doShutdown();
    });

    // 🎯 TARGET LINE 388: Invalid snapshot structure validation
    it('should detect invalid snapshot structure', async () => {
      const testManager = new BufferPersistenceManager();
      await (testManager as any).doInitialize();

      // Setup timing infrastructure
      const mockTimer = createMockResilientTimer();
      const mockMetrics = createMockMetricsCollector();
      (testManager as any)._resilientTimer = mockTimer;
      (testManager as any)._metricsCollector = mockMetrics;

      // Create snapshot with missing timestamp to trigger line 388
      const invalidSnapshot = {
        // Missing timestamp to trigger line 388
        version: '1.0.0',
        items: [], // Include items to avoid checksum calculation error
        maxSize: 10,
        strategy: mockStrategy,
        checksum: 'test-checksum'
      } as any;

      // Validate invalid snapshot to trigger structure validation
      const validation = await testManager.validateSnapshot(invalidSnapshot);

      // Verify error was detected (line 388 executed)
      expect(validation.valid).toBe(false);
      expect(validation.errors).toContain('Invalid snapshot structure - missing required fields');

      await (testManager as any).doShutdown();
    });

    // 🎯 TARGET LINES 426-428: Error handling in snapshot validation
    it('should handle validation errors and cleanup properly', async () => {
      const testManager = new BufferPersistenceManager();
      await (testManager as any).doInitialize();

      // Setup timing infrastructure
      const mockTimer = createMockResilientTimer();
      const mockMetrics = createMockMetricsCollector();
      (testManager as any)._resilientTimer = mockTimer;
      (testManager as any)._metricsCollector = mockMetrics;

      // ✅ SPY, DON'T REPLACE: Monitor logError and mock _calculateChecksum to throw error
      const logErrorSpy = jest.spyOn(testManager, 'logError');
      const calculateChecksumSpy = jest.spyOn(testManager as any, '_calculateChecksum')
        .mockImplementation(() => {
          throw new Error('Checksum calculation failed');
        });

      // Create a valid snapshot
      const validSnapshot = {
        timestamp: new Date(),
        version: '1.0.0',
        maxSize: 10,
        items: [],
        strategy: mockStrategy,
        checksum: 'valid-checksum'
      } as IBufferSnapshot<any>;

      // Validate snapshot to trigger checksum error and cleanup (lines 426-428)
      await expect(testManager.validateSnapshot(validSnapshot))
        .rejects.toThrow('Checksum calculation failed');

      // Verify error was logged (line 427 executed)
      expect(logErrorSpy).toHaveBeenCalledWith(
        'Snapshot validation failed',
        expect.any(Error)
      );

      logErrorSpy.mockRestore();
      calculateChecksumSpy.mockRestore();
      await (testManager as any).doShutdown();
    });

    // 🎯 BRANCH COVERAGE: Test all conditional branches in enablePersistence
    it('should cover all branches in enablePersistence method', async () => {
      const testManager = new BufferPersistenceManager();
      await (testManager as any).doInitialize();

      // ✅ SPY, DON'T REPLACE: Monitor createSafeInterval calls
      const createSafeIntervalSpy = jest.spyOn(testManager as any, 'createSafeInterval');

      // Test Branch 1: enabled=true AND snapshotInterval>0 (should call createSafeInterval)
      testManager.enablePersistence({
        enabled: true,
        snapshotInterval: 5000,
        maxSnapshots: 3,
        storageProvider: 'memory'
      });
      expect(createSafeIntervalSpy).toHaveBeenCalledTimes(1);

      // Reset spy
      createSafeIntervalSpy.mockClear();

      // Test Branch 2: enabled=false (should NOT call createSafeInterval)
      testManager.enablePersistence({
        enabled: false,
        snapshotInterval: 5000,
        maxSnapshots: 3,
        storageProvider: 'memory'
      });
      expect(createSafeIntervalSpy).not.toHaveBeenCalled();

      // Test Branch 3: enabled=true BUT snapshotInterval=0 (should NOT call createSafeInterval)
      testManager.enablePersistence({
        enabled: true,
        snapshotInterval: 0,
        maxSnapshots: 3,
        storageProvider: 'memory'
      });
      expect(createSafeIntervalSpy).not.toHaveBeenCalled();

      createSafeIntervalSpy.mockRestore();
      await (testManager as any).doShutdown();
    });

    // 🎯 BRANCH COVERAGE: Test conditional branches in snapshot creation
    it('should cover conditional branches in snapshot creation', async () => {
      const testManager = new BufferPersistenceManager();
      await (testManager as any).doInitialize();

      // Setup timing infrastructure
      const mockTimer = createMockResilientTimer();
      const mockMetrics = createMockMetricsCollector();
      (testManager as any)._resilientTimer = mockTimer;
      (testManager as any)._metricsCollector = mockMetrics;

      // Test Branch 1: With persistence config AND maxSnapshots limit exceeded
      testManager.enablePersistence({
        enabled: true,
        snapshotInterval: 60000,
        maxSnapshots: 2, // Low limit to trigger trimming
        storageProvider: 'memory'
      });

      // Create snapshots to exceed limit and trigger trimming branch
      await testManager.createSnapshot(mockAllItems, 10, mockStrategy, mockAccessCounts, mockLastAccessed);
      await testManager.createSnapshot(mockAllItems, 10, mockStrategy, mockAccessCounts, mockLastAccessed);
      await testManager.createSnapshot(mockAllItems, 10, mockStrategy, mockAccessCounts, mockLastAccessed);

      // Verify trimming occurred (line 312 branch executed)
      const snapshots = testManager.getStoredSnapshots();
      expect(snapshots).toHaveLength(2); // Should be trimmed to maxSnapshots

      // Test Branch 2: Without persistence config (no trimming)
      const testManager2 = new BufferPersistenceManager(); // No config
      await (testManager2 as any).doInitialize();
      (testManager2 as any)._resilientTimer = mockTimer;
      (testManager2 as any)._metricsCollector = mockMetrics;

      await testManager2.createSnapshot(mockAllItems, 10, mockStrategy, mockAccessCounts, mockLastAccessed);
      await testManager2.createSnapshot(mockAllItems, 10, mockStrategy, mockAccessCounts, mockLastAccessed);
      await testManager2.createSnapshot(mockAllItems, 10, mockStrategy, mockAccessCounts, mockLastAccessed);

      // Verify no trimming occurred (branch not executed)
      const snapshots2 = testManager2.getStoredSnapshots();
      expect(snapshots2).toHaveLength(3); // All snapshots preserved

      await (testManager as any).doShutdown();
      await (testManager2 as any).doShutdown();
    });

    // 🎯 BRANCH COVERAGE: Test custom provider conditional branch
    it('should cover custom provider conditional branch', async () => {
      // Test Branch 1: WITH custom provider
      const mockCustomProvider = {
        saveSnapshot: jest.fn().mockResolvedValue(undefined),
        loadSnapshot: jest.fn().mockResolvedValue(null)
      };

      const testManager1 = new BufferPersistenceManager({
        enabled: true,
        snapshotInterval: 60000,
        maxSnapshots: 3,
        storageProvider: 'memory',
        customProvider: mockCustomProvider // Has custom provider
      });
      await (testManager1 as any).doInitialize();

      const mockTimer = createMockResilientTimer();
      const mockMetrics = createMockMetricsCollector();
      (testManager1 as any)._resilientTimer = mockTimer;
      (testManager1 as any)._metricsCollector = mockMetrics;

      // Create snapshot to trigger custom provider branch (line 317)
      await testManager1.createSnapshot(mockAllItems, 10, mockStrategy, mockAccessCounts, mockLastAccessed);
      expect(mockCustomProvider.saveSnapshot).toHaveBeenCalledTimes(1);

      // Test Branch 2: WITHOUT custom provider
      const testManager2 = new BufferPersistenceManager({
        enabled: true,
        snapshotInterval: 60000,
        maxSnapshots: 3,
        storageProvider: 'memory'
        // No customProvider
      });
      await (testManager2 as any).doInitialize();
      (testManager2 as any)._resilientTimer = mockTimer;
      (testManager2 as any)._metricsCollector = mockMetrics;

      // Create snapshot - should NOT trigger custom provider branch
      await testManager2.createSnapshot(mockAllItems, 10, mockStrategy, mockAccessCounts, mockLastAccessed);
      // No custom provider call should occur

      await (testManager1 as any).doShutdown();
      await (testManager2 as any).doShutdown();
    });

    // 🎯 BRANCH COVERAGE: Test shutdown conditional branches
    it('should cover shutdown conditional branches', async () => {
      // Test Branch 1: WITH persistence enabled
      const testManager1 = new BufferPersistenceManager({
        enabled: true, // Persistence enabled
        snapshotInterval: 60000,
        maxSnapshots: 3,
        storageProvider: 'memory'
      });
      await (testManager1 as any).doInitialize();

      const logInfoSpy1 = jest.spyOn(testManager1, 'logInfo');

      // Shutdown with persistence enabled (line 201-208 branch)
      await (testManager1 as any).doShutdown();

      // Verify persistence-enabled shutdown logic was executed
      expect(logInfoSpy1).toHaveBeenCalledWith(
        'Final snapshot creation would be handled by orchestrator during shutdown'
      );

      // Test Branch 2: WITHOUT persistence enabled
      const testManager2 = new BufferPersistenceManager({
        enabled: false, // Persistence disabled
        snapshotInterval: 60000,
        maxSnapshots: 3,
        storageProvider: 'memory'
      });
      await (testManager2 as any).doInitialize();

      const logInfoSpy2 = jest.spyOn(testManager2, 'logInfo');

      // Shutdown without persistence enabled (branch not executed)
      await (testManager2 as any).doShutdown();

      // Verify persistence-specific shutdown logic was NOT executed
      expect(logInfoSpy2).not.toHaveBeenCalledWith(
        'Final snapshot creation would be handled by orchestrator during shutdown'
      );

      logInfoSpy1.mockRestore();
      logInfoSpy2.mockRestore();
    });
  });

  // ============================================================================
  // FINAL COVERAGE TARGETS - LINES 226 AND 404
  // ============================================================================

  describe('Final Coverage Targets', () => {
    // 🎯 TARGET LINE 226: Automatic snapshot creation interval callback execution
    it('should execute automatic snapshot interval callback (Line 226)', async () => {
      const testManager = new BufferPersistenceManager();
      await (testManager as any).doInitialize();

      // ✅ SPY, DON'T REPLACE: Monitor createSafeInterval to capture the callback
      const createSafeIntervalSpy = jest.spyOn(testManager as any, 'createSafeInterval');
      const createAutomaticSnapshotSpy = jest.spyOn(testManager as any, '_createAutomaticSnapshot');

      // Enable persistence to register the interval callback
      testManager.enablePersistence({
        enabled: true,
        snapshotInterval: 5000,
        maxSnapshots: 3,
        storageProvider: 'memory'
      });

      // ✅ NATURAL EXECUTION: Extract and execute the callback to trigger line 226
      expect(createSafeIntervalSpy).toHaveBeenCalledWith(
        expect.any(Function),
        5000,
        'buffer-persistence'
      );

      // Get the callback function that was registered (this is line 226: () => this._createAutomaticSnapshot())
      const intervalCallback = createSafeIntervalSpy.mock.calls[0][0] as () => Promise<void>;

      // Execute the callback to trigger line 226
      await intervalCallback();

      // Verify that _createAutomaticSnapshot was called via the callback (line 226 executed)
      expect(createAutomaticSnapshotSpy).toHaveBeenCalledTimes(1);

      createSafeIntervalSpy.mockRestore();
      createAutomaticSnapshotSpy.mockRestore();
      await (testManager as any).doShutdown();
    });

    // 🎯 TARGET LINE 404: Item count validation error in snapshot validation
    it('should validate item count vs maxSize and generate error (Line 404)', async () => {
      const testManager = new BufferPersistenceManager();
      await (testManager as any).doInitialize();

      // Setup timing infrastructure
      const mockTimer = createMockResilientTimer();
      const mockMetrics = createMockMetricsCollector();
      (testManager as any)._resilientTimer = mockTimer;
      (testManager as any)._metricsCollector = mockMetrics;

      // Create snapshot with MORE items than maxSize to trigger line 404
      const oversizedSnapshot: IBufferSnapshot<any> = {
        timestamp: new Date(),
        version: '1.0.0',
        maxSize: 2, // Set maxSize to 2
        items: [ // But provide 3 items (exceeds maxSize)
          {
            key: 'item1',
            value: 'value1',
            metadata: {
              insertedAt: new Date(),
              lastAccessed: new Date(),
              accessCount: 1,
              size: 10
            }
          },
          {
            key: 'item2',
            value: 'value2',
            metadata: {
              insertedAt: new Date(),
              lastAccessed: new Date(),
              accessCount: 1,
              size: 10
            }
          },
          {
            key: 'item3',
            value: 'value3',
            metadata: {
              insertedAt: new Date(),
              lastAccessed: new Date(),
              accessCount: 1,
              size: 10
            }
          }
        ],
        strategy: mockStrategy,
        checksum: 'test-checksum'
      };

      // Validate snapshot to trigger line 404 (item count validation)
      const validation = await testManager.validateSnapshot(oversizedSnapshot);

      // Verify line 404 was executed (error about item count vs maxSize)
      expect(validation.valid).toBe(false);
      expect(validation.errors).toContain('Snapshot contains 3 items but maxSize is 2');

      // Also verify that metrics recording (line 408) was executed
      expect(mockMetrics.recordTiming).toHaveBeenCalledWith('snapshotValidation', expect.any(Object));

      await (testManager as any).doShutdown();
    });

    // 🎯 ADDITIONAL TARGET: Ensure metrics recording line 408 is covered in success case
    it('should record timing metrics for successful validation (Line 408)', async () => {
      const testManager = new BufferPersistenceManager();
      await (testManager as any).doInitialize();

      // Setup timing infrastructure
      const mockTimer = createMockResilientTimer();
      const mockMetrics = createMockMetricsCollector();
      (testManager as any)._resilientTimer = mockTimer;
      (testManager as any)._metricsCollector = mockMetrics;

      // ✅ NATURAL EXECUTION: Use the existing successful validation test pattern
      // Create snapshot using the same method that works in other tests
      const snapshot = await testManager.createSnapshot(
        mockAllItems,
        10,
        mockStrategy,
        mockAccessCounts,
        mockLastAccessed
      );

      // Clear previous metrics calls from createSnapshot
      mockMetrics.recordTiming.mockClear();

      // Validate the snapshot to trigger successful validation and metrics recording (line 408)
      const validation = await testManager.validateSnapshot(snapshot);

      // Verify validation succeeded
      expect(validation.valid).toBe(true);
      expect(validation.errors).toHaveLength(0);

      // Verify metrics recording (line 408) was executed
      expect(mockMetrics.recordTiming).toHaveBeenCalledWith('snapshotValidation', expect.any(Object));

      await (testManager as any).doShutdown();
    });
  });
});