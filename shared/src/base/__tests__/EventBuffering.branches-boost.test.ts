/**
 * EventBuffering – Branches Boost (Quick Win +4.29% Branches)
 * Scenarios: overflow strategies (drop/flush/expand), auto-flush threshold, periodic flush
 */

import { EventBuffering } from '../event-handler-registry/modules/EventBuffering';

async function makeReady(cfg?: any) {
  const svc = new EventBuffering({ bufferSize: 5, maxFlushSize: 2, autoFlushThreshold: 0.6, overflowStrategy: 'flush', ...cfg });
  await svc.initialize();
  return svc;
}

async function fillBuffer(svc: any, n: number) {
  for (let i = 0; i < n; i++) {
    await svc.bufferEvent('evt', { i }, {} as any);
  }
}

describe('EventBuffering – Branches Boost', () => {
  it('auto-flush triggers at threshold and calls auto-flush callback', async () => {
    const svc = await makeReady({ bufferSize: 5, autoFlushThreshold: 0.6, maxFlushSize: 100 }); // threshold=3, flush all
    const emitted: Array<any> = [];
    svc.setAutoFlushCallback(async (type, data, options) => { emitted.push({ type, data, options }); });

    // Add 2 (below threshold), then 1 more to cross threshold
    await svc.bufferEvent('evt', { a: 1 }, {} as any);
    await svc.bufferEvent('evt', { a: 2 }, {} as any);
    const res = await svc.bufferEvent('evt', { a: 3 }, {} as any);

    expect(res.flushed).toBe(true); // auto-flush executed
    expect(emitted.length).toBeGreaterThan(0); // callback invoked
    expect(svc.getBufferSize()).toBe(0); // flushed all due to maxFlushSize

    await svc.shutdown();
  });

  it('overflow strategy: flush path executes flushEvents and reduces size', async () => {
    const svc = await makeReady({ bufferSize: 3, maxFlushSize: 2, overflowStrategy: 'flush', autoFlushThreshold: 2 }); // disable auto-flush

    // Pre-fill to capacity
    await fillBuffer(svc, 3);
    const before = svc.getBufferSize();

    const res = await svc.triggerBufferOverflowTest();
    const after = svc.getBufferSize();

    expect(res.executed).toBe(true);
    expect(before).toBe(3);
    expect(after).toBeLessThanOrEqual(3);

    await svc.shutdown();
  });

  it('overflow strategy: drop path logs and keeps operating without flush', async () => {
    const svc = await makeReady({ bufferSize: 3, overflowStrategy: 'drop' });
    await fillBuffer(svc, 3);
    const before = svc.getBufferSize();
    const res = await svc.triggerBufferOverflowTest();

    expect(res.executed).toBe(true);
    expect(svc.getBufferSize()).toBe(before); // drop only, no additional flush

    await svc.shutdown();
  });

  it('overflow strategy: expand path warns and does not change buffer size', async () => {
    const svc = await makeReady({ bufferSize: 3, overflowStrategy: 'expand' });
    await fillBuffer(svc, 3);
    const before = svc.getBufferSize();
    const res = await svc.triggerBufferOverflowTest();

    expect(res.executed).toBe(true);
    expect(svc.getBufferSize()).toBe(before);

    await svc.shutdown();
  });

  it('periodic flush executes when buffer not empty and skips when empty', async () => {
    const svc = await makeReady({ bufferSize: 4, maxFlushSize: 2 });

    // Not empty path
    await fillBuffer(svc, 3);
    const beforeNotEmpty = svc.getBufferSize();
    const p1 = await svc.executePeriodicFlushTest();
    const afterNotEmpty = svc.getBufferSize();
    expect(p1.executed).toBe(true);
    expect(p1.bufferWasEmpty).toBe(false);
    expect(afterNotEmpty).toBeLessThan(beforeNotEmpty);

    // Empty path
    await svc._eventBuffer.initialize(); // ensure operational; then flush all completely
    while (!svc.isBufferEmpty()) {
      await svc.flushEvents(10);
    }
    const p2 = await svc.executePeriodicFlushTest();
    expect(p2.bufferWasEmpty).toBe(true);

    await svc.shutdown();
  });
});

