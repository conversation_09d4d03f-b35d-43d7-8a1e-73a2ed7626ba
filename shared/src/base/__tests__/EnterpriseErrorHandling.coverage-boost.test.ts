/**
 * EnterpriseErrorHandling – Coverage Boost (Quick Win: Branches +8.19)
 * Targets uncovered areas: CircuitBreaker HALF_OPEN path (around 270),
 * executeWithRetry mid-loop breaker open (608-611),
 * ensure-classification guard (708), map-clear path (799),
 * exportMetrics overallHealth 'critical' (883)
 */

import { EnterpriseErrorHandler, CircuitBreaker, CircuitBreakerState } from '../utils/EnterpriseErrorHandling';

// Helper that guarantees a rejected operation
const failingOp = async () => { throw new Error('network timeout'); };

describe('EnterpriseErrorHandling – Coverage Boost', () => {
  beforeEach(() => {
    jest.useFakeTimers();
    EnterpriseErrorHandler.clearAllState();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('CircuitBreaker transitions to HALF_OPEN and canExecute returns based on recovery attempts (covers ~270)', () => {
    // Create circuit breaker with threshold=1 (open after one failure) and timeout=0 (immediate recovery)
    const cb = EnterpriseErrorHandler.getCircuitBreaker('op-half-open', {
      circuitBreakerThreshold: 1,
      circuitBreakerTimeoutMs: 0,
      circuitBreakerRecoveryAttempts: 2
    });

    // Initially CLOSED → canExecute true
    expect(cb.canExecute()).toBe(true);

    // Record failure → should OPEN
    cb.recordFailure();
    // OPEN state with timeout passed → canExecute triggers _shouldAttemptRecovery → HALF_OPEN
    const allowed = cb.canExecute();
    expect(allowed).toBe(true); // successCount < recoveryAttempts

    // Record success to increment successCount but remain HALF_OPEN while below recoveryAttempts
    cb.recordSuccess();
    expect(cb.canExecute()).toBe(true);
  });

  it('executeWithRetry opens breaker mid-loop and hits canExecute=false path on retry (608-611)', async () => {
    const opId = 'mid-loop-open';

    const resultPromise = EnterpriseErrorHandler.executeWithRetry(failingOp, opId, {
      maxRetries: 2,
      baseDelayMs: 1,
      maxDelayMs: 5,
      exponentialBackoff: false,
      jitterEnabled: false,
      circuitBreakerThreshold: 1 // first failure opens the breaker
    });

    // Let the retry delay proceed
    await jest.runOnlyPendingTimersAsync();

    const res = await resultPromise;
    expect(res.success).toBe(false);
    expect(res.circuitBreakerTriggered).toBe(true);
    expect(res.attempts).toBeGreaterThanOrEqual(1);
  });

  it('ensure errorClassification guard executes when maxRetries is negative (708)', async () => {
    // Negative maxRetries → loop will not execute; guard must classify error before return
    const res = await EnterpriseErrorHandler.executeWithRetry(() => Promise.reject('bad'), 'neg-retries', {
      maxRetries: -1,
      baseDelayMs: 0,
      exponentialBackoff: false,
      jitterEnabled: false
    });

    expect(res.success).toBe(false);
    expect(res.errorClassification).toBeDefined();
  });

  it('resetOperationMetrics clear-all branch (799) and metrics retrieval', async () => {
    // Create some metrics by performing an operation
    const res = await EnterpriseErrorHandler.executeWithRetry(failingOp, 'metrics-op', { maxRetries: 0 });
    expect(res.success).toBe(false);

    // Ensure metrics exist then clear all
    const before = EnterpriseErrorHandler.getOperationMetrics() as Map<string, any>;
    expect(before.size).toBeGreaterThan(0);

    EnterpriseErrorHandler.resetOperationMetrics();
    const after = EnterpriseErrorHandler.getOperationMetrics() as Map<string, any>;
    expect(after.size).toBe(0);
  });

  it('exportMetricsForMonitoring sets overallHealth to critical when successRate < 0.5 (883)', async () => {
    // Produce failures to keep success rate at 0
    await EnterpriseErrorHandler.executeWithRetry(failingOp, 'crit-op-1', { maxRetries: 0 });
    await EnterpriseErrorHandler.executeWithRetry(failingOp, 'crit-op-2', { maxRetries: 0 });

    const exported = EnterpriseErrorHandler.exportMetricsForMonitoring();
    expect(exported.systemSummary.overallHealth === 'critical' || exported.systemSummary.overallHealth === 'degraded').toBe(true);
    // For deterministic critical: ensure success rate 0
    expect(exported.systemSummary.overallHealth).toBe('critical');
  });
});

