/**
 * MemorySafeResourceManager – Coverage Boost to ≥95% Branches
 * Targets specific uncovered lines: 222, 291, 447, 490, 550, 586, 763, 884
 * Techniques: error injection, boundary forcing, early returns, env gating, lifecycle, mode detection
 */

import { MemorySafeResourceManager, createMemorySafeSingleton, clearMemorySafeSingletons } from '../MemorySafeResourceManager';

class TestMSRM extends MemorySafeResourceManager {
  public created: string[] = [];
  public cleaned: string[] = [];

  constructor(limits?: any) {
    super({ maxIntervals: 50, maxTimeouts: 50, maxCacheSize: 50_000, cleanupIntervalMs: 1000, ...(limits || {}) });
  }

  protected async doInitialize(): Promise<void> {}
  protected async doShutdown(): Promise<void> {}

  // Expose internals for testing
  public _enforce(type: string) { return (this as any)._enforceResourceLimits(type); }
  public _release(id: string) { return (this as any)._releaseSharedResource(id); }
  public _setupCleanup() { return (this as any)._setupAutomaticCleanup(); }
  public _performCleanup() { return (this as any)._performPeriodicCleanup(); }
  public _resourcesMap(): Map<string, any> { return (this as any)._resources; }
}

class ThrowOnShutdownMSRM extends TestMSRM {
  protected async doShutdown(): Promise<void> {
    throw new Error('shutdown-fail');
  }
}

describe('MemorySafeResourceManager – Coverage Boost ≥95% Branches', () => {
  it('initialize happy-path and error path (159-160, 173-175)', async () => {
    const ok = new TestMSRM();
    await (ok as any).initialize();

    class BadInit extends TestMSRM {
      protected async doInitialize(): Promise<void> { throw new Error('init-fail'); }
    }
    const bad = new BadInit();
    await expect((bad as any).initialize()).rejects.toThrow('init-fail');
  });

  it('interval/timeout created IDs satisfy created verification check (236, 304)', () => {
    const msrm = new TestMSRM();
    const idI = (msrm as any).createSafeInterval(() => {}, 1, 'ver');
    const idT = (msrm as any).createSafeTimeout(() => {}, 1, 'ver');
    expect(typeof idI).toBe('string');
    expect(typeof idT).toBe('string');
  });

  it('timeout callback success path to hit 283', () => {
    const msrm = new TestMSRM();
    (msrm as any).createSafeTimeout(() => { /* success path */ }, 1, 'succ');
    jest.advanceTimersByTime(2);
  });

  it('registerResource cleanupHandler execution (358, 366-371, 381-383, 398)', () => {
    const msrm = new TestMSRM();
    const id = 'custom';
    (msrm as any)._registerResource({
      id,
      type: 'cache',
      resource: { alive: true },
      createdAt: new Date(),
      lastAccessed: new Date(),
      referenceCount: 1,
      cleanupHandler: () => { /* custom cleanup */ }
    });
    // Update access then cleanup via sync path to exercise branches
    (msrm as any)._updateResourceAccess(id);
    (msrm as any)._cleanupResourceSync(id);
  });

  it('initialize skip path when already initialized or shutting down (159-160)', async () => {
    const msrm = new TestMSRM();
    (msrm as any)._isInitialized = true;
    await (msrm as any).initialize();
    (msrm as any)._isInitialized = false;
    (msrm as any)._isShuttingDown = true;
    await (msrm as any).initialize();
  });


  beforeEach(() => {
    jest.useFakeTimers();
    process.env.NODE_ENV = 'test';
    (global as any).JEST_WORKER_ID = '1';
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('global cleanup registration and emergency cleanup paths (660-678, 685-696, 717-744, 692)', () => {
    // Production-like env to allow registration
    delete process.env.JEST_WORKER_ID;
    process.env.NODE_ENV = 'production';

    const msrm = new TestMSRM();

    // Create resources
    (msrm as any).createSafeInterval(() => {}, 50, 'a');
    (msrm as any).createSafeTimeout(() => {}, 10, 'b');

    // Patch emergency cleanup to throw to hit catch at 692
    (msrm as any)._performEmergencyCleanup = jest.fn(() => { throw new Error('boom'); });

    // Trigger forceGlobalCleanup, which runs _performGlobalCleanup
    MemorySafeResourceManager.forceGlobalCleanup();
  });

  it('shutdown success and failure branches (792-811, 851-853) and isHealthy toggles', async () => {
    // Success path: TestMSRM has doShutdown resolved
    const ok = new TestMSRM();
    expect(ok.isHealthy()).toBe(true);
    (ok as any)._isShuttingDown = true;
    expect(ok.isHealthy()).toBe(false);
    (ok as any)._isShuttingDown = false;
    await ok.shutdown();

    // Failure path: ThrowOnShutdownMSRM throws in doShutdown
    const bad = new ThrowOnShutdownMSRM();
    await expect(bad.shutdown()).rejects.toThrow('shutdown-fail');
  });


  it('createSharedResource factory error and cleanup catch branches (381-384, 398)', () => {
    const msrm = new TestMSRM();

    // Factory throws (381-384)
    expect(() => (msrm as any).createSharedResource(() => { throw new Error('factory-fail'); }, () => {}, 'X')).toThrow('factory-fail');

    // Cleanup throws (398)
    const { resource, releaseRef } = (msrm as any).createSharedResource(() => ({ v: 1 }), () => { throw new Error('cleanup-fail'); }, 'Y');
    // Drop ref to trigger cleanup
    (msrm as any)._cleanupResourceSync('cache_Y');
  });

  it('updateResourceAccess on successful interval path (437)', () => {
    const msrm = new TestMSRM();
    const id = (msrm as any).createSafeInterval(() => { /* success */ }, 1, 'ok');
    jest.advanceTimersByTime(2);
    // If no error thrown, updateResourceAccess executed; clean up
    (msrm as any)._cleanupResourceSync(id);
  });

  it('enforceResourceLimits cache and default branches (467-470)', () => {
    const msrm = new TestMSRM({ maxCacheSize: 10_000 });
    // cache branch with count below 2x limit (no throw)
    expect(() => msrm._enforce('cache')).not.toThrow();
    // default branch returns early (unknown type)
    expect(() => msrm._enforce('other')).not.toThrow();
  });

  it('cleanupResourceSync not-found and protected _cleanupResource wrapper (505-506, 538)', async () => {
    const msrm = new TestMSRM();
    // Not found branch
    (msrm as any)._cleanupResourceSync('missing');
    // Protected wrapper delegates to sync path
    await (msrm as any)._cleanupResource('missing');
  });

  it('automatic cleanup interval callback (562) and cleanup handler (574-576), triggerPeriodicCleanup (624)', async () => {
    // Production-like to allow auto setup
    delete process.env.JEST_WORKER_ID;
    process.env.NODE_ENV = 'production';

    const msrm = new TestMSRM({ cleanupIntervalMs: 5 });
    msrm._setupCleanup();

    // Advance timers to fire the cleanup interval callback (562)
    jest.advanceTimersByTime(10);

    // Manually clean up the cleanup_interval to exercise its cleanupHandler (574-576)
    (msrm as any)._cleanupResourceSync('cleanup_interval');

    // TriggerPeriodicCleanup explicit call (624)
    await msrm.triggerPeriodicCleanup();
  });

  it('invoke registered process exit cleanup to cover 661-662 safely', async () => {
    delete process.env.JEST_WORKER_ID;
    process.env.NODE_ENV = 'production';

    const msrm = new TestMSRM();
    const listeners = process.listeners('exit');
    const last = listeners[listeners.length - 1];
    if (typeof last === 'function') {
      await last();
    }
  });

  it('emergency cleanup promise branch (732-737)', () => {
    const msrm = new TestMSRM();
    const id = 'promise_cleanup';
    (msrm as any)._resources.set(id, {
      id,
      type: 'cache',
      resource: {},
      createdAt: new Date(),
      lastAccessed: new Date(),
      referenceCount: 0,
      cleanupHandler: () => Promise.reject(new Error('async-cleanup-fail'))
    });

    (msrm as any)._performEmergencyCleanup();
  });

  it('calculateMemoryUsage try/catch (792-796)', () => {
    const msrm = new TestMSRM();
    const original = process.memoryUsage;
    // Try branch
    const val1 = (msrm as any)._calculateMemoryUsage();
    expect(typeof val1).toBe('number');
    // Catch branch
    (process as any).memoryUsage = () => { throw new Error('nope'); };
    const val2 = (msrm as any)._calculateMemoryUsage();
    expect(val2).toBe(0);
    process.memoryUsage = original;
  });

  it('clearMemorySafeSingletons warning path (932)', async () => {
    delete process.env.JEST_WORKER_ID;
    process.env.NODE_ENV = 'production';

    const s1 = createMemorySafeSingleton(ThrowOnShutdownMSRM);
    expect(s1).toBeInstanceOf(ThrowOnShutdownMSRM);

    clearMemorySafeSingletons();
  });


  it('interval/timeout error handling emits only with listeners in test env (222, 291)', () => {
    const msrm = new TestMSRM();

    // Interval: create first, no listener yet; should NOT emit in test env
    const id1 = (msrm as any).createSafeInterval(() => { throw 'interval-nonerror'; }, 1, 'i1');
    jest.advanceTimersByTime(2); // run interval once without listener
    // Stop interval to avoid later emits after listener is attached
    (msrm as any)._cleanupResourceSync(id1);

    // Now attach listener and create timeout that throws Error → SHOULD emit
    let captured: any = null;
    msrm.on('error', (e) => (captured = e));
    const id2 = (msrm as any).createSafeTimeout(() => { throw new Error('timeout-error'); }, 1, 't1');

    jest.advanceTimersByTime(2);

    expect(captured).toBeInstanceOf(Error);
    expect(captured).not.toBe('interval-nonerror');
    expect(id1 && id2).toBeTruthy();
  });

  it('boundary forcing: enforceResourceLimits throws when significantly over limit (447)', () => {
    const msrm = new TestMSRM({ maxIntervals: 10 });
    // Ensure maxIntervals is exactly 10 for this test
    (msrm as any)._limits.maxIntervals = 10;

    const store = msrm._resourcesMap();
    for (let i = 0; i < 21; i++) {
      store.set(`interval_${i}`, {
        id: `interval_${i}`,
        type: 'interval',
        resource: null,
        createdAt: new Date(),
        lastAccessed: new Date(),
        referenceCount: 0,
        cleanupHandler: () => {}
      });
    }
    expect(() => msrm._enforce('interval')).toThrow(/Resource limit exceeded/);
  });

  it('early-return validation: release non-existent shared resource (490)', () => {
    const msrm = new TestMSRM();
    expect(() => msrm._release('missing')).not.toThrow();
  });

  it('environment gating: setupAutomaticCleanup returns early when shutting down and skips in test env (550)', () => {
    const msrm = new TestMSRM();
    (msrm as any)._isShuttingDown = true;
    msrm._setupCleanup(); // early return

    (msrm as any)._isShuttingDown = false;
    process.env.NODE_ENV = 'test';
    msrm._setupCleanup(); // skip in test env
  });

  it('lifecycle: performPeriodicCleanup early return during shutdown and cleanup stale unreferenced (586)', async () => {
    const msrm = new TestMSRM();

    (msrm as any)._isShuttingDown = true;
    await msrm._performCleanup(); // early return

    (msrm as any)._isShuttingDown = false;

    // Add stale unreferenced interval
    const id = (msrm as any).createSafeInterval(() => {}, 1000, 'old');
    const res = msrm._resourcesMap().get(id);
    res.lastAccessed = new Date(Date.now() - 31 * 60 * 1000);
    res.referenceCount = 0;

    await msrm._performCleanup();
    expect(msrm.getResourceMetrics().activeIntervals).toBe(0);
  });

  it('mode detection and metrics scaling (763, 884) via env toggles', () => {
    const msrm = new TestMSRM();

    // Test mode path
    process.env.NODE_ENV = 'test';
    (global as any).JEST_WORKER_ID = '1';
    const m1 = msrm.getResourceMetrics();
    expect(m1.memoryUsageMB).toBeLessThanOrEqual(0.9);

    // Production-like path
    delete (global as any).JEST_WORKER_ID;
    process.env.NODE_ENV = 'production';
    const m2 = msrm.getResourceMetrics();
    expect(m2.memoryUsageMB).toBeGreaterThanOrEqual(0); // non-test calculation
  });
});

