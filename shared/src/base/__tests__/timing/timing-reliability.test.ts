/**
 * @file Timing Reliability Validation Tests
 * @task-id T-TSK-03.SUB-03.3.TRV-01
 * @location shared/src/base/__tests__/timing/timing-reliability.test.ts
 * @standards Anti-Simplification | MEM-SAFE-002 | Essential Coding Criteria | GOV-AI-TEST-001
 *
 * Scope
 * - Validate reliability characteristics of ResilientTimer and ResilientMetricsCollector
 * - Scenarios: normal vs stress, fallback reliability, consistency across cycles, error recovery,
 *   reliability metrics validation, concurrent degradation behavior, lifecycle reliability
 */

import { ResilientTimer, IResilientTimingResult } from '../../utils/ResilientTiming';
import { ResilientMetricsCollector } from '../../utils/ResilientMetrics';

jest.setTimeout(30000);

describe('Timing Reliability Validation', () => {
  it('should produce reliable timing under normal conditions', () => {
    const timer = new ResilientTimer({ enableFallbacks: true, unreliableThreshold: 3 });
    const ctx = timer.start();
    const t = ctx.end();
    expect(t.duration).toBeGreaterThan(0);
    expect(typeof t.timestamp).toBe('number');
    expect([true, false]).toContain(t.reliable);
    // Under normal CI conditions, we expect reliable OR reasonable fallback
    expect(t.reliable || t.fallbackUsed).toBe(true);
  });

  it('should degrade gracefully when performance.now fails - fallback path reliable', () => {
    const timer = new ResilientTimer({ enableFallbacks: true, unreliableThreshold: 0, estimateBaseline: 7 });
    const ctx = timer.start();
    const t = ctx.end();
    // With threshold 0, any unreliable reading triggers fallback
    expect(t.fallbackUsed || t.reliable).toBe(true);
    expect(t.duration).toBeGreaterThan(0);
  });

  it('should maintain timing consistency across multiple measurement cycles', () => {
    const timer = new ResilientTimer({ enableFallbacks: true });
    const durations: number[] = [];
    for (let i = 0; i < 50; i++) {
      const m = timer.measureSync(() => ({ i })).timing;
      durations.push(m.duration);
    }
    // Consistency: most durations should be in a small band given test environment granularity
    const max = Math.max(...durations);
    const min = Math.min(...durations);
    expect(max - min).toBeLessThanOrEqual(10);
  });

  it('should recover and continue providing reasonable measurements after errors', () => {
    const metrics = new ResilientMetricsCollector({ enableFallbacks: true });

    // Simulate a series of measurements including potential unreliable ones
    for (let i = 0; i < 100; i++) {
      const measurement = metrics.timer.measureSync(() => i).timing;
      metrics.recordTiming('exec', measurement);
    }

    // Ensure we can still create a reliable snapshot or a snapshot with warnings
    const snap = metrics.createSnapshot();
    expect(typeof snap.timestamp).toBe('number');
    expect(snap.reliable === true || snap.reliable === false).toBe(true);
    // If unreliable metrics exist, warnings array should reflect that
    if (!snap.reliable) {
      expect(Array.isArray(snap.warnings)).toBe(true);
    }

    // Compatible metrics object should never throw and provide reasonable values
    const compat = metrics.createCompatibleMetrics();
    expect(typeof compat).toBe('object');
  });

  it('should retain acceptable reliability under concurrent access', async () => {
    const metrics = new ResilientMetricsCollector({ enableFallbacks: true });
    const concurrency = 20;
    const iterations = 20;

    await Promise.all(Array.from({ length: concurrency }).map(async () => {
      for (let i = 0; i < iterations; i++) {
        const m = await metrics.timer.measure(async () => {});
        metrics.recordTiming('concurrent', m.timing);
      }
    }));

    // After concurrent activity, metric should still be present and either reliable or have safe estimate
    const v = metrics.getMetric('concurrent');
    expect(v === null || typeof v.value === 'number').toBe(true);
  });

  it('timing context lifecycle should be reliable (create, measure, cleanup)', () => {
    const timer = new ResilientTimer({ enableFallbacks: true });
    // Create many contexts and end them; ensure no exceptions and bounded variance
    const results: IResilientTimingResult[] = [];
    for (let i = 0; i < 200; i++) {
      const ctx = timer.start();
      results.push(ctx.end());
    }
    expect(results.length).toBe(200);
    const durations = results.map(r => r.duration);
    const spread = Math.max(...durations) - Math.min(...durations);
    expect(spread).toBeLessThanOrEqual(20);
  });
});

