/**
 * @file Timing Context Validation Tests
 * @task-id T-TSK-03.SUB-03.4.TCV-01
 * @location shared/src/base/__tests__/timing/timing-context-validation.test.ts
 * @standards Anti-Simplification | MEM-SAFE-002 | Essential Coding Criteria | GOV-AI-TEST-001
 *
 * Scope
 * - Validate ResilientTimingContext lifecycle and behavior
 * - Focus: creation/init, accuracy, cleanup, concurrent isolation, error handling, state consistency, memory efficiency
 */

import { ResilientTimer, ResilientTimingContext, IResilientTimingResult } from '../../utils/ResilientTiming';

jest.setTimeout(30000);

describe('Timing Context Validation (ResilientTimingContext)', () => {
  it('should initialize with proper method and timestamps in test env', () => {
    const timer = new ResilientTimer({ enableFallbacks: true });
    const ctx = timer.start();
    const result = ctx.end();
    expect(['performance', 'date', 'process', 'estimate']).toContain(result.method);
    expect(result.duration).toBeGreaterThan(0);
    expect(typeof result.timestamp).toBe('number');
  });

  it('should provide accurate measurement and non-zero duration with Jest compatibility', () => {
    const timer = new ResilientTimer({ enableFallbacks: true });
    const r = timer.measureSync(() => 42).timing;
    expect(r.duration).toBeGreaterThan(0); // Jest minimum enforced
    expect(r.reliable || r.fallbackUsed).toBe(true);
  });

  it('contexts should be isolated under concurrency and not interfere', async () => {
    const timer = new ResilientTimer({ enableFallbacks: true });
    const concurrency = 30;
    const iterations = 20;

    const batches = await Promise.all(Array.from({ length: concurrency }).map(async () => {
      const res: IResilientTimingResult[] = [];
      for (let i = 0; i < iterations; i++) {
        const ctx = timer.start();
        res.push(ctx.end());
      }
      return res;
    }));

    // Verify no negative or NaN durations and reasonable spread across all contexts
    for (const batch of batches) {
      for (const r of batch) {
        expect(r.duration).toBeGreaterThan(0);
        expect(Number.isFinite(r.duration)).toBe(true);
      }
    }
  });

  it('should handle errors during measured operation and still end context correctly', () => {
    const timer = new ResilientTimer({ enableFallbacks: true });

    expect(() => timer.measureSync(() => { throw new Error('boom'); })).toThrow('boom');
    // Ensure subsequent contexts still work
    const ctx = timer.start();
    const r = ctx.end();
    expect(r.duration).toBeGreaterThan(0);
  });

  it('context state should remain consistent across lifecycle', () => {
    const timer = new ResilientTimer({ enableFallbacks: true });
    const results: IResilientTimingResult[] = [];

    for (let i = 0; i < 100; i++) {
      const ctx = timer.start();
      results.push(ctx.end());
    }

    const methods = new Set(results.map(r => r.method));
    expect(methods.size).toBeGreaterThanOrEqual(1); // method stays coherent per env

    const durations = results.map(r => r.duration);
    const spread = Math.max(...durations) - Math.min(...durations);
    expect(spread).toBeLessThanOrEqual(20);
  });

  it('context creation/destruction should be memory efficient', () => {
    if (!(global as any).gc) {
      // If GC not available, still run logic but skip strict assertion
      const timer = new ResilientTimer({ enableFallbacks: true });
      for (let i = 0; i < 2000; i++) { const ctx = timer.start(); ctx.end(); }
      expect(true).toBe(true);
      return;
    }

    const before = process.memoryUsage().heapUsed;
    const timer = new ResilientTimer({ enableFallbacks: true });

    for (let i = 0; i < 5000; i++) {
      const ctx = timer.start();
      ctx.end();
    }

    (global as any).gc();
    const after = process.memoryUsage().heapUsed;
    const diffMB = (after - before) / (1024 * 1024);
    expect(diffMB).toBeLessThanOrEqual(20);
  });
});

