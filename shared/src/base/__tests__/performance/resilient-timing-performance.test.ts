/**
 * @file Resilient Timing Performance Tests
 * @task-id T-TSK-03.SUB-02.3.RTP-01
 * @location shared/src/base/__tests__/performance/resilient-timing-performance.test.ts
 * @standards Anti-Simplification | MEM-SAFE-002 | Essential Coding Criteria | GOV-AI-TEST-001
 *
 * Scope
 * - Validate ResilientTimer + ResilientMetricsCollector performance and reliability
 * - Benchmarks: context create/end, metrics recording/aggregation, concurrent access
 * - Validate memory overhead bounds and fallback performance
 */

import { ResilientTimer } from '../../utils/ResilientTiming';
import { ResilientMetricsCollector } from '../../utils/ResilientMetrics';

jest.setTimeout(30000);

type BenchResult = { avgMs: number; p95Ms: number; maxMs: number };

function percentile(values: number[], p: number): number {
  const sorted = [...values].sort((a, b) => a - b);
  const idx = Math.ceil((p / 100) * sorted.length) - 1;
  return sorted[Math.max(0, Math.min(sorted.length - 1, idx))];
}

async function benchAsync(fn: () => Promise<void>, iterations = 500): Promise<BenchResult> {
  const durations: number[] = [];
  for (let i = 0; i < iterations; i++) {
    const start = Date.now();
    await fn();
    const end = Date.now();
    durations.push(end - start);
  }
  return { avgMs: durations.reduce((a, b) => a + b, 0) / durations.length, p95Ms: percentile(durations, 95), maxMs: Math.max(...durations) };
}

function benchSync(fn: () => void, iterations = 500): BenchResult {
  const durations: number[] = [];
  for (let i = 0; i < iterations; i++) {
    const start = Date.now();
    fn();
    const end = Date.now();
    durations.push(end - start);
  }
  return { avgMs: durations.reduce((a, b) => a + b, 0) / durations.length, p95Ms: percentile(durations, 95), maxMs: Math.max(...durations) };
}

describe('Performance - Resilient Timing Infrastructure', () => {
  it('ResilientTimer: timing context start/end overhead should be negligible', () => {
    const timer = new ResilientTimer({ enableFallbacks: true, unreliableThreshold: 3, maxExpectedDuration: 30000, estimateBaseline: 5 });

    const result = benchSync(() => {
      const ctx = timer.start();
      const timing = ctx.end();
      expect(timing.duration).toBeGreaterThanOrEqual(0);
      expect(typeof timing.timestamp).toBe('number');
      expect(typeof timing.reliable).toBe('boolean');
    }, 1000);

    // Enterprise requirement: sub-millisecond average in CI (Date.now granularity limits apply)
    expect(result.avgMs).toBeLessThanOrEqual(1);
    expect(result.p95Ms).toBeLessThanOrEqual(2);
  });

  it('ResilientMetricsCollector: recordTiming aggregation must be efficient', () => {
    const metrics = new ResilientMetricsCollector({ maxHistory: 5000, enableAggregation: true });
    const timer = metrics.timer;

    const result = benchSync(() => {
      const ctx = timer.start();
      const timing = ctx.end();
      metrics.recordTiming('op', timing);
    }, 2000);

    expect(result.avgMs).toBeLessThanOrEqual(1);
    expect(result.p95Ms).toBeLessThanOrEqual(2);
  });

  it('Concurrent timing contexts: reliability and throughput under load', async () => {
    const metrics = new ResilientMetricsCollector({ maxHistory: 10000, enableAggregation: true });

    const concurrency = 50;
    const iterations = 50;
    const tasks: Array<Promise<void>> = [];

    for (let i = 0; i < concurrency; i++) {
      tasks.push((async () => {
        for (let j = 0; j < iterations; j++) {
          const measurement = await metrics.timer.measure(async () => {
            // simulate minimal async workload
          });
          metrics.recordTiming('concurrent-op', measurement.timing);
        }
      })());
    }

    const { avgMs, p95Ms, maxMs } = await benchAsync(async () => { await Promise.all(tasks); }, 1);

    // Throughput target: finish batch quickly, and reliability captured
    expect(avgMs).toBeLessThanOrEqual(500); // one batch end-to-end in CI
    expect(p95Ms).toBeLessThanOrEqual(800);
    expect(maxMs).toBeLessThanOrEqual(1200);
  });

  it('Memory overhead of timing infrastructure should remain bounded', () => {
    const before = process.memoryUsage().heapUsed;
    const metrics = new ResilientMetricsCollector({ maxHistory: 20000, enableAggregation: true });

    for (let i = 0; i < 5000; i++) {
      const m = metrics.timer.measureSync(() => {});
      metrics.recordTiming('mem', m.timing);
    }

    if (global.gc) global.gc();
    const after = process.memoryUsage().heapUsed;
    const diffMB = (after - before) / (1024 * 1024);

    expect(diffMB).toBeLessThanOrEqual(20); // 20MB bound
  });

  it('Fallback mechanism performance when high failure conditions are simulated', () => {
    const timer = new ResilientTimer({ enableFallbacks: true, unreliableThreshold: 0, estimateBaseline: 5 });

    const result = benchSync(() => {
      const ctx = timer.start();
      // Simulate environment where performance.now might fail frequently
      const timing = ctx.end();
      expect(timing.fallbackUsed || timing.reliable).toBe(true);
    }, 1000);

    // Even in fallback conditions, overhead should remain acceptable
    expect(result.avgMs).toBeLessThanOrEqual(2);
    expect(result.p95Ms).toBeLessThanOrEqual(4);
  });
});

