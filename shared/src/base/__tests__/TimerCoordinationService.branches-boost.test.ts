/**
 * TimerCoordinationService – <PERSON><PERSON> (target uncovered lines 229, 237, 466, 471, 524)
 * Business value: Validates hard limits, duplicate direct timer handling, and audit scheduling
 */

import { TimerCoordinationService } from '../TimerCoordinationService';

describe('TimerCoordinationService – Branches Boost', () => {
  afterEach(() => {
    TimerCoordinationService.resetInstance();
    jest.useRealTimers();
  });

  it('throws when service timer limit is exceeded (line 466)', () => {
    const svc = TimerCoordinationService.getInstance({ maxTimersPerService: 0, maxGlobalTimers: 100, minIntervalMs: 1 });
    expect(() => svc.createCoordinatedInterval(() => {}, 1, 'svc-limit', 't1')).toThrow(/Timer limit exceeded for service/);
  });

  it('throws when global timer limit is exceeded (line 471)', () => {
    const svc = TimerCoordinationService.getInstance({ maxTimersPerService: 10, maxGlobalTimers: 0, minIntervalMs: 1 });
    expect(() => svc.createCoordinatedInterval(() => {}, 1, 'svc-global', 't1')).toThrow(/Global timer limit exceeded/);
  });

  it('clears existing direct interval on forced duplicate (line 229) and logs on callback error (line 237)', () => {
    const oldEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'test';

    const svc = TimerCoordinationService.getInstance({ maxTimersPerService: 5, maxGlobalTimers: 100, minIntervalMs: 1 });

    jest.useFakeTimers();

    const clearSpy = jest.spyOn(global, 'clearInterval');

    // First creation registers in _directIntervals
    const id = svc.createCoordinatedInterval(() => { throw new Error('boom'); }, 1, 'svc', 'dup');

    // Force duplicate creation bypassing early duplicate check to hit clearInterval(existing)
    (svc as any).createCoordinatedInterval(() => { throw new Error('boom2'); }, 1, 'svc', 'dup', { force: true });

    // Advance timers to execute callbacks and hit catch/log path
    jest.advanceTimersByTime(2);

    expect(clearSpy).toHaveBeenCalled();

    // Restore env
    process.env.NODE_ENV = oldEnv;
  });

  it('creates audit interval in non-test env and executes its callback (line 524)', () => {
    const oldEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'production';

    const svc = TimerCoordinationService.getInstance({ maxTimersPerService: 5, maxGlobalTimers: 100, minIntervalMs: 1, timerAuditIntervalMs: 1000 });

    const spy = jest.spyOn(svc as any, 'createSafeInterval').mockImplementation((cb: any, ms: number, name: string) => {
      // Immediately invoke audit callback to simulate scheduling
      if (name === 'timer-audit') {
        try { cb(); } catch {} // ignore errors in audit path for this test
      }
      return 'id';
    });

    svc.createCoordinatedInterval(() => {}, 1, 'prod', 'audit');

    // Ensure audit creation was attempted
    const auditCall = spy.mock.calls.find((c) => c[2] === 'timer-audit');
    expect(auditCall).toBeDefined();

    process.env.NODE_ENV = oldEnv;
  });
});

