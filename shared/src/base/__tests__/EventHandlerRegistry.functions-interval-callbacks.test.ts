/**
 * EventHandlerRegistry – Functions ≥95% via executing scheduled callbacks
 * Purpose: Execute arrow functions defined at lines 206 and 213 by advancing fake timers
 * Business value: Validates periodic orphan detection and metrics updates execute as scheduled
 */

import { EventHandlerRegistry } from '../EventHandlerRegistry';

describe('EventHandlerRegistry – Scheduled callbacks execution', () => {
  beforeEach(() => {
    // Jest setup already uses fake timers globally, but ensure consistency
    jest.useFakeTimers();
    process.env.NODE_ENV = 'test';
    (global as any).JEST_WORKER_ID = '1';
  });

  afterEach(() => {
    jest.useFakeTimers();
  });

  it('runs orphan detection and metrics update intervals when timers advance', async () => {
    const reg = new EventHandlerRegistry({
      maxHandlersPerClient: 5,
      maxGlobalHandlers: 100,
      orphanDetectionIntervalMs: 5, // very small to execute quickly
      handlerTimeoutMs: 1 // consider any stale handler as orphaned immediately
    } as any);

    // Register a handler and backdate lastUsed so orphan detection removes it
    const id = reg.registerHandler('client-x', 'evt-x', jest.fn());
    const h = reg.getHandler(id)!;
    h.lastUsed = new Date(Date.now() - 1000);

    await reg.initialize();

    // Advance time to trigger both orphan detection (5ms) and metrics update (30s)
    jest.advanceTimersByTime(30010);

    // After timers, handler should be cleaned up and metrics updated
    const metrics = reg.getMetrics();
    expect(metrics.orphanedHandlers).toBeGreaterThanOrEqual(1);
    expect(metrics.cleanupOperations).toBeGreaterThanOrEqual(1);
    // The handler should be unregistered
    expect(reg.getHandler(id)).toBeUndefined();

    await reg.shutdown();
  });
});

