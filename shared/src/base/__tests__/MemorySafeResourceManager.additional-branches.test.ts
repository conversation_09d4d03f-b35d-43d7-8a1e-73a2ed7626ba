/**
 * MemorySafeResourceManager – Additional Branch Targets
 */

import { MemorySafeResourceManager } from '../MemorySafeResourceManager';

describe('MemorySafeResourceManager – Additional Branches', () => {
  class TestMgr extends MemorySafeResourceManager {
    protected async doInitialize(): Promise<void> {}
    protected async doShutdown(): Promise<void> {}
  }

  afterEach(() => {
    jest.restoreAllMocks();
    jest.useRealTimers();
  });

  it('does not emit error in test env without error listeners (no-emit branch at 222-229)', async () => {
    const mgr = new TestMgr({} as any);
    await mgr.initialize();

    const emitSpy = jest.spyOn(mgr as any, 'emit');

    jest.useFakeTimers();
    // No error listeners attached — should NOT emit in test env
    (mgr as any).createSafeInterval(() => { throw new Error('boom'); }, 5, 'no-emit');
    jest.advanceTimersByTime(5);

    // Ensure emit was not called with 'error'
    expect(emitSpy).not.toHaveBeenCalledWith('error', expect.anything());

    await mgr.shutdown();
  });

  it('returns early in _performPeriodicCleanup when shutting down (586)', async () => {
    const mgr = new TestMgr({} as any);
    await mgr.initialize();

    // Force shutting down state to hit early-return branch
    (mgr as any)._isShuttingDown = true;

    const emitSpy = jest.spyOn(mgr as any, 'emit');
    await (mgr as any)._performPeriodicCleanup();

    // No periodicCleanup emission when shutting down
    expect(emitSpy).not.toHaveBeenCalledWith('periodicCleanup', expect.anything());

    await mgr.shutdown();
  });
});

