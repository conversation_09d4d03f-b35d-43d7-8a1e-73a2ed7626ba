/**
 * LoggingMixin – Additional Coverage Tests
 */

import { withLogging, SimpleLogger } from '../LoggingMixin';

describe('LoggingMixin – Additional Coverage', () => {
  const realEnv = { ...process.env };
  let errorSpy: jest.SpyInstance;
  let warnSpy: jest.SpyInstance;
  let infoSpy: jest.SpyInstance;
  let debugSpy: jest.SpyInstance;

  beforeEach(() => {
    errorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    warnSpy = jest.spyOn(console, 'warn').mockImplementation(() => {});
    infoSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
    debugSpy = jest.spyOn(console, 'debug').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
    process.env = { ...realEnv };
  });

  it('formats errors consistently in withLogging mixin for various error types', () => {
    class Base {}
    const Logged = withLogging(Base as any, 'TestService');
    const inst = new (Logged as any)();

    inst.logError('ctx', new Error('boom'));
    expect(errorSpy).toHaveBeenCalledWith(
      expect.stringContaining('[ERROR] TestService: ctx - boom'),
      ''
    );

    inst.logError('ctx2', 'str-error');
    expect(errorSpy).toHaveBeenCalledWith(
      expect.stringContaining('[ERROR] TestService: ctx2 - str-error'),
      ''
    );

    inst.logError('ctx3', null);
    expect(errorSpy).toHaveBeenCalledWith(
      expect.stringContaining('[ERROR] TestService: ctx3 - null'),
      ''
    );

    inst.logError('ctx4', undefined);
    expect(errorSpy).toHaveBeenCalledWith(
      expect.stringContaining('[ERROR] TestService: ctx4 - undefined'),
      ''
    );

    inst.logError('ctx5', { code: 123 });
    expect(errorSpy).toHaveBeenCalledWith(
      expect.stringContaining('[ERROR] TestService: ctx5 - [object Object]'),
      ''
    );
  });

  it('gates debug logging in withLogging by environment flags', () => {
    class Base {}
    const Logged = withLogging(Base as any, 'DebugSvc');
    const inst = new (Logged as any)();

    // No debug by default
    delete process.env.NODE_ENV;
    delete process.env.DEBUG;
    inst.logDebug('no-debug');
    expect(debugSpy).not.toHaveBeenCalled();

    // Enabled by NODE_ENV=development
    process.env.NODE_ENV = 'development';
    inst.logDebug('dev-debug');
    expect(debugSpy).toHaveBeenCalledWith(
      expect.stringContaining('[DEBUG] DebugSvc: dev-debug'),
      ''
    );

    jest.clearAllMocks();
    // Enabled by DEBUG=true
    delete process.env.NODE_ENV;
    process.env.DEBUG = 'true';
    inst.logDebug('flag-debug');
    expect(debugSpy).toHaveBeenCalledWith(
      expect.stringContaining('[DEBUG] DebugSvc: flag-debug'),
      ''
    );
  });

  it('SimpleLogger formats errors consistently and gates debug by env', () => {
    const logger = new SimpleLogger('SimpleSvc');

    logger.logError('ctx', new Error('oops'));
    expect(errorSpy).toHaveBeenCalledWith(
      expect.stringContaining('[ERROR] SimpleSvc: ctx - oops'),
      ''
    );

    logger.logError('ctx2', 'oops2');
    expect(errorSpy).toHaveBeenCalledWith(
      expect.stringContaining('[ERROR] SimpleSvc: ctx2 - oops2'),
      ''
    );

    // Debug off by default
    delete process.env.NODE_ENV;
    delete process.env.DEBUG;
    logger.logDebug('no-debug');
    expect(debugSpy).not.toHaveBeenCalled();

    // Debug enabled in development
    process.env.NODE_ENV = 'development';
    logger.logDebug('dev-debug');
    expect(debugSpy).toHaveBeenCalledWith(
      expect.stringContaining('[DEBUG] SimpleSvc: dev-debug'),
      ''
    );
  });
});

