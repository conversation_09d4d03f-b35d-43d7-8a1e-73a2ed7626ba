/**
 * EventBuffering – More Branches (flush loop + error logging)
 */

import { EventBuffering } from '../event-handler-registry/modules/EventBuffering';

describe('EventBuffering – More Branches', () => {
  it('complete flush loop iterates until empty and logs safety threshold without triggering', async () => {
    const svc = new EventBuffering({ bufferSize: 6, maxFlushSize: 2, overflowStrategy: 'flush', autoFlushThreshold: 0.95 });
    await svc.initialize();

    // Fill and then run complete flush test (public wrapper that mimics loop)
    for (let i = 0; i < 5; i++) {
      await svc.bufferEvent('evt', { i }, {} as any);
    }

    const res = await svc.executeCompleteFlushTest();

    expect(res.executed).toBe(true);
    expect(res.initialBufferSize).toBeGreaterThan(0);
    expect(res.totalEventsFlushed).toBeGreaterThan(0);

    await svc.shutdown();
  });

  it('processBufferedEvents continues on per-event errors but records processed count', async () => {
    const svc = new EventBuffering({ bufferSize: 5, maxFlushSize: 5, autoFlushThreshold: 0.95 });
    await svc.initialize();

    // Put a few events
    for (let i = 0; i < 4; i++) {
      await svc.bufferEvent('evt', { i }, {} as any);
    }

    const count = await svc.processBufferedEvents(async (type, data) => {
      if ((data as any).i % 2 === 0) throw new Error('emit-fail'); // half fail
    });

    expect(count).toBeGreaterThan(0);
    expect(svc.getBufferSize()).toBe(0);

    await svc.shutdown();
  });
});

