/**
 * EventHandlerRegistry – Functions ≥95% Boost (Part 2)
 * Covers remaining uncovered function paths:
 * - registerHandler invalid params (278)
 * - registerHandler client limit exceeded (288-289)
 * - unregisterHandler non-existent early return (335-336)
 * - getHandlersForEvent empty list (368-369 -> return [])
 * - logError wrapper (450)
 * - Global singleton helpers: getEventHandlerRegistry / resetEventHandlerRegistry (536-539, 544-545)
 */

import { EventHandlerRegistry, getEventHandlerRegistry, resetEventHandlerRegistry } from '../EventHandlerRegistry';

describe('EventHandlerRegistry – Functions Boost Part 2', () => {
  it('registerHandler: throws on invalid params (line 278)', () => {
    const reg = new EventHandlerRegistry({} as any);
    expect(() => reg.registerHandler('', 'evt', jest.fn())).toThrow('Invalid handler registration parameters');
    expect(() => reg.registerHandler('client', '', jest.fn())).toThrow('Invalid handler registration parameters');
    expect(() => reg.registerHandler('client', 'evt', undefined as unknown as any)).toThrow('Invalid handler registration parameters');
  });

  it('registerHandler: enforces client limit (lines 288-289)', () => {
    const reg = new EventHandlerRegistry({ maxHandlersPerClient: 1 } as any);
    reg.registerHandler('c1', 'evt', jest.fn());
    expect(() => reg.registerHandler('c1', 'evt2', jest.fn())).toThrow(/exceeded maximum handler limit/);
  });

  it('unregisterHandler: returns false for non-existent handler (335-336)', () => {
    const reg = new EventHandlerRegistry({} as any);
    const result = reg.unregisterHandler('missing-id');
    expect(result).toBe(false);
  });

  it('getHandlersForEvent: returns empty array for unknown event (368-369)', () => {
    const reg = new EventHandlerRegistry({} as any);
    const handlers = reg.getHandlersForEvent('unknown');
    expect(Array.isArray(handlers)).toBe(true);
    expect(handlers.length).toBe(0);
  });

  it('logError wrapper delegates (450)', () => {
    const reg = new EventHandlerRegistry({} as any);
    // Just ensure no throw and method exists
    reg.logError('something went wrong', new Error('boom'));
  });

  it('global instance helpers: getEventHandlerRegistry / resetEventHandlerRegistry (536-539, 544-545)', async () => {
    // Ensure we get a singleton instance
    const g1 = getEventHandlerRegistry();
    const g2 = getEventHandlerRegistry();
    expect(g1).toBe(g2);

    // Reset and ensure a new instance is created
    await resetEventHandlerRegistry();
    const g3 = getEventHandlerRegistry();
    expect(g3).not.toBe(g1);
  });
});

