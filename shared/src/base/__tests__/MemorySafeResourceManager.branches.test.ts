/**
 * MemorySafeResourceManager – Branch Coverage Surgical Boost
 */

import { MemorySafeResourceManager } from '../MemorySafeResourceManager';

describe('MemorySafeResourceManager – Branch Coverage', () => {
  class TestMgr extends MemorySafeResourceManager {
    protected async doInitialize(): Promise<void> {}
    protected async doShutdown(): Promise<void> {}
  }

  const saveEnv = () => ({ NODE_ENV: process.env.NODE_ENV, JEST_WORKER_ID: process.env.JEST_WORKER_ID });
  const restoreEnv = (env: { NODE_ENV?: string; JEST_WORKER_ID?: string }) => {
    if (typeof env.NODE_ENV === 'undefined') delete (process.env as any).NODE_ENV; else process.env.NODE_ENV = env.NODE_ENV;
    if (typeof env.JEST_WORKER_ID === 'undefined') delete (process.env as any).JEST_WORKER_ID; else process.env.JEST_WORKER_ID = env.JEST_WORKER_ID as any;
  };

  afterEach(() => jest.restoreAllMocks());

  it('skips automatic cleanup in test env (lines 552-559)', async () => {
    const env = saveEnv();
    try {
      process.env.NODE_ENV = 'test';
      process.env.JEST_WORKER_ID = '1';
      const mgr = new TestMgr({} as any);
      mgr.on('error', () => {}); // prevent unhandled error events
      const spy = jest.spyOn(mgr as any, 'createSafeInterval');
      await mgr.initialize();
      expect(spy).not.toHaveBeenCalled();
      await mgr.shutdown();
    } finally {
      restoreEnv(env);
    }
  });

  it('enforces resource limits when significantly over limit (lines 475-482)', async () => {
    const mgr = new TestMgr({} as any);
    mgr.on('error', () => {});
    await mgr.initialize();

    // Seed many interval resources to exceed 2x limit
    const limit = (mgr as any)._limits.maxIntervals;
    for (let i = 0; i < limit * 2; i++) {
      (mgr as any)._registerResource({ id: `i${i}`, type: 'interval', resource: 1, createdAt: new Date(), lastAccessed: new Date(), referenceCount: 1 });
    }

    expect(() => (mgr as any)._enforceResourceLimits('interval')).toThrow(/Resource limit exceeded/);
    await mgr.shutdown();
  });

  it('cleanup sync emits error and removes resource when cleanup handler throws (lines 520-531)', async () => {
    const mgr = new TestMgr({} as any);
    mgr.on('error', () => {});
    await mgr.initialize();

    // Register a resource with cleanup handler that throws non-Error
    (mgr as any)._registerResource({ id: 'x', type: 'cache', resource: {}, createdAt: new Date(), lastAccessed: new Date(), referenceCount: 0, cleanupHandler: () => { throw 'fail'; } });

    const cleaned: any[] = [];
    mgr.on('resourceCleaned', (r) => cleaned.push(r));
    const errSpy = jest.spyOn(mgr as any, 'emit');

    (mgr as any)._cleanupResourceSync('x');

    expect(cleaned.length).toBe(0); // when cleanup handler throws, only error is emitted
    expect(errSpy).toHaveBeenCalledWith('error', 'fail');
    // Ensure resource removed from map
    expect((mgr as any)._resources.has('x')).toBe(false);
    await mgr.shutdown();
  });

  it('periodic cleanup skips cleanup_interval and emits periodicCleanup (lines 602-618)', async () => {
    const env = saveEnv();
    try {
      process.env.NODE_ENV = 'production';
      delete (process.env as any).JEST_WORKER_ID;

      const mgr = new TestMgr({} as any);
      mgr.on('error', () => {});
      await mgr.initialize();

      // Seed old resource and the cleanup interval registration
      ;(mgr as any)._registerResource({ id: 'cleanup_interval', type: 'interval', resource: 1, createdAt: new Date(), lastAccessed: new Date(), referenceCount: 1 });
      ;(mgr as any)._registerResource({ id: 'old', type: 'cache', resource: {}, createdAt: new Date(Date.now() - 40 * 60 * 1000), lastAccessed: new Date(Date.now() - 40 * 60 * 1000), referenceCount: 0 });

      const cleaned: string[] = [];
      mgr.on('resourceCleaned', (r: any) => cleaned.push(r.id));
      const emitSpy = jest.spyOn(mgr as any, 'emit');

      await (mgr as any)._performPeriodicCleanup();

      expect(emitSpy).toHaveBeenCalledWith('periodicCleanup', { cleanedResources: expect.any(Number) });
      await mgr.shutdown();
    } finally {
      restoreEnv(env);
    }
  });
});

