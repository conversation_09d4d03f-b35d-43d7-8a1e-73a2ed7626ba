/**
 * ResilientTiming – More Coverage (target remaining branches around getCurrentTime fallbacks)
 */

import { ResilientTimer, measureAsync } from '../utils/ResilientTiming';

describe('ResilientTiming – More Coverage', () => {
  const realPerf = (global as any).performance;
  const realHr = (process as any).hrtime;

  afterEach(() => {
    (global as any).performance = realPerf;
    (process as any).hrtime = realHr;
  });

  it('falls back from performance.now to process.hrtime, then to Date.now', async () => {
    // First, force performance.now failure
    (global as any).performance = { now: () => { throw new Error('perf-fail'); } } as any;
    // Provide hrtime
    (process as any).hrtime = () => [1, 500000000] as any; // 1.5s -> 1500ms

    const t1 = new ResilientTimer({ maxExpectedDuration: 10000 });
    const r1 = t1.measureSync(() => 'ok');
    expect(['process', 'date']).toContain(r1.timing.method);

    // Next, break hrtime as well to force Date
    (process as any).hrtime = undefined;
    const t2 = new ResilientTimer({ maxExpectedDuration: 10000 });
    const r2 = t2.measureSync(() => 'ok');
    expect(['date', 'process']).toContain(r2.timing.method);
  });

  it('validateAndAdjustTiming uses estimate when duration invalid and enableFallbacks=true', () => {
    const t = new ResilientTimer({ maxExpectedDuration: 1000, estimateBaseline: 7, enableFallbacks: true });
    const ctx: any = (t as any).start();
    // Call private method via any cast
    const res = (ctx as any).validateAndAdjustTiming(NaN, 'date', false);
    expect(res.fallbackUsed).toBe(true);
    expect(res.method).toBe('estimate');
    expect(res.duration).toBeGreaterThanOrEqual(1);
  });
});

