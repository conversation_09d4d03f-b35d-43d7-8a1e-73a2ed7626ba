/**
 * EventHandlerRegistry – Function Coverage Boost
 */

import { EventHandlerRegistry } from '../EventHandlerRegistry';

describe('EventHandlerRegistry – Function Coverage', () => {
  it('covers unregisterClientHandlers, getHandlersForEvent timestamp update, and emergency cleanup', async () => {
    const reg = new EventHandlerRegistry({ maxHandlersPerClient: 10, maxGlobalHandlers: 100, handlerTimeoutMs: 10, orphanDetectionIntervalMs: 3600000 } as any);

    const h1 = jest.fn();
    const h2 = jest.fn();
    const id1 = reg.registerHandler('c1', 'e1', h1);
    const id2 = reg.registerHandler('c1', 'e2', h2);

    // getHandlersForEvent updates lastUsed timestamp implicitly
    const before = reg.getHandler(id1)!;
    const handlers = reg.getHandlersForEvent('e1');
    expect(handlers.length).toBeGreaterThanOrEqual(1);
    const after = reg.getHandler(id1)!;
    expect(after.lastUsed.getTime()).toBeGreaterThanOrEqual(before.lastUsed.getTime());

    // unregisterClientHandlers removes all for client
    const removed = reg.unregisterClientHandlers('c1');
    expect(removed).toBe(2);

    // Seed many handlers to trigger emergency cleanup (20% oldest removed)
    for (let i = 0; i < 10; i++) {
      reg.registerHandler('c'+i, 'eX', jest.fn());
    }
    // Force global limit path to call emergency cleanup
    (reg as any)._config.maxGlobalHandlers = 5; // lower limit to trigger
    reg.registerHandler('c-extra', 'eX', jest.fn());

    // Directly call emergency cleanup to ensure function path covered
    (reg as any)._performHandlerEmergencyCleanup();

    // Metrics path coverage
    const metrics = reg.getMetrics();
    expect(metrics.totalHandlers).toBeGreaterThanOrEqual(0);
  });
});

