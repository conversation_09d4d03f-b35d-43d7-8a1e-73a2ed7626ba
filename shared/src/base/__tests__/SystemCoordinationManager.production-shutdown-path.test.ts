/**
 * SystemCoordinationManager – Production Shutdown Timing Path (606–611)
 * Goal: Exercise non-test setTimeout path by toggling NODE_ENV to 'production'
 */

import { SystemCoordinationManager } from '../memory-safety-manager/modules/SystemCoordinationManager';

describe('SystemCoordinationManager – Production shutdown timing', () => {
  const realEnv = { ...process.env };

  afterEach(() => {
    process.env = { ...realEnv };
    jest.useRealTimers();
  });

  it('hits default strategy setTimeout path for non-test environment (lines 606–611)', async () => {
    // Arrange: production-like environment (no JEST_WORKER_ID, NODE_ENV=production)
    const oldWorker = process.env.JEST_WORKER_ID;
    delete process.env.JEST_WORKER_ID;
    process.env.NODE_ENV = 'production';

    jest.useFakeTimers();

    const registry = new Map<string, any>([
      ['svc1', { status: 'active' }],
      ['svc2', { status: 'active' }]
    ]);
    const mgr = new SystemCoordinationManager(registry as any);

    // Act: orchestrate with default strategy to target else path (shutdownTime=100)
    const p = mgr.orchestrateSystemShutdown('default');

    // Advance timers to execute the shutdown setTimeout
    jest.advanceTimersByTime(110);

    const res = await p;

    // Assert: all components processed via production timer path
    expect(res.totalComponents).toBe(2);
    expect(res.shutdownComponents + res.failedComponents).toBe(2);

    // Restore
    if (oldWorker) process.env.JEST_WORKER_ID = oldWorker;
  });
});

