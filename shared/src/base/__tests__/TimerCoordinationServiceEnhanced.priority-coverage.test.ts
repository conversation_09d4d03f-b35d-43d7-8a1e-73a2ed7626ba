import { TimerCoordinationServiceEnhanced } from '../TimerCoordinationServiceEnhanced';
import { POOL_STRATEGIES } from '../timer-coordination/modules/TimerConfiguration';

// Scope: shared/src only

describe('TimerCoordinationServiceEnhanced – Priority Coverage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  function createService(overrides: Partial<any> = {}) {
    const svc = new TimerCoordinationServiceEnhanced({
      maxTimersPerService: 20,
      maxGlobalTimers: 100,
      minIntervalMs: 10,
      timerAuditIntervalMs: 1000,
      pooling: {
        enabled: true,
        defaultPoolSize: 2,
        maxPools: 3,
        poolMonitoringInterval: 1000,
        autoOptimization: true,
      },
      scheduling: {
        cronParsingEnabled: false,
        conditionalTimersEnabled: true,
        prioritySchedulingEnabled: true,
        jitterEnabled: false,
        maxJitterMs: 0,
      },
      coordination: {
        groupingEnabled: true,
        chainExecutionEnabled: true,
        synchronizationEnabled: true,
        maxGroupSize: 5,
        maxChainLength: 5,
      },
      integration: {
        phase1BufferEnabled: true,
        phase2EventEnabled: true,
        bufferSize: 10,
        eventEmissionEnabled: true,
      },
      performance: {
        poolOperationTimeoutMs: 500,
        schedulingTimeoutMs: 500,
        synchronizationTimeoutMs: 500,
        monitoringEnabled: true,
        metricsCollectionInterval: 1000,
      },
      ...overrides,
    } as any);
    return svc as any;
  }

  it('initialize records success timing and shutdown records success timing', async () => {
    const svc: any = createService();
    const record = jest.spyOn((svc as any)._metricsCollector, 'recordTiming');
    await svc.initialize();
    expect(record).toHaveBeenCalledWith(
      'orchestrator_operations',
      expect.objectContaining({ duration: expect.any(Number) })
    );

    await svc.shutdown();
    expect(record).toHaveBeenCalledWith(
      'orchestrator_operations',
      expect.objectContaining({ duration: expect.any(Number) })
    );
  });

  it('initialize error path logs and rethrows when metrics throws once', async () => {
    const svc: any = createService();
    const record = jest.spyOn((svc as any)._metricsCollector, 'recordTiming');
    // Throw only on first success record to enter catch
    (record as any).mockImplementationOnce(() => { throw new Error('metrics-fail'); });
    await expect(svc.initialize()).rejects.toThrow('metrics-fail');
    // Catch should also try to record failed timing
    expect(record).toHaveBeenCalledWith('orchestrator_operations_failed', expect.any(Object));
  });

  it('pool creation and round-robin/least-used pooled timers', async () => {
    const svc: any = createService();
    await svc.initialize();

    // Create a pool with round_robin
    const poolCfgRR = {
      maxPoolSize: 2,
      initialSize: 0,
      poolStrategy: POOL_STRATEGIES.ROUND_ROBIN,
      autoExpansion: false,
      maxExpansionSize: 0,
      idleTimeout: 5000,
      sharedResourcesEnabled: false,
      monitoringEnabled: true,
      onPoolExhaustion: 'queue',
    } as any;

    const poolRR = svc.createTimerPool('p-rr', poolCfgRR);
    expect(poolRR.poolStrategy).toBe(POOL_STRATEGIES.ROUND_ROBIN);

    const id1 = svc.createPooledTimer('p-rr', () => {}, 100, 'svcA', 't1');
    const id2 = svc.createPooledTimer('p-rr', () => {}, 100, 'svcA', 't2');
    expect(typeof id1).toBe('string');
    expect(typeof id2).toBe('string');

    // Create a pool with least_used
    const poolCfgLU = { ...poolCfgRR, poolStrategy: POOL_STRATEGIES.LEAST_USED };
    const poolLU = svc.createTimerPool('p-lu', poolCfgLU);
    expect(poolLU.poolStrategy).toBe(POOL_STRATEGIES.LEAST_USED);

    const id3 = svc.createPooledTimer('p-lu', () => {}, 100, 'svcB', 't3');
    expect(typeof id3).toBe('string');
  });

  it('createCoordinatedTimeout uses base service and removes timer after callback', async () => {
    const svc: any = createService();
    await svc.initialize();

    const removeSpy = jest.spyOn((svc as any)._baseTimerService, 'removeCoordinatedTimer');

    const called: string[] = [];
    const compId = svc.createCoordinatedTimeout(() => { called.push('done'); }, 100, 'svcX', 't9');

    jest.advanceTimersByTime(1200);
    expect(called).toEqual(['done']);
    expect(removeSpy).toHaveBeenCalledWith('svcX:t9');
    expect(typeof compId).toBe('string');
  });

  it('removeCoordinatedTimer handles base service errors and returns false', async () => {
    const svc: any = createService();
    await svc.initialize();

    jest.spyOn((svc as any)._baseTimerService, 'removeCoordinatedTimer').mockImplementation(() => { throw new Error('rm-fail'); });
    const logErr = jest.spyOn(svc, 'logError');

    const ok = svc.removeCoordinatedTimer('svcY:t10');
    expect(ok).toBe(false);
    expect(logErr).toHaveBeenCalledWith('Failed to remove coordinated timer', expect.any(Error), { compositeId: 'svcY:t10' });
  });

  it('delegates scheduling and coordination operations', async () => {
    const svc: any = createService();
    await svc.initialize();

    // Utilities/lazy getters get exercised by delegations
    const opId = svc.generateOperationId();
    const timerId = svc.generateTimerId();
    expect(typeof opId).toBe('string');
    expect(typeof timerId).toBe('string');

    // Delegated scheduling
    const delayedId = svc.scheduleDelayedTimer(() => {}, 200, 'svcD', 'td');
    expect(typeof delayedId).toBe('string');

    // Coordination
    const group = svc.createTimerGroup('g1', ['svcD:td'], 'parallel');
    expect(group.groupId || group.id || 'g1').toBeTruthy();

    // Further coordination coverage
    await expect(svc.synchronizeTimerGroup('g1')).resolves.toBeDefined();
    expect(svc.createTimerChain([{ id: 's1', timerId: 'svcD:td', componentId: 'svcD', operation: 'run' } as any])).toBeDefined();
    expect(svc.createTimerBarrier(['svcD:td'], () => {})).toBeDefined();

    await expect(svc.pauseTimerGroup('g1')).resolves.toBeUndefined();
    await expect(svc.resumeTimerGroup('g1')).resolves.toBeUndefined();
    await expect(svc.destroyTimerGroup('g1')).resolves.toBeDefined();
  });

  it('base service delegation: coordinated interval, clear timers, and cron validation', async () => {
    const svc: any = createService();
    await svc.initialize();

    // create coordinated interval (will use direct interval in test env with min 1000ms)
    const comp = svc.createCoordinatedInterval(() => {}, 50, 'svcC', 'tid-x');
    expect(typeof comp).toBe('string');

    // Clear timers for service
    expect(() => svc.clearServiceTimers('svcC')).not.toThrow();
    expect(() => svc.clearAllTimers()).not.toThrow();

    // Cron expression validation
    expect(typeof svc.validateCronExpression('* * * * *')).toBe('boolean');
  });

  it('pool statistics and utilization via delegation', async () => {
    const svc: any = createService();
    await svc.initialize();

    const poolCfgRR = {
      maxPoolSize: 2, initialSize: 0, poolStrategy: POOL_STRATEGIES.ROUND_ROBIN,
      autoExpansion: false, maxExpansionSize: 0, idleTimeout: 5000,
      sharedResourcesEnabled: false, monitoringEnabled: true, onPoolExhaustion: 'queue',
    } as any;
    svc.createTimerPool('stats-pool', poolCfgRR);
    svc.createPooledTimer('stats-pool', () => {}, 100, 'svcS', 'ts1');

    const stats = svc.getPoolStatistics('stats-pool');
    expect(stats).toBeTruthy();
  });

  it('getters for stats, health details, configuration and integration flags', async () => {
    const svc: any = createService();
    await svc.initialize();

    expect(svc.getTimerStatistics()).toBeDefined();
    expect(svc.getHealthDetails()).toBeDefined();
    expect(svc.getConfiguration()).toBeDefined();
    expect(typeof svc.isPhase1Enabled()).toBe('boolean');
    expect(typeof svc.isPhase2Enabled()).toBe('boolean');
    expect(svc.getIntegrationMetrics()).toBeDefined();
  });
});

