import { BufferConfigurationManager, IEnhancedBufferConfig } from '../atomic-circular-buffer-enhanced/modules/BufferConfigurationManager';

describe('BufferConfigurationManager – Priority Coverage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  function createManager(initial?: Partial<IEnhancedBufferConfig>) {
    const mgr = new BufferConfigurationManager(initial);
    mgr.initializeSync();
    return mgr as any;
  }

  it('initializeSync sets up timing and merges defaults', () => {
    const mgr: any = createManager({ maxSize: 123 });
    const cfg = mgr.getCurrentConfiguration();
    expect(cfg.maxSize).toBe(123);
    expect(mgr._resilientTimer).toBeDefined();
    expect(mgr._metricsCollector).toBeDefined();
  });

  it('doInitialize async path logs info', async () => {
    const mgr = new BufferConfigurationManager();
    const infoSpy = jest.spyOn(mgr as any, 'logInfo');
    await (mgr as any).initialize(); // MemorySafeResourceManager public API
    expect(infoSpy).toHaveBeenCalledWith(
      'BufferConfigurationManager initialized with resilient timing (async)'
    );
  });

  it('shutdown success path logs info (217)', async () => {
    const mgr: any = new BufferConfigurationManager();
    await mgr.initialize();
    await expect(mgr.shutdown()).resolves.toBeUndefined();
  });

  it('validateConfiguration errors and warnings across fields', () => {
    const mgr: any = createManager();

    // invalid maxSize and initialCapacity conflict
    let res = mgr.validateConfiguration({ maxSize: 0, initialCapacity: 10 });
    expect(res.valid).toBe(false);
    expect(res.errors).toEqual(expect.arrayContaining(['maxSize must be greater than 0']));

    // very large maxSize warning
    res = mgr.validateConfiguration({ maxSize: 1000001 });
    expect(res.warnings).toEqual(
      expect.arrayContaining(['maxSize is very large, consider performance implications'])
    );

    // initialCapacity > maxSize
    res = mgr.validateConfiguration({ maxSize: 10, initialCapacity: 11 });
    expect(res.errors).toEqual(
      expect.arrayContaining(['initialCapacity cannot be greater than maxSize'])
    );

    // strategy invalid policy
    res = mgr.validateConfiguration({ strategy: { evictionPolicy: 'nope' } as any });
    expect(res.errors).toEqual(expect.arrayContaining(['Invalid eviction policy: nope']));

    // strategy custom without fn
    res = mgr.validateConfiguration({ strategy: { evictionPolicy: 'custom' } as any });
    expect(res.errors).toEqual(
      expect.arrayContaining(['Custom eviction policy requires customEvictionFn'])
    );

    // compactionThreshold errors and warnings
    res = mgr.validateConfiguration({ strategy: { compactionThreshold: -0.1 } as any });
    expect(res.errors).toEqual(
      expect.arrayContaining(['compactionThreshold must be between 0 and 1'])
    );

    res = mgr.validateConfiguration({ strategy: { compactionThreshold: 0.9 } as any });
    expect(res.warnings).toEqual(
      expect.arrayContaining(['High compaction threshold may impact performance'])
    );

    // performance warnings
    res = mgr.validateConfiguration({ performance: { metricsRetentionMs: 1000 } as any });
    expect(res.warnings).toEqual(
      expect.arrayContaining(['metricsRetentionMs is very low, may impact analytics quality'])
    );

    res = mgr.validateConfiguration({ performance: { maxAccessHistorySize: 20000 } as any });
    expect(res.warnings).toEqual(
      expect.arrayContaining(['maxAccessHistorySize is very high, may impact memory usage'])
    );
  });

  it('updateConfiguration success updates state and logs; failure logs error', () => {
    const mgr: any = createManager();

    const infoSpy = jest.spyOn(mgr, 'logInfo');
    const errSpy = jest.spyOn(mgr, 'logError');

    // success
    const ok = mgr.updateConfiguration({ maxSize: 200 });
    expect(ok.valid).toBe(true);
    expect(infoSpy).toHaveBeenCalledWith(
      'Configuration updated successfully',
      expect.objectContaining({ hasWarnings: false })
    );
    expect(mgr.getCurrentConfiguration().maxSize).toBe(200);

    // failure
    const bad = mgr.updateConfiguration({ maxSize: 0 });
    expect(bad.valid).toBe(false);
    expect(errSpy).toHaveBeenCalledWith(
      'Configuration update failed',
      expect.any(Error)
    );
  });

  it('getCurrentConfiguration/getDefaultConfiguration return clones; resetToDefaults works', () => {
    const mgr: any = createManager({ maxSize: 333 });
    const current = mgr.getCurrentConfiguration();
    const defaults = mgr.getDefaultConfiguration();

    expect(current).not.toBe(mgr._currentConfig);
    expect(defaults).not.toBe(mgr._defaultConfig);

    mgr.resetToDefaults();
    const after = mgr.getCurrentConfiguration();
    expect(after.maxSize).toBe(defaults.maxSize);
  });

  it('merge timing metrics recorded during validation', () => {
    const mgr: any = createManager();
    const timingSpy = jest.spyOn(mgr._metricsCollector, 'recordTiming');

    const res = mgr.validateConfiguration({ maxSize: 999 });
    expect(res.valid).toBe(true);
    expect(timingSpy).toHaveBeenCalledWith(
      'configValidation',
      expect.objectContaining({ duration: expect.any(Number) })
    );
  });

  it('exportConfiguration error branch and importConfiguration invalid JSON', () => {
    const mgr: any = createManager();

    // Force JSON.stringify to throw once
    const originalStringify = JSON.stringify;
    JSON.stringify = (() => { throw new Error('stringify-fail'); }) as any;
    const errSpy = jest.spyOn(mgr, 'logError');
    expect(() => mgr.exportConfiguration()).toThrow('stringify-fail');
    expect(errSpy).toHaveBeenCalledWith('Failed to export configuration', expect.any(Error));
    JSON.stringify = originalStringify;

    // import invalid JSON
    expect(() => mgr.importConfiguration('{bad json')).toThrow('Invalid configuration JSON format');
    expect(errSpy).toHaveBeenCalledWith('Failed to import configuration', expect.anything());
  });

  it('getConfigurationSummary returns concise summary of current config', () => {
    const mgr: any = createManager({
      maxSize: 42,
      strategy: { evictionPolicy: 'fifo' } as any,
      performance: { enableMetrics: false } as any,
      monitoring: { logLevel: 'warn' } as any
    });

    const summary = mgr.getConfigurationSummary();
    expect(summary).toMatchObject({
      maxSize: 42,
      evictionPolicy: 'fifo',
      metricsEnabled: false,
      loggingLevel: 'warn'
    });
  });
});

