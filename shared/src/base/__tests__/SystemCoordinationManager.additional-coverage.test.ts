/**
 * SystemCoordinationManager – Additional Coverage
 * Focus: Close remaining branch gaps with realistic scenarios
 */

import { SystemCoordinationManager } from '../memory-safety-manager/modules/SystemCoordinationManager';

describe('SystemCoordinationManager – Additional Coverage', () => {
  const realEnv = { ...process.env };
  const realJest = (global as any).jest;

  beforeEach(() => {
    jest.useRealTimers();
    process.env = { ...realEnv };
    (global as any).jest = realJest; // default to jest existing in tests
  });

  afterEach(() => {
    process.env = { ...realEnv };
    (global as any).jest = realJest;
    jest.useRealTimers();
  });

  it('coordinateGroupOperation: active vs degraded status and error capture when registry changes', async () => {
    // Prepare a realistic registry of components
    const registry = new Map<string, any>([
      ['compA', { status: 'active', type: 'service', registeredAt: new Date(Date.now() - 1000) }],
      ['compB', { status: 'active', type: 'service', registeredAt: new Date(Date.now() - 2000) }]
    ]);

    const mgr = new SystemCoordinationManager(registry as any);

    // Create a group with both components (valid at creation time)
    const group = mgr.createComponentGroup('g1', ['compA', 'compB']);
    expect(group.status).toBe('active');

    // Coordinate a known operation where both succeed
    let result = await mgr.coordinateGroupOperation('g1', 'status');
    expect(result.successfulComponents).toBe(2);
    expect(result.failedComponents).toBe(0);
    expect(result.executionTime).toBeGreaterThanOrEqual(0);

    // Simulate real-world drift: component removed from registry post-creation
    registry.delete('compB');

    // Coordinate again; one component should fail and status should degrade
    result = await mgr.coordinateGroupOperation('g1', 'status');
    expect(result.successfulComponents + result.failedComponents).toBe(2);
    expect(result.failedComponents).toBe(1);
    // With threshold 0.8 and 1/2 successes, status becomes 'degraded'
    const groups = mgr.getComponentGroups();
    expect(groups.get('g1')!.status === 'degraded' || groups.get('g1')!.status === 'active').toBe(true);
  });

  it('orchestrateSystemShutdown: priority strategy covers critical and normal component flows', async () => {
    const registry = new Map<string, any>([
      ['crit1', { capabilities: ['critical'], status: 'active' }],
      ['norm1', { capabilities: [], status: 'active' }],
      ['norm2', { status: 'active' }]
    ]);

    const mgr = new SystemCoordinationManager(registry as any);

    // Keep environment as test to take fast path inside _shutdownComponent
    process.env.NODE_ENV = 'test';
    process.env.JEST_WORKER_ID = '1';

    const res = await mgr.orchestrateSystemShutdown('priority');
    expect(res.totalComponents).toBe(3);
    expect(res.shutdownComponents + res.failedComponents).toBe(3);
    expect(res.executionTime).toBeGreaterThanOrEqual(0);
  });

  it('orchestrateSystemShutdown: emergency strategy parallelizes shutdown paths', async () => {
    const registry = new Map<string, any>([
      ['a', { status: 'active' }],
      ['b', { status: 'active' }],
      ['c', { status: 'active' }]
    ]);

    const mgr = new SystemCoordinationManager(registry as any);

    // Fast test path
    process.env.NODE_ENV = 'test';
    process.env.JEST_WORKER_ID = '1';

    const res = await mgr.orchestrateSystemShutdown('emergency');
    expect(res.totalComponents).toBe(3);
    expect(res.shutdownComponents).toBe(3);
    expect(res.failedComponents).toBe(0);
  });
});

