/** MemorySafeResourceManager – Surgical precision branch coverage to reach 100% */
import { MemorySafeResourceManager } from '../MemorySafeResourceManager';

class T extends MemorySafeResourceManager {
  protected async doInitialize(): Promise<void> {}
  protected async doShutdown(): Promise<void> {}
  // Expose protected for testing production-vs-test detection
  public isTestMode(): boolean { return (this as any)._isTestMode(); }
}

describe('MemorySafeResourceManager – 100% branch coverage', () => {
  beforeEach(() => {
    jest.useFakeTimers();
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });
  afterEach(async () => {
    jest.useRealTimers();
    jest.restoreAllMocks();
  });

  it('interval: in test env, emits error only when error listeners are attached (covers 222 branch)', async () => {
    process.env.NODE_ENV = 'test';
    delete process.env.JEST_WORKER_ID;

    const m = new T();
    // Case A: No listeners → should NOT emit (test env, no listeners)
    const emitSpyA = jest.spyOn(m as any, 'emit');
    (m as any).createSafeInterval(() => { throw new Error('boom-A'); }, 1, 'int-A');
    jest.advanceTimersByTime(2);
    expect(emitSpyA).not.toHaveBeenCalledWith('error', expect.anything());

    // Case B: With listeners → SHOULD emit (test env, listeners present)
    const m2 = new T();
    const listener = jest.fn();
    m2.on('error', listener);
    (m2 as any).createSafeInterval(() => { throw new Error('boom-B'); }, 1, 'int-B');
    jest.advanceTimersByTime(2);
    expect(listener).toHaveBeenCalled();

    await m.shutdown();
    await m2.shutdown();
  });

  it('interval: in production, emits error even without listeners (covers other side of 222 condition) safely via emit spy', async () => {
    process.env.NODE_ENV = 'production';
    delete process.env.JEST_WORKER_ID;

    const m = new T();
    // Spy on emit to prevent unhandled error event from failing the test
    const emitSpy = jest.spyOn(m as any, 'emit').mockImplementation(() => true as any);
    (m as any).createSafeInterval(() => { throw new Error('boom-prod'); }, 1, 'int-prod');
    jest.advanceTimersByTime(2);
    expect(emitSpy).toHaveBeenCalledWith('error', expect.any(Error));

    await m.shutdown();
  });

  it('timeout: covers both isTestEnvironment true/false at emission check (287-293) with realistic production behavior', async () => {
    // Test env - no listeners → no emit
    process.env.NODE_ENV = 'test';
    delete process.env.JEST_WORKER_ID;

    const m = new T();
    const emitSpyA = jest.spyOn(m as any, 'emit');
    (m as any).createSafeTimeout(() => { throw new Error('boom-T1'); }, 1, 'to-A');
    jest.advanceTimersByTime(2);
    expect(emitSpyA).not.toHaveBeenCalledWith('error', expect.anything());

    // Production - without listeners → emit
    process.env.NODE_ENV = 'production';
    delete process.env.JEST_WORKER_ID;

    const m2 = new T();
    const emitSpyB = jest.spyOn(m2 as any, 'emit').mockImplementation(() => true as any);
    (m2 as any).createSafeTimeout(() => { throw new Error('boom-T2'); }, 1, 'to-B');
    jest.advanceTimersByTime(2);
    expect(emitSpyB).toHaveBeenCalledWith('error', expect.any(Error));

    await m.shutdown();
    await m2.shutdown();
  });

  it('generateResourceId uses unnamed when name omitted (covers 447 branch via name || "unnamed")', async () => {
    const m = new T();
    // Spy registerResource to capture id generation
    const regSpy = jest.spyOn(m as any, '_registerResource');

    // Omit name → should include "unnamed"
    (m as any).createSafeInterval(() => {}, 1);
    jest.advanceTimersByTime(1);
    expect(regSpy).toHaveBeenCalled();
    const callArg = regSpy.mock.calls[0][0];
    expect(callArg.id).toContain('interval_unnamed_');

    // Provide name → should include provided name
    (m as any).createSafeTimeout(() => {}, 1, 'hasName');
    jest.advanceTimersByTime(1);
    const callArg2 = regSpy.mock.calls[1][0];
    expect(callArg2.id).toContain('timeout_hasName_');

    await m.shutdown();
  });

  it('isTestMode covers both true/false outcomes of the first operand (process.env.NODE_ENV === "test") to satisfy branch mapping at 884', async () => {
    const m = new T();

    const prevNodeEnv = process.env.NODE_ENV;
    const prevWorker = process.env.JEST_WORKER_ID;
    try {
      // First operand TRUE path
      process.env.NODE_ENV = 'test';
      delete process.env.JEST_WORKER_ID;
      expect(m.isTestMode()).toBe(true);

      // First operand FALSE path (while overall still true due to Jest global)
      process.env.NODE_ENV = 'production';
      delete process.env.JEST_WORKER_ID;
      expect(m.isTestMode()).toBe(true);
    } finally {
      if (prevNodeEnv !== undefined) process.env.NODE_ENV = prevNodeEnv; else delete process.env.NODE_ENV;
      if (prevWorker !== undefined) process.env.JEST_WORKER_ID = prevWorker; else delete process.env.JEST_WORKER_ID;
    }

    await m.shutdown();
  });
});

