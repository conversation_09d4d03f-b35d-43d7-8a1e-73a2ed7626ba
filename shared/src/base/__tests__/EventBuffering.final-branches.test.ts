/**
 * EventBuffering – Final Branches touch-ups (error path)
 */

import { EventBuffering } from '../event-handler-registry/modules/EventBuffering';

describe('EventBuffering – Final Branches', () => {
  it('flushEvents catch path emits flushError when removeItem throws', async () => {
    const svc = new EventBuffering({ bufferSize: 3, maxFlushSize: 2, autoFlushThreshold: 0.95 });
    await svc.initialize();

    // Put two events
    await svc.bufferEvent('evt', { a: 1 }, {} as any);
    await svc.bufferEvent('evt', { a: 2 }, {} as any);

    // Monkey-patch removeItem to throw once to exercise catch path
    const originalRemove = (svc as any)._eventBuffer.removeItem.bind((svc as any)._eventBuffer);
    let threw = false;
    (svc as any)._eventBuffer.removeItem = async (_key: string) => {
      if (!threw) {
        threw = true;
        throw new Error('remove-fail');
      }
      return originalRemove(_key);
    };

    await expect(svc.flushEvents(2)).rejects.toThrow('remove-fail');

    await svc.shutdown();
  });
});

