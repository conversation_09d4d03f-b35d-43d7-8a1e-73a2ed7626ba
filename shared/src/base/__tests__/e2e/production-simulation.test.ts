/**
 * @file Production Simulation E2E Tests for OA Framework Enhanced Services
 * @task-id T-TSK-03.SUB-01.5.PRS-01
 * @location shared/src/base/__tests__/e2e/production-simulation.test.ts
 * @standards Anti-Simplification | MEM-SAFE-002 | Essential Coding Criteria | GOV-AI-TEST-001
 * 
 * Description
 * - Simulates realistic production scenarios across Enhanced Services under stress conditions
 * - Validates initialization, high-volume operations, metrics/health, cleanup, memory safety
 */

import { CleanupCoordinatorEnhanced, CleanupOperationType, CleanupPriority } from '../../CleanupCoordinatorEnhanced';
import { MemorySafetyManagerEnhanced, createEnhancedMemorySafetyManager } from '../../MemorySafetyManagerEnhanced';
import { AtomicCircularBufferEnhanced } from '../../AtomicCircularBufferEnhanced';
import { MemorySafeResourceManagerEnhanced } from '../../MemorySafeResourceManagerEnhanced';
import { TimerCoordinationServiceEnhanced } from '../../TimerCoordinationServiceEnhanced';
import { EventHandlerRegistryEnhanced } from '../../EventHandlerRegistryEnhanced';
import { CleanupOperationFunction } from '../../types/CleanupTypes';

jest.setTimeout(60000);

type Services = {
  cleanup: CleanupCoordinatorEnhanced;
  timer: TimerCoordinationServiceEnhanced;
  events: EventHandlerRegistryEnhanced;
  memory: MemorySafetyManagerEnhanced;
  buffer: AtomicCircularBufferEnhanced<any>;
  resource: MemorySafeResourceManagerEnhanced;
};

const withTimeout = async <T>(promise: Promise<T>, ms: number, label: string): Promise<T> => {
  let timeoutHandle: NodeJS.Timeout | null = null;
  const timeoutPromise = new Promise<never>((_, reject) => {
    timeoutHandle = setTimeout(() => reject(new Error(`${label} timed out after ${ms}ms`)), ms);
  });
  try {
    const result = await Promise.race([promise, timeoutPromise]);
    if (timeoutHandle) clearTimeout(timeoutHandle);
    return result as T;
  } finally {
    if (timeoutHandle) clearTimeout(timeoutHandle);
  }
};

function createServices(): Services {
  return {
    cleanup: new CleanupCoordinatorEnhanced({ testMode: true }),
    timer: new TimerCoordinationServiceEnhanced({}),
    events: new EventHandlerRegistryEnhanced(),
    memory: createEnhancedMemorySafetyManager(),
    buffer: new AtomicCircularBufferEnhanced<any>(200),
    resource: new MemorySafeResourceManagerEnhanced(),
  };
}

async function initializeServices(s: Services): Promise<void> {
  await withTimeout(s.memory.initialize(), 7000, 'MemorySafetyManagerEnhanced initialize');
  await withTimeout(s.cleanup.initialize(), 7000, 'CleanupCoordinatorEnhanced initialize');
  await withTimeout(s.buffer.initialize(), 7000, 'AtomicCircularBufferEnhanced initialize');
  await withTimeout(s.events.initialize(), 7000, 'EventHandlerRegistryEnhanced initialize');
}

async function shutdownServices(s: Services): Promise<void> {
  try { await withTimeout(s.events.shutdown(), 4000, 'EventHandlerRegistryEnhanced shutdown'); } catch {}
  try { await withTimeout(s.buffer.shutdown(), 4000, 'AtomicCircularBufferEnhanced shutdown'); } catch {}
  try { await withTimeout(s.cleanup.shutdown(), 4000, 'CleanupCoordinatorEnhanced shutdown'); } catch {}
  try { await withTimeout(s.memory.shutdown(), 4000, 'MemorySafetyManagerEnhanced shutdown'); } catch {}
  try { await withTimeout(s.resource.shutdown(), 4000, 'MemorySafeResourceManagerEnhanced shutdown'); } catch {}
}

function memoryBaseline(): number { return process.memoryUsage().heapUsed; }
function gc(): void { if ((global as any).gc) (global as any).gc(); }

/**
 * Simulate production-like workload: 200 events, buffer ops, periodic cleanup
 */
async function simulateProductionWorkload(s: Services): Promise<{ emitted: number; ops: number; clean: number }>{
  // Register handlers (including middleware path use via metadata)
  await s.events.registerHandler('prod-client', 'order.created', async (evt) => {
    await s.buffer.addItem(`order:${evt.id}`, evt);
  }, { source: 'prod-simulator' });

  // Bulk emit events
  const total = 200;
  let success = 0;
  for (let i = 0; i < total; i++) {
    const res = await s.events.emitEvent('order.created', { id: `ORD-${i}`, amount: i * 3.14 }, { priority: 'normal' });
    if (res.successfulHandlers >= 1) success++;
  }

  // Buffer snapshot/restore stress
  const snapshot = await s.buffer.createSnapshot();
  await s.buffer.addItem('stress-key', { now: Date.now() });
  await s.buffer.restoreFromSnapshot(snapshot);

  // Register and schedule periodic cleanup operation
  let cleaned = 0;
  const cleanOp: CleanupOperationFunction = async (component: string) => {
    cleaned++;
    return { success: true, duration: 2, component, operation: 'periodic-clean', timestamp: new Date() };
  };
  s.cleanup.registerCleanupOperation('periodic-clean', cleanOp);
  const opId = s.cleanup.scheduleCleanup(
    CleanupOperationType.RESOURCE_CLEANUP,
    'periodic-clean',
    async () => { await s.buffer.addItem('cleanup-marker', Date.now()); cleaned++; },
    { priority: CleanupPriority.NORMAL }
  );
  expect(typeof opId).toBe('string');
  await s.cleanup.processQueue();

  // Metrics/health checks
  expect(s.buffer.isHealthy()).toBe(true);
  expect(s.memory.isHealthy()).toBe(true);

  return { emitted: success, ops: total, clean: cleaned };
}

describe('E2E - Production Simulation (Enhanced Services)', () => {
  let s: Services;
  let baseline: number;

  beforeEach(() => {
    s = createServices();
    baseline = memoryBaseline();
  });

  afterEach(async () => {
    await shutdownServices(s);
    gc();
    jest.clearAllMocks();
  });

  it('should simulate production workload end-to-end without memory leaks or errors', async () => {
    await initializeServices(s);

    const result = await simulateProductionWorkload(s);
    expect(result.emitted).toBe(result.ops);
    expect(result.clean).toBeGreaterThanOrEqual(1);

    await shutdownServices(s);

    // Memory safety: ensure bounded growth
    gc();
    const used = process.memoryUsage().heapUsed;
    const diff = used - baseline;
    const MAX_GROWTH = 50 * 1024 * 1024; // 50MB
    expect(diff).toBeLessThanOrEqual(MAX_GROWTH);
  });
});

