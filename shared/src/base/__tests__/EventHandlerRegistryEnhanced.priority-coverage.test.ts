import { EventHandlerRegistryEnhanced } from '../EventHandlerRegistryEnhanced';
import { EventHandlerRegistry, resetEventHandlerRegistry } from '../EventHandlerRegistry';
import { MemorySafeResourceManager } from '../MemorySafeResourceManager';

// Ensure shared-only scope; no server imports

describe('EventHandlerRegistryEnhanced – Priority Coverage', () => {
  beforeEach(async () => {
    await resetEventHandlerRegistry();
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(async () => {
    await resetEventHandlerRegistry();
    jest.clearAllTimers();
  });

  function createRegistry(config: any = {}) {
    const registry = new EventHandlerRegistryEnhanced(config);
    return registry;
  }

  it('initialize catch path logs and rethrows (193-194)', async () => {
    const spy = jest
      .spyOn(MemorySafeResourceManager.prototype as any, 'initialize')
      .mockRejectedValueOnce(new Error('init-fail'));

    const registry = createRegistry();
    const logSpy = jest.spyOn(registry as any, 'logError');

    await expect(registry.initialize()).rejects.toThrow('init-fail');
    expect(logSpy).toHaveBeenCalledWith(
      'EventHandlerRegistryEnhanced initialization failed',
      expect.any(Error)
    );

    spy.mockRestore();
  });

  it('shutdown catch path logs and rethrows (204-205)', async () => {
    const initSpy = jest
      .spyOn(MemorySafeResourceManager.prototype as any, 'initialize')
      .mockResolvedValueOnce(undefined);
    const shutSpy = jest
      .spyOn(MemorySafeResourceManager.prototype as any, 'shutdown')
      .mockRejectedValueOnce(new Error('shutdown-fail'));

    const registry = createRegistry();
    await expect(registry.initialize()).resolves.toBeUndefined();

    const logSpy = jest.spyOn(registry as any, 'logError');
    await expect(registry.shutdown()).rejects.toThrow('shutdown-fail');
    expect(logSpy).toHaveBeenCalledWith(
      'EventHandlerRegistryEnhanced shutdown failed',
      expect.any(Error)
    );

    initSpy.mockRestore();
    shutSpy.mockRestore();
  });

  it('registerHandler dedup check failure logs warning (313)', async () => {
    const registry = createRegistry({
      deduplication: { enabled: true, strategy: 'signature', autoMergeMetadata: true }
    });
    await registry.initialize();

    (registry as any)._deduplicationEngine.checkForDuplicate = jest
      .fn()
      .mockRejectedValueOnce(new Error('dedup-fail'));

    const warnSpy = jest.spyOn(registry as any, 'logWarn');

    const id = await registry.registerHandler('c1', 'evt', () => 'ok');
    expect(typeof id).toBe('string');
    expect(warnSpy).toHaveBeenCalledWith(
      'Deduplication check failed, proceeding with registration',
      expect.objectContaining({ error: expect.any(Error) })
    );
  });

  it('registerHandler signature registration failure logs warning (331)', async () => {
    const registry = createRegistry({
      deduplication: { enabled: true, strategy: 'signature', autoMergeMetadata: true }
    });
    await registry.initialize();

    (registry as any)._deduplicationEngine.registerHandlerSignature = jest
      .fn()
      .mockImplementationOnce(() => { throw new Error('sig-fail'); });

    const warnSpy = jest.spyOn(registry as any, 'logWarn');

    const id = await registry.registerHandler('c1', 'evt', () => 'ok');
    expect(typeof id).toBe('string');
    expect(warnSpy).toHaveBeenCalledWith(
      'Failed to register handler signature for deduplication',
      expect.objectContaining({ error: expect.any(Error), handlerId: expect.any(String) })
    );
  });

  it('getInstance/resetInstance cover singleton lifecycle (343-363)', async () => {
    const inst = EventHandlerRegistryEnhanced.getInstance();
    expect(inst).toBeInstanceOf(EventHandlerRegistryEnhanced);
    const shutdownSpy = jest.spyOn(inst, 'shutdown').mockResolvedValueOnce();
    await EventHandlerRegistryEnhanced.resetInstance();
    expect(shutdownSpy).toHaveBeenCalled();
  });

  it('emitEventBatch inner catch increments failed and logs (473-477)', async () => {
    const registry = createRegistry();
    await registry.initialize();
    const errSpy = jest.spyOn(registry as any, 'logError');

    const events = [
      { eventType: 'valid', data: {} },
      { eventType: '', data: {} } // invalid → emitEvent throws
    ];

    await registry.registerHandler('c', 'valid', () => 'x');

    const result = await registry.emitEventBatch(events as any);
    expect(result.failedEvents).toBeGreaterThan(0);
    expect(errSpy).toHaveBeenCalledWith(
      'Batch event emission failed',
      expect.any(Error),
      expect.objectContaining({ batchId: expect.any(String), eventType: '' })
    );
  });

  it('emitEventBatch outer catch records error timing (497-499)', async () => {
    const registry = createRegistry();
    await registry.initialize();

    const metrics = (registry as any)._metricsCollector;
    const recordSpy = jest.spyOn(metrics, 'recordTiming');

    // Throw only on the 'eventBatch' recording (after loop), not during inner emissions
    recordSpy.mockImplementation((name: string, timing: any) => {
      if (name === 'eventBatch') {
        throw new Error('metrics-fail');
      }
      return undefined as any;
    });

    await registry.registerHandler('c', 'evt', () => 'ok');

    await expect(
      registry.emitEventBatch([{ eventType: 'evt', data: {} }] as any)
    ).rejects.toThrow('metrics-fail');

    // The catch should record eventBatchError timing
    expect(recordSpy).toHaveBeenCalledWith(
      'eventBatchError',
      expect.objectContaining({ duration: expect.any(Number) })
    );
  });

  it('emitEventWithTimeout resolves before timeout in production branch (522, 534, 542-554)', async () => {
    const prevEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'production';

    const registry = createRegistry();
    await registry.initialize();

    await registry.registerHandler('c', 'evt', async () => 'ok');

    const p = registry.emitEventWithTimeout('evt', {}, 1000);
    await expect(p).resolves.toMatchObject({ eventType: 'evt' });

    if (prevEnv !== undefined) process.env.NODE_ENV = prevEnv; else delete process.env.NODE_ENV;
  });

  it('emitEventWithTimeout times out in production branch and cleans up (519-524)', async () => {
    const prevEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'production';

    const registry = createRegistry();
    await registry.initialize();

    // Register a handler that never resolves to force timeout
    await registry.registerHandler('c', 'slow', async () => new Promise(() => {}));

    const p = registry.emitEventWithTimeout('slow', {}, 10);
    // Advance fake timers to trigger timeout
    jest.advanceTimersByTime(20);
    await expect(p).rejects.toThrow(/timeout/i);

    if (prevEnv !== undefined) process.env.NODE_ENV = prevEnv; else delete process.env.NODE_ENV;
  });

  it('addMiddleware before initialize throws (567) and removeMiddleware warns and returns false (582-583)', async () => {
    const registry = createRegistry();
    await expect(() => registry.addMiddleware({ name: 'm', priority: 1 } as any)).toThrow();

    const warnSpy = jest.spyOn(registry as any, 'logWarn');
    const removed = registry.removeMiddleware('m');
    expect(removed).toBe(false);
    expect(warnSpy).toHaveBeenCalledWith('Middleware manager not initialized', { name: 'm' });
  });

  it('_executeHandlerWithMiddleware path without manager (604-621) success and error; _executeHandlers catch (942-949)', async () => {
    const registry = createRegistry();
    await registry.initialize();

    // Remove middleware manager
    (registry as any)._middlewareManager = undefined;

    // Register one success and one throwing handler
    await registry.registerHandler('c1', 'evt', () => 'ok');
    await registry.registerHandler('c2', 'evt', () => { throw 'non-error'; });

    const res = await registry.emitEvent('evt', {});
    expect(res.successfulHandlers + res.failedHandlers).toBe(2);
    expect(res.failedHandlers).toBe(1);
  });

  it('_findDuplicateHandler returns existing handler (640-652) and null path (654-655)', async () => {
    const registry = createRegistry({
      deduplication: { enabled: true, strategy: 'signature', autoMergeMetadata: true }
    });
    await registry.initialize();

    const id = await registry.registerHandler('c', 'evt', () => 'ok');

    (registry as any)._deduplicationEngine.checkForDuplicate = jest
      .fn()
      .mockResolvedValueOnce({ isDuplicate: true, existingHandlerId: id })
      .mockResolvedValueOnce({ isDuplicate: false });

    const existing = await (registry as any)._findDuplicateHandler('c', 'evt', () => 'x', {});
    expect(existing?.id).toBe(id);

    const none = await (registry as any)._findDuplicateHandler('c', 'evt', () => 'x', {});
    expect(none).toBeNull();
  });

  it('emitEventBuffered bypasses buffering when disabled (692-694)', async () => {
    const registry = createRegistry();
    await registry.initialize();

    const emitSpy = jest.spyOn(registry, 'emitEvent').mockResolvedValueOnce({
      eventId: 'E-1', eventType: 'evt', targetHandlers: 1, successfulHandlers: 1, failedHandlers: 0,
      executionTime: 0, handlerResults: [], errors: []
    } as any);

    const eventId = await registry.emitEventBuffered('evt', {});
    expect(eventId).toBe('E-1');
    expect(emitSpy).toHaveBeenCalled();
  });

  it('delegate getters and unregisterClientHandlers coverage (339, 341-343)', async () => {
    const registry = createRegistry();
    await registry.initialize();

    const id1 = await registry.registerHandler('clientX', 'eventX', () => 'a');
    await registry.registerHandler('clientX', 'eventX', () => 'b');

    const handlers = registry.getHandlersForEvent('eventX');
    expect(handlers.length).toBeGreaterThanOrEqual(1);

    const handler = registry.getHandler(id1);
    expect(handler?.id).toBe(id1);

    const removedCount = registry.unregisterClientHandlers('clientX');
    expect(removedCount).toBeGreaterThanOrEqual(1);
  });

  it('emitEventToClient returns structured result (443-449)', async () => {
    const registry = createRegistry();
    await registry.initialize();

    await registry.registerHandler('client1', 'ev', () => 'ok');
    const res = await registry.emitEventToClient('client1', 'ev', {});
    expect(res.targetClientId).toBe('client1');
    expect(res.targetHandlers).toBe(1);
  });

  it('emitEvent validation error records error timing (429-431)', async () => {
    const registry = createRegistry();
    await registry.initialize();

    await expect(registry.emitEvent('', {})).rejects.toThrow(/Invalid eventType/);
  });

  it('registerHandler duplicate path merges metadata and increments metric (302-309)', async () => {
    const registry = createRegistry({ deduplication: { enabled: true, strategy: 'signature', autoMergeMetadata: true } });
    await registry.initialize();

    const firstId = await registry.registerHandler('c', 'evtDup', () => 'x', { a: 1 } as any);

    // Force dedup engine to report duplicate for the second registration
    (registry as any)._deduplicationEngine.checkForDuplicate = jest.fn().mockResolvedValue({ isDuplicate: true, existingHandlerId: firstId });

    const returnedId = await registry.registerHandler('c', 'evtDup', () => 'x', { b: 2 } as any);
    expect(returnedId).toBe(firstId);

    const handler = registry.getHandler(firstId)!;
    expect(handler.metadata).toMatchObject({ a: 1, b: 2 });

    const metrics = registry.getEnhancedMetrics();
    expect(metrics.duplicatesDetected).toBeGreaterThanOrEqual(1);
  });

  it('emitEventWithTimeout uses test-mode setImmediate path (531)', async () => {
    // Keep NODE_ENV as test (default), and use real timers to allow setImmediate
    jest.useRealTimers();
    const registry = createRegistry();
    await registry.initialize();

    await registry.registerHandler('c', 'slowTest', async () => new Promise(() => {}));

    await expect(registry.emitEventWithTimeout('slowTest', {}, 5)).rejects.toThrow(/timeout/i);

    // Restore fake timers for rest of suite
    jest.useFakeTimers();
  });

  it('enableEventBuffering logs and initializes when enabled (676-678)', async () => {
    const registry = createRegistry();
    await registry.initialize();

    const infoSpy = jest.spyOn(registry as any, 'logInfo');
    registry.enableEventBuffering({ enabled: true, bufferSize: 3, flushInterval: 50, bufferStrategy: 'fifo', autoFlushThreshold: 0.6, onBufferOverflow: 'drop_oldest' } as any);

    expect(infoSpy).toHaveBeenCalledWith(
      'Event buffering enabled',
      expect.objectContaining({ bufferSize: 3, strategy: 'fifo' })
    );
  });

  it('_performEnterpriseEventFlush logs on emit error (723)', async () => {
    const registry = createRegistry({ buffering: { enabled: true, bufferSize: 2, flushInterval: 50, bufferStrategy: 'fifo', autoFlushThreshold: 1, onBufferOverflow: 'drop_oldest' } });
    await registry.initialize();

    (registry as any)._eventBuffering.flushEvents = jest.fn().mockResolvedValueOnce([
      { id: 'b1', type: 'evt', data: {}, options: {} }
    ]);
    const emitSpy = jest.spyOn(registry, 'emitEvent').mockRejectedValueOnce(new Error('emit-fail'));
    const logSpy = jest.spyOn(registry as any, 'logError');

    await (registry as any)._performEnterpriseEventFlush();
    expect(emitSpy).toHaveBeenCalled();
    expect(logSpy).toHaveBeenCalledWith(
      'Failed to emit flushed event',
      expect.any(Error),
      expect.objectContaining({ eventId: 'b1', eventType: 'evt' })
    );
  });

  it('_handleEmissionError severity branches (756, 758-759)', async () => {
    const registry = createRegistry();
    const logErr = jest.spyOn(registry as any, 'logError');
    const logWarn = jest.spyOn(registry as any, 'logWarning');

    await (registry as any)._handleEmissionError({ handlerId: 'h', clientId: 'c', error: new Error('unauthorized'), timestamp: new Date() }, 'op');
    expect(logErr).toHaveBeenCalled();

    await (registry as any)._handleEmissionError({ handlerId: 'h2', clientId: 'c2', error: new Error('timeout'), timestamp: new Date() }, 'op2');
    expect(logWarn).toHaveBeenCalled();
  });

  it('disableEventBuffering and getBufferStatus (805-840)', async () => {
    const registry = createRegistry({ buffering: { enabled: true, bufferSize: 3, flushInterval: 50, bufferStrategy: 'fifo', autoFlushThreshold: 1, onBufferOverflow: 'drop_oldest' } });
    await registry.initialize();

    // Pretend buffer has some size
    (registry as any)._eventBuffering.getBufferSize = jest.fn().mockReturnValue(2);

    const statusBefore = registry.getBufferStatus();
    expect(statusBefore).toMatchObject({ enabled: true, currentSize: 2, maxSize: 3 });

    await registry.disableEventBuffering();

    const statusAfter = registry.getBufferStatus();
    expect(statusAfter?.enabled).toBe(false);
  });

  it('_updateMetrics branches and metrics manager delegation error (868-881)', async () => {
    const registry = createRegistry();
    await registry.initialize();

    (registry as any)._metricsManager = { updateEmissionMetrics: jest.fn().mockImplementation(() => { throw new Error('mm-fail'); }) };

    // emission success
    await (registry as any)._updateMetrics('emission', true, 5);
    // middleware
    await (registry as any)._updateMetrics('middleware', true, 0);
    // buffer
    await (registry as any)._updateMetrics('buffer', true, 0);
  });

  it('emitEventBuffered path with buffering enabled covers 697-700', async () => {
    const registry = createRegistry({ buffering: { enabled: true, bufferSize: 3, flushInterval: 50, bufferStrategy: 'fifo', autoFlushThreshold: 1, onBufferOverflow: 'drop_oldest' } });
    await registry.initialize();

    const id = await registry.emitEventBuffered('evt', { a: 1 }, {});
    expect(typeof id).toBe('string');
  });

  it('flushBufferedEvents delegates to processBufferedEvents (789-797)', async () => {
    const registry = createRegistry({ buffering: { enabled: true, bufferSize: 3, flushInterval: 50, bufferStrategy: 'fifo', autoFlushThreshold: 1, onBufferOverflow: 'drop_oldest' } });
    await registry.initialize();

    (registry as any)._eventBuffering.processBufferedEvents = jest.fn().mockResolvedValue(undefined);

    await registry.flushBufferedEvents();
    expect((registry as any)._eventBuffering.processBufferedEvents).toHaveBeenCalled();
  });

  it('add/remove middleware after initialize covers info logging (569-574, 585-591)', async () => {
    const registry = createRegistry();
    await registry.initialize();

    const logInfoSpy = jest.spyOn(registry as any, 'logInfo');

    registry.addMiddleware({ name: 'mid', priority: 1, beforeHandlerExecution: async () => true } as any);
    expect(logInfoSpy).toHaveBeenCalledWith(
      'Middleware added successfully',
      expect.objectContaining({ name: 'mid', priority: 1, status: 'active' })
    );

    const removed = registry.removeMiddleware('mid');
    expect(removed).toBe(true);
    expect(logInfoSpy).toHaveBeenCalledWith(
      'Middleware removal attempted',
      expect.objectContaining({ name: 'mid', removed: true, status: 'removed' })
    );
  });

  it('shutdown success path covers logInfo (202-203)', async () => {
    const registry = createRegistry();
    await registry.initialize();
    await expect(registry.shutdown()).resolves.toBeUndefined();
  });

  it('autoFlush callback triggers emitEvent (244) via threshold', async () => {
    const registry = createRegistry({
      buffering: { enabled: true, bufferSize: 5, flushInterval: 10000, bufferStrategy: 'fifo', autoFlushThreshold: 0.6, onBufferOverflow: 'drop_oldest' }
    });
    await registry.initialize();

    const emitSpy = jest.spyOn(registry, 'emitEvent');

    // 3 events = 60% -> triggers auto-flush
    await registry.emitEventBuffered('evt', { id: 1 });
    await registry.emitEventBuffered('evt', { id: 2 });
    await registry.emitEventBuffered('evt', { id: 3 });

    expect(emitSpy).toHaveBeenCalled();
  });

  it('emitEventBatch else path increments failedEvents when handler fails (473)', async () => {
    const registry = createRegistry();
    await registry.initialize();

    await registry.registerHandler('c', 'bad', () => { throw new Error('boom'); });

    const batch = [{ eventType: 'bad', data: {} }];
    const res = await registry.emitEventBatch(batch as any);

    expect(res.failedEvents).toBeGreaterThan(0);
  });

  it('emitEventWithTimeout catch path via invalid eventType triggers reject (549-554)', async () => {
    const prevEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'production';

    const registry = createRegistry();
    await registry.initialize();

    await expect(registry.emitEventWithTimeout('', {}, 1000)).rejects.toThrow(/Invalid eventType/);

    if (prevEnv !== undefined) process.env.NODE_ENV = prevEnv; else delete process.env.NODE_ENV;
  });

  it('_handleDuplicateRegistration merges metadata and updates lastUsed (659-667)', async () => {
    const registry = createRegistry({
      deduplication: { enabled: true, strategy: 'signature', autoMergeMetadata: true, onDuplicateDetected: jest.fn() }
    });
    await registry.initialize();

    const existing: any = { id: 'h1', clientId: 'c', eventType: 'evt', callback: () => 'ok', registeredAt: new Date(), lastUsed: new Date(0), metadata: { a: 1 } };
    const before = existing.lastUsed.getTime();

    await (registry as any)._handleDuplicateRegistration(existing, () => 'dup', { b: 2 });

    expect(existing.metadata).toMatchObject({ a: 1, b: 2 });
    expect(existing.lastUsed.getTime()).toBeGreaterThan(before);
  });

  it('getEnhancedMetrics returns aggregated metrics after emission (783, 795)', async () => {
    const registry = createRegistry();
    await registry.initialize();

    await registry.registerHandler('c', 'evt', () => 'ok');
    await registry.emitEvent('evt', {});

    const m = registry.getEnhancedMetrics();
    expect(m.totalEmissions).toBeGreaterThan(0);
    expect(m.totalHandlers).toBeGreaterThan(0);
  });

  it('doShutdown with buffering enabled flushes and shuts down modules (259-275)', async () => {
    const registry = createRegistry({ buffering: { enabled: true, bufferSize: 2, flushInterval: 50, bufferStrategy: 'fifo', autoFlushThreshold: 1, onBufferOverflow: 'drop_oldest' } });
    await registry.initialize();
    await expect((registry as any).doShutdown()).resolves.toBeUndefined();
  });


  it('middleware getters and getBufferStatus null (819-821, 834)', async () => {
    const registry = createRegistry();
    await registry.initialize();

    expect(registry.getMiddleware()).toEqual([]);
    expect(registry.getMiddlewareByName('x')).toBeUndefined();
    expect(registry.getBufferStatus()).toBeNull();

    // clearAllMiddleware logs info but returns void without effect
    const logInfoSpy = jest.spyOn(registry as any, 'logInfo');
    registry.clearAllMiddleware();
    expect(logInfoSpy).toHaveBeenCalledWith('Middleware functionality disabled');
  });

  it('_executeHandlers non-throwing failure path (932-939)', async () => {
    const registry = createRegistry();
    await registry.initialize();

    // Stub middleware manager to return a failure result without throwing
    (registry as any)._middlewareManager.executeHandlerWithMiddleware = jest.fn().mockResolvedValue({
      handlerId: 'h1', clientId: 'c1', success: false, result: 'oops', executionTime: 0
    });

    await registry.registerHandler('c', 'evt', () => 'ignored');

    const res = await (registry as any)._executeHandlers(
      [{ id: 'h1', clientId: 'c1', eventType: 'evt', callback: () => 'ignored', registeredAt: new Date(), lastUsed: new Date() }],
      {},
      'evt',
      'E-1'
    );

    expect(res.errors.length).toBe(1);
  });

  it('_validateEmissionOptions throws on conflicts and invalid timeout (893-897, 902)', async () => {
    const registry = createRegistry();

    expect(() => (registry as any)._validateEmissionOptions({ targetClients: ['a'], excludeClients: ['a'] })).toThrow();
    expect(() => (registry as any)._validateEmissionOptions({ timeout: -1 })).toThrow();
  });
});

