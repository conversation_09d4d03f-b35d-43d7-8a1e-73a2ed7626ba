/**
 * MiddlewareManager – Coverage Boost to ≥95% Branches
 * Targets uncovered lines: 72 (config fallbacks), 164 (sort comparator fallback),
 * 333 (metadata fallback), 359 (non-Error in before catch → onHandlerError), 373 (non-Error handler failure)
 */

import { MiddlewareManager } from '../event-handler-registry/modules/MiddlewareManager';

// Minimal handler factory
const makeHandler = (id: string, withMetadata: boolean) => ({
  id,
  clientId: 'c1',
  eventType: 'evt',
  callback: async (data: any) => {
    if (data && data.throwString) throw 'plain-failure'; // non-Error branch at 373
    if (data && data.throwError) throw new Error('boom');
    return { ok: true, data };
  },
  registeredAt: new Date(),
  lastUsed: new Date(),
  metadata: withMetadata ? { tag: 'x' } : undefined
});

describe('MiddlewareManager – Coverage Boost ≥95% Branches', () => {
  it('covers constructor default-param path (line 72) and config fallback/provided branches', async () => {
    // Default param path: no args
    const mmNoArgs = new MiddlewareManager();
    expect((mmNoArgs as any)._config.maxMiddleware).toBe(10);

    // Fallbacks with explicit empty object
    const mmDefault = new MiddlewareManager({});
    expect((mmDefault as any)._config.maxMiddleware).toBe(10);
    expect((mmDefault as any)._config.enableTiming).toBe(true);
    expect((mmDefault as any)._config.timeoutMs).toBe(5000);

    // Provided values (including false and non-fallback numeric)
    const mmCustom = new MiddlewareManager({ maxMiddleware: 3, enableTiming: false, timeoutMs: 300 });
    expect((mmCustom as any)._config.maxMiddleware).toBe(3);
    expect((mmCustom as any)._config.enableTiming).toBe(false);
    expect((mmCustom as any)._config.timeoutMs).toBe(300);

    // Provided falsy to force fallback (timeoutMs 0 -> fallback to 5000)
    const mmZero = new MiddlewareManager({ timeoutMs: 0 });
    expect((mmZero as any)._config.timeoutMs).toBe(5000);
  });

  it('covers sort comparator fallback for priority=0 and multiple comparisons (line 164)', async () => {
    const mm = new MiddlewareManager({});
    await mm.initialize();

    mm.addMiddleware({ name: 'a', priority: 0 } as any);
    mm.addMiddleware({ name: 'b', priority: 0 } as any);
    mm.addMiddleware({ name: 'c', priority: 5 } as any);

    const list = mm.getMiddleware();
    // Sorting should place 'c' first; 'a' and 'b' order is stable when equal
    expect(list[0].name).toBe('c');

    await mm.shutdown();
  });

  it('covers metadata fallback (line 333) and before catch non-Error onHandlerError branch (line 359)', async () => {
    const mm = new MiddlewareManager({ enableTiming: true });
    await mm.initialize();

    // Middleware where before throws a non-Error; onHandlerError handles it (returns true)
    mm.addMiddleware({
      name: 'guard',
      priority: 1,
      beforeHandlerExecution: async () => { throw 'not-an-error'; }
      , onHandlerError: async () => true
    } as any);

    const res = await mm.executeHandlerWithMiddleware(makeHandler('h-meta', false) as any, { a: 1 }, 'evt');
    // Expect success due to onHandlerError handling
    expect(res.success).toBe(true);

    await mm.shutdown();
  });

  it('covers handler non-Error failure → conversion branch at 373 and recovery', async () => {
    const mm = new MiddlewareManager({ enableTiming: true });
    await mm.initialize();

    // First middleware will handle handler error
    mm.addMiddleware({
      name: 'recover',
      priority: 10,
      beforeHandlerExecution: async () => true,
      onHandlerError: async () => true
    } as any);

    const res = await mm.executeHandlerWithMiddleware(makeHandler('h-nonerr', true) as any, { throwString: true }, 'evt');
    expect(res.success).toBe(true);

    await mm.shutdown();
  });
});

