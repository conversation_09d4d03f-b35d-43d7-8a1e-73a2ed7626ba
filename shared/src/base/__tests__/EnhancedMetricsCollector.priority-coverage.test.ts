import { EnhancedMetricsCollector } from '../memory-safety-manager/modules/EnhancedMetricsCollector';

// Scope: shared/src only

describe('EnhancedMetricsCollector – Priority Coverage', () => {
  beforeEach(() => {
    jest.useFakeTimers();
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllTimers();
  });

  function createCollector() {
    return new EnhancedMetricsCollector() as any;
  }

  it('initializes periodic intervals and cleans up on shutdown', async () => {
    const col: any = createCollector();
    await col.initialize();

    const before = col.getResourceMetrics().activeIntervals; // from MemorySafeResourceManager
    expect(before).toBeGreaterThanOrEqual(2); // two intervals created in doInitialize

    // Add some data then shutdown
    col.recordComponentMetric('compA', 'execution-time', 10);
    col.recordPerformanceMetric('op1', 50);

    await col.shutdown();

    // Intervals cleaned by base shutdown
    const after = col.getResourceMetrics().activeIntervals;
    expect(after).toBe(0);

    // Metrics cleared by doShutdown
    const summary = col.getMetricsSummary();
    expect(summary.totalMetrics).toBe(0);
    expect(col.getPerformanceTrends().trends.size).toBe(0);
  });

  it('collects system metrics and records collection duration; error path rethrows and ends timer', async () => {
    const col: any = createCollector();
    await col.initialize();

    // Success path
    const rt = jest.spyOn((col as any)._baseMetricsCollector, 'recordTiming');
    const metrics = await col.collectSystemMetrics();
    expect(metrics).toHaveProperty('eventHandlers');
    expect(rt).toHaveBeenCalledWith(
      'metrics-collection-duration',
      expect.objectContaining({ duration: expect.any(Number) })
    );

    // Error path via recordTiming throwing
    rt.mockImplementationOnce(() => { throw new Error('timing-fail'); });
    await expect(col.collectSystemMetrics()).rejects.toThrow('timing-fail');
  });

  it('records component metrics across types and updates averages/memory/errors', async () => {
    const col: any = createCollector();
    await col.initialize();

    col.recordComponentMetric('comp1', 'execution-time', 100);
    col.recordComponentMetric('comp1', 'execution-time', 50);
    col.recordComponentMetric('comp1', 'memory-usage', 2048);
    col.recordComponentMetric('comp1', 'error', 1);

    const summary = col.getMetricsSummary();
    const cm = summary.componentMetrics.get('comp1')!;
    expect(cm.operationCount).toBe(2);
    expect(cm.averageExecutionTime).toBeCloseTo(75, 1);
    expect(cm.memoryUsage).toBe(2048);
    expect(cm.errorRate).toBeGreaterThan(0);
    expect(cm.lastActivity).toBeInstanceOf(Date);
  });

  it('records performance metrics, aggregates properly, and stores trend data with trimming', async () => {
    const col: any = createCollector();
    await col.initialize();

    col.recordPerformanceMetric('opX', 1200); // >1000 to affect performance health
    col.recordPerformanceMetric('opX', 300);
    col.recordPerformanceMetric('opY', 50);

    // Add many points to opX to trigger trimming to last 100
    for (let i = 0; i < 105; i++) {
      col.recordPerformanceMetric('opX', i);
    }

    const summary = col.getMetricsSummary();
    const pmX = summary.performanceMetrics.get('opX')!;
    expect(pmX.totalExecutions).toBeGreaterThanOrEqual(107);
    expect(pmX.minDuration).toBeGreaterThanOrEqual(0);
    expect(pmX.maxDuration).toBeGreaterThanOrEqual(pmX.minDuration);

    const trends = col.getPerformanceTrends();
    const opXTrend = trends.trends.get('opX')!;
    expect(opXTrend.dataPoints.length).toBeLessThanOrEqual(100);
  });

  it('getPerformanceTrends includes recommendations based on error rate threshold', async () => {
    const col: any = createCollector();
    await col.initialize();

    // Drive error rate above 0.1 overall
    for (let i = 0; i < 5; i++) col.recordComponentMetric('c', 'execution-time', 10);
    for (let i = 0; i < 2; i++) col.recordComponentMetric('c', 'error', 1);

    const trends = col.getPerformanceTrends();
    expect(trends.recommendations.some((r: string) => r.includes('High error rate'))).toBe(true);
  });

  it('assesses system health considering components, performance, and memory health', async () => {
    const col: any = createCollector();
    await col.initialize();

    // Components: increase error and inactivity for health impact
    col.recordComponentMetric('hc', 'execution-time', 10);
    col.recordComponentMetric('hc', 'error', 1);
    // Simulate inactivity > 10 mins by backdating lastActivity
    const cm = col.getMetricsSummary().componentMetrics.get('hc')!;
    (cm as any).lastActivity = new Date(Date.now() - (601_000));

    // Performance: add slow operation to reduce performance health
    col.recordPerformanceMetric('slowOp', 1500);

    // Mock memory usage to high ratio >0.9
    const originalMem = process.memoryUsage;
    (process as any).memoryUsage = () => ({ heapUsed: 91, heapTotal: 100 });

    const assessment = col.assessSystemHealth();
    expect(['excellent','good','fair','poor','critical']).toContain(assessment.overallHealth);
    expect(assessment.healthScore).toBeGreaterThanOrEqual(0);
    expect(assessment.healthScore).toBeLessThanOrEqual(100);
    expect(assessment.componentHealth.size).toBeGreaterThan(0);

    // Restore
    (process as any).memoryUsage = originalMem;
  });

  it('collectSystemMetrics computes totals with populated component and performance metrics', async () => {
    const col: any = createCollector();
    await col.initialize();

    // Populate component and performance metrics
    col.recordComponentMetric('c1', 'execution-time', 100);
    col.recordComponentMetric('c1', 'memory-usage', 1024);
    col.recordComponentMetric('c1', 'error', 1);
    col.recordPerformanceMetric('opA', 200);
    col.recordPerformanceMetric('opA', 300);

    const m = await col.collectSystemMetrics();
    expect(m.eventHandlers.totalHandlers).toBeGreaterThanOrEqual(1);
    expect(m.cleanup.totalOperations).toBeGreaterThanOrEqual(2);
    expect(m.cleanup.averageExecutionTime).toBeGreaterThan(0);
    expect(m.timers.coordinatedOperations).toBeGreaterThanOrEqual(2);
  });

  it('memory health thresholds and health category mapping across ranges', async () => {
    const col: any = createCollector();
    await col.initialize();

    const originalMem = process.memoryUsage;

    // >0.9 => 20
    (process as any).memoryUsage = () => ({ heapUsed: 91, heapTotal: 100 });
    expect((col as any)['_calculateMemoryHealth']()).toBe(20);

    // >0.8 => 50
    (process as any).memoryUsage = () => ({ heapUsed: 85, heapTotal: 100 });
    expect((col as any)['_calculateMemoryHealth']()).toBe(50);

    // >0.7 => 70
    (process as any).memoryUsage = () => ({ heapUsed: 75, heapTotal: 100 });
    expect((col as any)['_calculateMemoryHealth']()).toBe(70);

    // else => 100
    (process as any).memoryUsage = () => ({ heapUsed: 60, heapTotal: 100 });
    expect((col as any)['_calculateMemoryHealth']()).toBe(100);

    // Category mapping
    expect((col as any)['_getHealthCategory'](95)).toBe('excellent');
    expect((col as any)['_getHealthCategory'](80)).toBe('good');
    expect((col as any)['_getHealthCategory'](65)).toBe('fair');
    expect((col as any)['_getHealthCategory'](45)).toBe('poor');
    expect((col as any)['_getHealthCategory'](10)).toBe('critical');

    (process as any).memoryUsage = originalMem;
  });

  it('directly calls periodic collectors to execute their bodies', async () => {
    const col: any = createCollector();
    await col.initialize();

    (col as any)['_collectPeriodicMetrics']();
    (col as any)['_analyzePerformanceTrends']();
    expect(true).toBe(true);
  });

  it('overall error rate zero branch when no operations exist', async () => {
    const col: any = createCollector();
    await col.initialize();

    // Ensure no component operations
    const rate = (col as any)['_calculateOverallErrorRate']();
    expect(rate).toBe(0);
  });

});

