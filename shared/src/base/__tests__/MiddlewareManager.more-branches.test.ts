/**
 * MiddlewareManager – More Branches (push Branches ≥90%)
 */

import { MiddlewareManager } from '../event-handler-registry/modules/MiddlewareManager';

const makeHandler = (id: string, clientId = 'c1') => ({
  id,
  clientId,
  eventType: 'evt',
  callback: async (data: any) => {
    if (data && data.throw) throw new Error('handler-failure');
    return { ok: true, data };
  },
  registeredAt: new Date(),
  lastUsed: new Date(),
  metadata: {}
});

describe('MiddlewareManager – More Branches', () => {
  it('validates addMiddleware inputs and duplicate/max limits', async () => {
    const mm = new MiddlewareManager({ maxMiddleware: 2 });
    await mm.initialize();

    // invalid: missing name
    expect(() => mm.addMiddleware({ name: '' as any, priority: 1 } as any)).toThrow(/valid name/);

    // invalid: non-numeric priority
    expect(() => mm.addMiddleware({ name: 'm1', priority: 'hi' as any } as any)).toThrow(/numeric priority/);

    // add two valid, then duplicate
    mm.addMiddleware({ name: 'm1', priority: 1 } as any);
    expect(() => mm.addMiddleware({ name: 'm1', priority: 2 } as any)).toThrow(/already exists/);

    // reach max limit
    mm.addMiddleware({ name: 'm2', priority: 1 } as any);
    expect(() => mm.addMiddleware({ name: 'm3', priority: 0 } as any)).toThrow(/Maximum middleware limit/);

    await mm.shutdown();
  });

  it('remove/get/clear middleware management paths', async () => {
    const mm = new MiddlewareManager({});
    await mm.initialize();

    mm.addMiddleware({ name: 'a', priority: 1 } as any);
    mm.addMiddleware({ name: 'b', priority: 2 } as any);

    // remove existing returns true
    expect(mm.removeMiddleware('a')).toBe(true);
    // remove non-existing returns false
    expect(mm.removeMiddleware('x')).toBe(false);

    // get by name and list
    expect(mm.getMiddlewareByName('b')!.name).toBe('b');
    const list = mm.getMiddleware();
    expect(Array.isArray(list)).toBe(true);

    // clear all
    mm.clearAllMiddleware();
    expect(mm.getMiddleware().length).toBe(0);

    await mm.shutdown();
  });

  it('no-middleware path executes handler directly and records handlerWithoutMiddleware timing', async () => {
    const mm = new MiddlewareManager({ enableTiming: true });
    await mm.initialize();

    const res = await mm.executeHandlerWithMiddleware(makeHandler('h0') as any, { a: 1 }, 'evt');
    expect(res.success).toBe(true);

    const metrics = mm.getMiddlewareMetrics();
    expect(metrics.totalExecutions).toBeGreaterThanOrEqual(1);

    await mm.shutdown();
  });

  it('executeHandlerWithMiddleware catch path when middleware throws and no handler error recovery', async () => {
    const mm = new MiddlewareManager({ enableTiming: true });
    await mm.initialize();

    mm.addMiddleware({
      name: 'thrower',
      priority: 5,
      beforeHandlerExecution: async () => { throw new Error('mw-fail'); }
      // no onHandlerError -> should bubble to catch in executeHandlerWithMiddleware
    } as any);

    await expect(mm.executeHandlerWithMiddleware(makeHandler('h1') as any, { a: 1 }, 'evt')).rejects.toThrow('mw-fail');

    await mm.shutdown();
  });

  it('beforeHandlerExecution throws but onHandlerError handles and continues to handler', async () => {
    const mm = new MiddlewareManager({ enableTiming: true });
    await mm.initialize();

    mm.addMiddleware({
      name: 'rescue',
      priority: 10,
      beforeHandlerExecution: async () => { throw new Error('pre-check'); },
      onHandlerError: async () => true
    } as any);

    const res = await mm.executeHandlerWithMiddleware(makeHandler('h2') as any, { a: 1 }, 'evt');
    expect(res.success).toBe(true);

    await mm.shutdown();
  });

  it('onHandlerError throws (catch → continue) then next middleware handles; after-handler throws and is ignored', async () => {
    const mm = new MiddlewareManager({ enableTiming: true });
    await mm.initialize();

    // First will throw in onHandlerError
    mm.addMiddleware({
      name: 'faulty-handler',
      priority: 5,
      beforeHandlerExecution: async () => true,
      onHandlerError: async () => { throw new Error('handler-cb'); }
    } as any);

    // Second will handle error
    mm.addMiddleware({
      name: 'final-rescue',
      priority: 4,
      beforeHandlerExecution: async () => true,
      onHandlerError: async () => true,
      afterHandlerExecution: async () => { throw new Error('post'); }
    } as any);

    const res = await mm.executeHandlerWithMiddleware(makeHandler('h3') as any, { throw: true }, 'evt');
    expect(res.success).toBe(true);

    await mm.shutdown();
  });

  it('resetMiddlewareMetrics updates counters and snapshot retrieval works', async () => {
    const mm = new MiddlewareManager({ enableTiming: true });
    await mm.initialize();

    // produce some counts
    mm.addMiddleware({ name: 'pass', priority: 1, beforeHandlerExecution: async () => true } as any);
    await mm.executeHandlerWithMiddleware(makeHandler('h4') as any, { a: 1 }, 'evt');

    const snap1 = mm.getMiddlewareMetrics();
    expect(snap1.totalExecutions).toBeGreaterThanOrEqual(1);

    mm.resetMiddlewareMetrics();
    const snap2 = mm.getMiddlewareMetrics();
    expect(snap2.totalExecutions).toBe(0);

    await mm.shutdown();
  });
});

