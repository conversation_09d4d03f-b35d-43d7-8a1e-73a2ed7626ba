/**
 * TimerCoordinationService – Surgical Branch Coverage
 */

import { TimerCoordinationService } from '../TimerCoordinationService';

describe('TimerCoordinationService – Branch Coverage', () => {
  const saveEnv = () => ({ NODE_ENV: process.env.NODE_ENV, JEST_WORKER_ID: process.env.JEST_WORKER_ID });
  const restoreEnv = (env: { NODE_ENV?: string; JEST_WORKER_ID?: string }) => {
    if (typeof env.NODE_ENV === 'undefined') delete (process.env as any).NODE_ENV; else process.env.NODE_ENV = env.NODE_ENV;
    if (typeof env.JEST_WORKER_ID === 'undefined') delete (process.env as any).JEST_WORKER_ID; else process.env.JEST_WORKER_ID = env.JEST_WORKER_ID as any;
  };

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('should call createSafeInterval for audit and handle catch path (lines 521-530)', () => {
    const env = saveEnv();
    try {
      process.env.NODE_ENV = 'production';
      delete (process.env as any).JEST_WORKER_ID;

      const svc = TimerCoordinationService.getInstance({ maxTimersPerService: 10, maxGlobalTimers: 100, minIntervalMs: 100, timerAuditIntervalMs: 1000 });
      // Spy on createSafeInterval to throw when creating audit interval
      const spy = jest.spyOn(svc as any, 'createSafeInterval').mockImplementation((cb: any, ms: number, name: string) => {
        if (name === 'timer-audit') throw new Error('audit-fail');
        return 'id';
      });

      const id = svc.createCoordinatedInterval(() => {}, 50, 's', 't');
      expect(typeof id).toBe('string');
      expect(spy).toHaveBeenCalled();
    } finally {
      restoreEnv(env);
      TimerCoordinationService.resetInstance();
    }
  });

  it('should throw when base interval limits are reached (line 477)', () => {
    const svc = TimerCoordinationService.getInstance({ maxTimersPerService: 10, maxGlobalTimers: 100, minIntervalMs: 100 });
    jest.spyOn(svc, 'getResourceMetrics').mockReturnValue({
      totalResources: 0,
      activeIntervals: (svc as any)._limits.maxIntervals - 1,
      activeTimeouts: 0,
      memoryUsageMB: 10,
    } as any);
    expect(() => svc.createCoordinatedInterval(() => {}, 200, 'A', 'B')).toThrow(/Global timer limit exceeded/);
    TimerCoordinationService.resetInstance();
  });

  it('should log error when timer callback throws (line 509) and avoid duplicate (lines 451-457)', () => {
    const svc = TimerCoordinationService.getInstance({ maxTimersPerService: 10, maxGlobalTimers: 100, minIntervalMs: 10 });
    jest.useFakeTimers();

    const logSpy = jest.spyOn(svc as any, 'logError').mockImplementation(() => {});

    const id = svc.createCoordinatedInterval(() => { throw 'cb-error'; }, 10, 'svc', 'tid');
    // Duplicate creation returns immediately
    const id2 = svc.createCoordinatedInterval(() => {}, 10, 'svc', 'tid');
    expect(id2).toBe(id);

    // Drive callback
    jest.advanceTimersByTime(10);
    expect(logSpy).toHaveBeenCalledWith('Timer callback error', expect.anything(), expect.objectContaining({ compositeId: id }));

    TimerCoordinationService.resetInstance();
  });

  it('should handle shutdown clearing direct intervals and logError on clear failure (lines 377-385)', async () => {
    const svc = TimerCoordinationService.getInstance({ maxTimersPerService: 10, maxGlobalTimers: 100, minIntervalMs: 10 });
    // Create a timer in test env so it uses direct interval
    const id = svc.createCoordinatedInterval(() => {}, 10, 'svc', 'tid2');

    // Force clearInterval to throw
    const clearSpy = jest.spyOn(global as any, 'clearInterval').mockImplementation(() => { throw new Error('clear-fail'); });
    const errSpy = jest.spyOn(svc as any, 'logError').mockImplementation(() => {});

    await (svc as any).shutdown();

    expect(clearSpy).toHaveBeenCalled();
    expect(errSpy).toHaveBeenCalledWith('Error clearing direct interval', expect.anything(), expect.objectContaining({ id: expect.anything() }));

    TimerCoordinationService.resetInstance();
  });

  it('should audit and warn on high counts and stale timers (lines 641-657, 660-669)', () => {
    const svc = TimerCoordinationService.getInstance({ maxTimersPerService: 2, maxGlobalTimers: 2, minIntervalMs: 10 });
    const warnSpy = jest.spyOn(svc as any, 'logWarning').mockImplementation(() => {});

    // Populate registry with stale timer
    const now = Date.now();
    (svc as any)._timerRegistry.set('a', { serviceId: 's1', timerId: 'a', intervalMs: 100, createdAt: new Date(now - 100000), lastExecution: new Date(now - 3600 * 1000 - 1), executionCount: 1 });
    (svc as any)._timerRegistry.set('b', { serviceId: 's1', timerId: 'b', intervalMs: 100, createdAt: new Date(now - 50000), lastExecution: null, executionCount: 0 });

    (svc as any)._auditTimers();

    expect(warnSpy).toHaveBeenCalled();
    TimerCoordinationService.resetInstance();
  });
});

