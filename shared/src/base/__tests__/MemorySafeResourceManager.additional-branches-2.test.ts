/**
 * MemorySafeResourceManager – Additional Branches 2
 * Targets: 685-743 (global/emergency cleanup), 701-712 (forceGlobalCleanup),
 *          903-938 (singletons), 954-973 (autoCleanup decorator), 763-766 (_isTestMode false)
 */

import {
  MemorySafeResourceManager,
  createMemorySafeSingleton,
  clearMemorySafeSingletons,
  autoCleanup
} from '../MemorySafeResourceManager';

class TestManager extends MemorySafeResourceManager {
  public created: string[] = [];

  protected async doInitialize(): Promise<void> {
    // no-op
  }
  protected async doShutdown(): Promise<void> {
    // no-op
  }

  // Expose a proxy to protected _isTestMode for testing branches
  public setTestModeFalseOnce(): void {
    // Override method on this instance to return false for next call
    const self = this as any;
    const original = this._isTestMode.bind(this);
    self._isTestMode = () => false;
    // Restore after one tick
    setTimeout(() => {
      self._isTestMode = original;
    }, 0);
  }
}

describe('MemorySafeResourceManager – Additional Branches 2', () => {
  const realEnv = { ...process.env };
  let logSpy: jest.SpyInstance;
  let errorSpy: jest.SpyInstance;
  let warnSpy: jest.SpyInstance;

  beforeEach(() => {
    jest.restoreAllMocks();
    process.env = { ...realEnv };
    logSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
    errorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    warnSpy = jest.spyOn(console, 'warn').mockImplementation(() => {});
  });

  afterEach(async () => {
    jest.useRealTimers();
    process.env = { ...realEnv };
  });

  it('covers forceGlobalCleanup and _performEmergencyCleanup paths', async () => {
    const mgr = new TestManager();
    // Create timeout to register a resource
    (mgr as any).createSafeTimeout(() => {}, 5, 't');

    // Static force cleanup should call emergency cleanup on instances
    MemorySafeResourceManager.forceGlobalCleanup();

    // Expect global cleanup and emergency cleanup logs emitted
    expect(logSpy).toHaveBeenCalledWith(
      expect.stringContaining('Force global cleanup triggered')
    );
    expect(logSpy).toHaveBeenCalledWith(
      expect.stringContaining('Performing global cleanup')
    );
    expect(logSpy).toHaveBeenCalledWith(
      expect.stringContaining('Emergency cleanup complete')
    );
  });

  it('covers createMemorySafeSingleton and clearMemorySafeSingletons', async () => {
    const s1 = createMemorySafeSingleton(TestManager);
    const s2 = createMemorySafeSingleton(TestManager);
    expect(s1).toBe(s2);

    await clearMemorySafeSingletons();
    expect(logSpy).toHaveBeenCalledWith(
      expect.stringContaining('All global singletons cleared')
    );
  });

  it('covers autoCleanup decorator scheduling with rejection catch then success', async () => {
    jest.useFakeTimers();

    class DecoratedManager extends TestManager {
      public periodicInvocations = 0;

      triggerPeriodicCleanup = jest
        .fn()
        // First call rejects to exercise the .catch(() => {}) handler inside decorator
        .mockRejectedValueOnce(new Error('decorator-fail'))
        // Second call resolves to simulate normal periodic cleanup
        .mockImplementationOnce(async () => {
          this.periodicInvocations++;
        });

      // Define a method and apply decorator programmatically
      public async doWork(): Promise<void> {
        return;
      }
    }

    // Apply decorator programmatically
    const descriptor = Object.getOwnPropertyDescriptor(
      DecoratedManager.prototype,
      'doWork'
    )!;
    const patched = autoCleanup(
      DecoratedManager.prototype as any,
      'doWork',
      descriptor
    );
    Object.defineProperty(DecoratedManager.prototype, 'doWork', patched);

    const mgr = new DecoratedManager();

    // 1) First invocation schedules timer that will reject → exercises catch arrow fn
    await mgr.doWork();
    jest.advanceTimersByTime(1000);

    // 2) Second invocation schedules timer that will resolve → normal path
    await mgr.doWork();
    jest.advanceTimersByTime(1000);

    // Verify both attempts happened and one success incremented counter
    expect(mgr.triggerPeriodicCleanup).toHaveBeenCalledTimes(2);
    expect(mgr.periodicInvocations).toBe(1);
  });

  it('covers getResourceMetrics non-test path via overriding _isTestMode', () => {
    const mgr = new TestManager();

    // Register a shared resource to impact counts
    const { releaseRef } = (mgr as any).createSharedResource(
      () => ({ v: 1 }),
      () => {},
      'metrics-resource'
    );

    // Force _isTestMode to false for the next call
    mgr.setTestModeFalseOnce();

    const spyProc = jest.spyOn(process, 'memoryUsage').mockReturnValue({
      rss: 0,
      heapTotal: 0,
      heapUsed: 10 * 1024 * 1024, // 10MB
      external: 0,
      arrayBuffers: 0 as any
    } as NodeJS.MemoryUsage);

    const metrics = mgr.getResourceMetrics();
    // In non-test path, memoryUsageMB should equal heapUsedMB (≈10)
    expect(metrics.memoryUsageMB).toBeCloseTo(10, 1);

    releaseRef();
    spyProc.mockRestore();
  });
});

