/** Additional MSRM tests to close specific uncovered lines: 358, 383, 660-678, 737, 853, 175, 236, 304 */
import { MemorySafeResourceManager } from '../MemorySafeResourceManager';

class M extends MemorySafeResourceManager {
  protected async doInitialize(): Promise<void> {}
  protected async doShutdown(): Promise<void> {}
}

describe('MSRM – Final branch closures', () => {
  beforeEach(() => {
    jest.useFakeTimers();
    process.env.NODE_ENV = 'test';
    (global as any).JEST_WORKER_ID = '1';
  });
  afterEach(() => jest.useRealTimers());

  it('createSharedResource enforces limits in production and emits error on factory failure (358, 383)', () => {
    delete process.env.JEST_WORKER_ID;
    process.env.NODE_ENV = 'production';

  });

  it('cleanup function body executed via exit listener (661-662)', async () => {
    delete process.env.JEST_WORKER_ID;
    process.env.NODE_ENV = 'production';
    const m = new M();
    // Ensure handlers registered
    (m as any)._registerGlobalCleanup();
    const exitListeners = process.listeners('exit');
    const cleanup = exitListeners[exitListeners.length - 1] as any;
    await cleanup();
  });

  it('complete production createSharedResource paths in one test', () => {
    process.env.NODE_ENV = 'production';
    const m = new M();
    let emitted: any = null;
    m.on('error', (e) => (emitted = e));

    // First, hit enforcement path by calling createSharedResource
    const r = (m as any).createSharedResource(() => ({ z: 1 }), () => {}, 'Z');
    expect(r.resource.z).toBe(1);

    // Now, factory failure should emit error and throw
    expect(() => (m as any).createSharedResource(() => { throw new Error('prod-factory-fail'); }, () => {}, 'Z2')).toThrow('prod-factory-fail');
    expect(emitted).toBeInstanceOf(Error);
  });

  it('SIGTERM handler executes cleanup and calls process.exit (676-678)', async () => {
    delete process.env.JEST_WORKER_ID;
    process.env.NODE_ENV = 'production';
    const m = new M();
    (m as any)._registerGlobalCleanup();

    const exitSpy = jest.spyOn(process, 'exit').mockImplementation((() => undefined) as any);
    const handlers = process.listeners('SIGTERM');
    // Find the handler that logs "Received SIGTERM"
    const target = handlers.find((fn: any) => typeof fn === 'function' && fn.toString().includes('Received SIGTERM')) as any;
    const toInvoke = (target || handlers[handlers.length - 1]) as any;
    await toInvoke();
    expect(exitSpy).toHaveBeenCalledWith(0);
    exitSpy.mockRestore();
  });

  it('createSafeInterval throws when setInterval returns undefined (236)', () => {
    jest.useRealTimers();
    const m = new M();
    const spy = jest.spyOn(global, 'setInterval' as any).mockReturnValue(undefined as any);
    expect(() => (m as any).createSafeInterval(() => {}, 1, 'badI')).toThrow(/Failed to create interval/);
    spy.mockRestore();
    jest.useFakeTimers();
  });

  it('createSafeTimeout throws when setTimeout returns undefined (304)', () => {
    jest.useRealTimers();
    const m = new M();
    const spy = jest.spyOn(global, 'setTimeout' as any).mockReturnValue(undefined as any);
    expect(() => (m as any).createSafeTimeout(() => {}, 1, 'badT')).toThrow(/Failed to create timeout/);
    spy.mockRestore();
    jest.useFakeTimers();
  });


  it('registerGlobalCleanup registers handlers in production (660-678)', () => {
    delete process.env.JEST_WORKER_ID;
    process.env.NODE_ENV = 'production';

    const m = new M();
    // Invoke private registration directly to cover lines
    (m as any)._registerGlobalCleanup();

    const exitListeners = process.listeners('exit');
    expect(exitListeners.length).toBeGreaterThan(0);
  });

  it('emergency cleanup synchronous error catch (737)', () => {
    const m = new M();
    (m as any)._resources.set('bad', {
      id: 'bad',
      type: 'cache',
      resource: {},
      createdAt: new Date(),
      lastAccessed: new Date(),
      referenceCount: 0,
      cleanupHandler: () => { throw new Error('sync-cleanup-fail'); }
    });
    (m as any)._performEmergencyCleanup();
    expect((m as any)._resources.size).toBe(0);
  });

  it('shutdown error branch rethrows (853) with error event emitted', async () => {
    class Bad extends M { protected async doShutdown(): Promise<void> { throw new Error('fail-shutdown'); } }
    const b = new Bad();
    let emitted: any = null;
    b.on('error', (e) => (emitted = e));
    await expect(b.shutdown()).rejects.toThrow('fail-shutdown');
    expect(emitted).toBeInstanceOf(Error);
  });

  it('initialize error branch throws at 175 with error event emitted', async () => {
    class BadInit extends M { protected async doInitialize(): Promise<void> { throw new Error('fail-init'); } }
    const m = new BadInit();
    let emitted: any = null;
    m.on('error', (e) => (emitted = e));
    await expect((m as any).initialize()).rejects.toThrow('fail-init');
    expect(emitted).toBeInstanceOf(Error);
  });

  it('createSafeInterval and createSafeTimeout execute creation checks (236, 304)', () => {
    const m = new M();
    const idI = (m as any).createSafeInterval(() => {}, 1, 'checkI');
    const idT = (m as any).createSafeTimeout(() => {}, 1, 'checkT');
    expect([idI, idT].every(Boolean)).toBe(true);
  });
});

