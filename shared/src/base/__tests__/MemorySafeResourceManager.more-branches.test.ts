/**
 * MemorySafeResourceManager – Remaining Branch Splits
 */

import { MemorySafeResourceManager } from '../MemorySafeResourceManager';

describe('MemorySafeResourceManager – Remaining Branches', () => {
  class TestMgr extends MemorySafeResourceManager {
    protected async doInitialize(): Promise<void> {}
    protected async doShutdown(): Promise<void> {}
  }

  afterEach(() => jest.restoreAllMocks());

  it('splits instanceof Error branches for interval/timeout callbacks', async () => {
    const mgr = new TestMgr({} as any);
    await mgr.initialize();

    // Attach error listener to ensure emit executes in test env when throwing
    const errors: any[] = [];
    mgr.on('error', (e) => errors.push(e));

    jest.useFakeTimers();

    // Interval that throws Error (true branch)
    const id1 = (mgr as any).createSafeInterval(() => { throw new Error('err'); }, 10, 't-err');
    jest.advanceTimersByTime(10);

    // Timeout that throws non-Error (false branch), ensure emit still fires
    const id2 = (mgr as any).createSafeTimeout(() => { throw 'oops'; }, 10, 't-noerr');
    jest.advanceTimersByTime(10);

    expect(errors.length).toBeGreaterThanOrEqual(1);

    await mgr.shutdown();
    expect([id1, id2].every(Boolean)).toBe(true);
  });

  it('tests getResourceMetrics test-mode vs production calculation', async () => {
    const mgr = new TestMgr({} as any);
    await mgr.initialize();

    const oldEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'test';
    const m1 = mgr.getResourceMetrics();

    process.env.NODE_ENV = 'production';
    const m2 = mgr.getResourceMetrics();

    // Values will differ by test-mode calculation; we only assert shape and number types
    expect(typeof m1.memoryUsageMB).toBe('number');
    expect(typeof m2.memoryUsageMB).toBe('number');

    process.env.NODE_ENV = oldEnv;
    await mgr.shutdown();
  });
});

