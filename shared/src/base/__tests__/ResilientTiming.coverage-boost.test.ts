/**
 * ResilientTiming – Coverage Boost (Statements/Lines +3.75, Branches +2.77)
 * Focus: fallbacks, jest test-mode paths, assertion helpers
 */

import { ResilientTimer, measureAsync, measureSync, assertPerformance, createPerformanceExpectation } from '../utils/ResilientTiming';

describe('ResilientTiming – Coverage Boost', () => {
  it('uses Date.now() paths in Jest and returns reliable timing with non-zero duration', () => {
    const t = new ResilientTimer({ maxExpectedDuration: 1000, estimateBaseline: 5 });
    const { result, timing } = t.measureSync(() => 42);

    expect(result).toBe(42);
    expect(timing.duration).toBeGreaterThan(0);
    expect(['date', 'performance', 'process', 'estimate']).toContain(timing.method);
  });

  it('measureAsync and measureSync capture timing and propagate errors with end() called in catch', async () => {
    const t = new ResilientTimer({ maxExpectedDuration: 1000, estimateBaseline: 5 });
    await expect(t.measure(async () => { throw new Error('boom'); })).rejects.toThrow('boom');

    expect(() => t.measureSync(() => { throw new Error('sync'); })).toThrow('sync');
  });

  it('assertPerformance skips when unreliable and warns; passes/fails appropriately for reliable', () => {
    const t = new ResilientTimer({ maxExpectedDuration: 1000, estimateBaseline: 5 });
    const { timing } = t.measureSync(() => 1);

    // Force unreliable via oversize duration using private validator via crafted timing object
    const unreliable: any = { ...timing, reliable: false, method: 'estimate' };
    expect(assertPerformance(unreliable, 1, { skipIfUnreliable: true, logWarnings: true })).toBe(true);

    const pass = assertPerformance(timing, timing.duration + 10, { logWarnings: true });
    const fail = assertPerformance({ ...timing, reliable: true, duration: timing.duration + 1000 }, timing.duration + 10, { logWarnings: true });
    expect(pass).toBe(true);
    expect(fail).toBe(false);
  });

  it('createPerformanceExpectation handles unreliable and reliable paths', () => {
    const t = new ResilientTimer({ maxExpectedDuration: 1000, estimateBaseline: 5 });
    const { timing } = t.measureSync(() => 1);

    const expRel = createPerformanceExpectation(timing);
    expect(expRel.toBeLessThan(timing.duration + 5)).toBe(true);
    expect(expRel.toBeGreaterThan(0)).toBe(true);
    expect(expRel.toBeReasonable()).toBe(true);

    const unreliable = { ...timing, reliable: false, fallbackUsed: true } as any;
    const expUnrel = createPerformanceExpectation(unreliable);
    expect(expUnrel.toBeLessThan(timing.duration + 5)).toBe(true);
    expect(expUnrel.toBeGreaterThan(0)).toBe(true);
    expect(expUnrel.toBeReasonable()).toBe(true);
  });
});

