/**
 * MemorySafeResourceManager – Branches Boost (≥90% branches)
 * Targets uncovered lines: 222, 291, 447, 490, 550, 586, 763, 884
 * Techniques: capacity=0, empty histories, Error vs non-Error injection, defensive getter throws
 */

import { EventEmitter } from 'events';
import { MemorySafeResourceManager } from '../MemorySafeResourceManager';

class TestMSRM extends MemorySafeResourceManager {
  public constructor() {
    super({ maxIntervals: 2, maxTimeouts: 2, cleanupIntervalMs: 1000 });
  }
  // Expose protected helpers for testing via any-cast usage
  public createInterval(cb: () => void, ms: number, name?: string) { return (this as any).createSafeInterval(cb, ms, name); }
  public createTimeout(cb: () => void, ms: number, name?: string) { return (this as any).createSafeTimeout(cb, ms, name); }
  protected async doInitialize(): Promise<void> {}
  protected async doShutdown(): Promise<void> {}
  public touch(id: string) { return (this as any)._updateResourceAccess(id); }
  public enforce(type: string) { return (this as any)._enforceResourceLimits(type); }
  public cleanupSync(id: string) { return (this as any)._cleanupResourceSync(id); }
  public periodic() { return (this as any)._performPeriodicCleanup(); }
}

describe('MemorySafeResourceManager – Branches Boost', () => {
  beforeEach(() => {
    jest.useFakeTimers();
    process.env.NODE_ENV = 'test';
    (global as any).JEST_WORKER_ID = '1';
  });

  it('interval error emits only with listeners in test env (lines 222-229)', () => {
    const msrm = new TestMSRM();

    // No listener: in test env, error should NOT emit
    const id1 = msrm.createInterval(() => { throw new Error('interval-fail'); }, 1, 'i1');
    jest.advanceTimersByTime(2);

    let observed: any = null;
    msrm.on('error', (e) => (observed = e));

    // With listener: error should emit even in test env
    const id2 = msrm.createInterval(() => { throw 'string-error'; }, 1, 'i2'); // non-Error object
    jest.advanceTimersByTime(2);

    expect(observed).toBe('string-error');
    expect([id1, id2].every(Boolean)).toBe(true);
  });

  it('timeout error emits only with listeners in test env and cleans up synchronously (lines 291, 296-298)', () => {
    const msrm = new TestMSRM();
    let captured: any = null;
    msrm.on('error', (e) => (captured = e));

    const id = msrm.createTimeout(() => { throw new Error('timeout-fail'); }, 1, 't1');
    jest.advanceTimersByTime(2);

    expect(captured).toBeInstanceOf(Error);
    // cleanupResourceSync should have removed the resource
    const metrics = msrm.getResourceMetrics();
    expect(metrics.activeTimeouts).toBe(0);
  });

  it('enforceResourceLimits selects correct limit based on type and throws when far over (lines 447, 475-481)', () => {
    const msrm = new TestMSRM();
    // Simulate high load: pre-populate resources to exceed lenient threshold (limit=50 → threshold=100)
    const store: Map<string, any> = (msrm as any)._resources;
    for (let i = 0; i < 100; i++) {
      store.set(`interval_test_${i}`, {
        id: `interval_test_${i}`,
        type: 'interval',
        resource: null,
        createdAt: new Date(),
        lastAccessed: new Date(),
        referenceCount: 0,
        cleanupHandler: () => {}
      });
    }
    expect(() => msrm.enforce('interval')).toThrow(/Resource limit exceeded/);
  });

  it('releaseSharedResource early-return branch when resource missing (around line ~490)', () => {
    const msrm = new TestMSRM();
    // Should not throw when missing
    (msrm as any)._releaseSharedResource('missing');
  });

  it('setupAutomaticCleanup respects isShuttingDown and test env (lines 550, 556-558)', () => {
    const msrm = new TestMSRM();
    (msrm as any)._isShuttingDown = true;
    // Should early return when shutting down
    (msrm as any)._setupAutomaticCleanup();

    (msrm as any)._isShuttingDown = false;
    process.env.NODE_ENV = 'test';
    // Should skip in test env
    (msrm as any)._setupAutomaticCleanup();
  });

  it('performPeriodicCleanup respects isShuttingDown and cleans unused (line 586)', async () => {
    const msrm = new TestMSRM();
    (msrm as any)._isShuttingDown = true;
    await (msrm as any)._performPeriodicCleanup(); // early return branch

    (msrm as any)._isShuttingDown = false;
    // Create an old, unreferenced resource
    const id = msrm.createInterval(() => {}, 1, 'old');
    // Mark as old and unreferenced
    const res = (msrm as any)._resources.get(id);
    res.lastAccessed = new Date(Date.now() - (31 * 60 * 1000));
    res.referenceCount = 0;

    await (msrm as any)._performPeriodicCleanup();
    expect(msrm.getResourceMetrics().activeIntervals).toBe(0);
  });

  it('getResourceMetrics uses _isTestMode path and scaling (lines 763-777) and _isTestMode detects Jest (line 884)', () => {
    const msrm = new TestMSRM();
    // Add some resources to affect scaling in test mode
    msrm.createInterval(() => {}, 1, 'm1');
    msrm.createTimeout(() => {}, 1, 'm2');
    const metrics = msrm.getResourceMetrics();
    expect(metrics.memoryUsageMB).toBeLessThanOrEqual(0.9);
    expect(metrics.totalResources).toBeGreaterThanOrEqual(2);
  });

  it('defensive read via throwing getter for _limits to exercise error handling around timers (proxy-based)', () => {
    const msrm = new TestMSRM();
    // Proxy that throws when reading cleanupIntervalMs
    const originalLimits = (msrm as any)._limits;
    const proxied = new Proxy(originalLimits, {
      get(target, prop) {
        if (prop === 'cleanupIntervalMs') {
          throw new Error('getter-failure');
        }
        return (target as any)[prop];
      }
    });
    (msrm as any)._limits = proxied;

    // Call setup; should not throw due to try/catch-less code path but simply not schedule
    process.env.NODE_ENV = 'production';
    (msrm as any)._setupAutomaticCleanup();
    // Restore
    (msrm as any)._limits = originalLimits;
  });
});

