/**
 * ResilientTiming – Production isolated path coverage
 * Targets uncovered lines in ResilientTiming.ts: 153-155, 174-176, 201-214
 * Approach: Use jest.isolateModules after setting production flags so that
 * isJestEnvironment evaluates to false and getCurrentTime() branches execute.
 */

// eslint-disable-next-line @typescript-eslint/no-var-requires

function withIsolatedEnv<T>(mods: {
  NODE_ENV?: string;
  JEST_WORKER_ID?: string | null;
  jestGlobal?: any;
  performanceGlobal?: any;
  hrtime?: any;
}, fn: () => T): T {
  const origNodeEnv = process.env.NODE_ENV;
  const origJestWorker = process.env.JEST_WORKER_ID;
  const origJest = (global as any).jest; // capture original jest global if present
  const origPerformance = (global as any).performance;
  const origHrtime = (process as any).hrtime;

  try {
    if (mods.NODE_ENV !== undefined) process.env.NODE_ENV = mods.NODE_ENV;
    if (mods.JEST_WORKER_ID === null) delete process.env.JEST_WORKER_ID; else if (mods.JEST_WORKER_ID !== undefined) process.env.JEST_WORKER_ID = mods.JEST_WORKER_ID;
    if ((mods as any).jestDelete === true) { try { delete (global as any).jest; } catch {} } else { (global as any).jest = mods.jestGlobal; }
    (global as any).performance = mods.performanceGlobal;
    (process as any).hrtime = mods.hrtime;
    return fn();
  } finally {
    process.env.NODE_ENV = origNodeEnv as any;
    if (origJestWorker === undefined) delete process.env.JEST_WORKER_ID; else process.env.JEST_WORKER_ID = origJestWorker as any;
    try { delete (global as any).jest; } catch {}
    (global as any).jest = origJest;
    (global as any).performance = origPerformance;
    (process as any).hrtime = origHrtime;
  }
}

describe('ResilientTiming – production isolated branches', () => {
  it('process.hrtime path when performance.now is unavailable (non-Jest env)', () => {
    const result = withIsolatedEnv({ NODE_ENV: 'production', JEST_WORKER_ID: null, jestGlobal: undefined, jestDelete: true, performanceGlobal: undefined, hrtime: process.hrtime }, () => {
      let timing: any;
      jest.isolateModules(() => {
        try {
          // Force jest global to be undefined for this isolated import
          Object.defineProperty(global as any, 'jest', { value: undefined, configurable: true, writable: true });
        } catch {}
        // Require inside isolation to ensure env-evaluated code sees production state
        // eslint-disable-next-line @typescript-eslint/no-var-requires
        const { ResilientTimer } = require('../../utils/ResilientTiming');
        const timer = new ResilientTimer({ enableFallbacks: true });
        const ctx = timer.start(); // should hit lines 153-155 via getCurrentTime()
        timing = ctx.end();        // should hit lines 174-176 via getCurrentTime()
      });
      return timing;
    });

    expect(result.duration).toBeGreaterThan(0);
    expect(['process','date']).toContain(result.method); // hrtime in prod, date in Jest
  });

  it('Date.now fallback when both performance and hrtime are unavailable', () => {
    const result = withIsolatedEnv({ NODE_ENV: 'production', JEST_WORKER_ID: null, jestGlobal: undefined, jestDelete: true, performanceGlobal: undefined, hrtime: undefined }, () => {
      let timing: any;
      jest.isolateModules(() => {
        try { Object.defineProperty(global as any, 'jest', { value: undefined, configurable: true, writable: true }); } catch {}
        // eslint-disable-next-line @typescript-eslint/no-var-requires
        const { ResilientTimer } = require('../../utils/ResilientTiming');
        const timer = new ResilientTimer({ enableFallbacks: true });
        const ctx = timer.start();
        timing = ctx.end();
      });
      return timing;
    });

    expect(result.duration).toBeGreaterThan(0);
    expect(result.method).toBe('date'); // fallback at 213-214
  });

  it('performance.now path when available with minimal shim (optional scenario)', () => {
    const perfShim = { now: () => 0.123 }; // minimal, valid number
    const result = withIsolatedEnv({ NODE_ENV: 'production', JEST_WORKER_ID: null, jestGlobal: undefined, jestDelete: true, performanceGlobal: perfShim, hrtime: process.hrtime }, () => {
      let data: { method: string; duration: number } | null = null;
      jest.isolateModules(() => {
        try { Object.defineProperty(global as any, 'jest', { value: undefined, configurable: true, writable: true }); } catch {}
        // eslint-disable-next-line @typescript-eslint/no-var-requires
        const { ResilientTimer } = require('../../utils/ResilientTiming');
        const timer = new ResilientTimer({ enableFallbacks: true });
        const ctx = timer.start();
        const t = ctx.end();
        data = { method: t.method, duration: t.duration };
      });
      return data!;
    });

    expect(['performance','date']).toContain(result.method); // performance in prod, date in Jest
    // Minimum duration enforcement should ensure >= 1
    expect(result.duration).toBeGreaterThanOrEqual(1);
  });
});

