/**
 * Targeted production-path coverage for ResilientTiming
 * Goals: Cover non-Jest environment branches in ResilientTimingContext:
 *  - Lines 153-155: start() non-Jest branch using getCurrentTime()
 *  - Lines 174-176: end() non-Jest branch using getCurrentTime()
 *  - Lines 201-214: getCurrentTime() hrtime path and Date.now() fallback
 * Enterprise value: Validates production timing method selection logic
 */

import { ResilientTimer, ResilientTimingContext } from '../../utils/ResilientTiming';

// Helper to temporarily modify global/env and restore afterwards
function withEnv<T>(mods: {
  NODE_ENV?: string;
  JEST_WORKER_ID?: string | null;
  jestGlobal?: any;
  performanceGlobal?: any;
  hrtime?: any;
}, fn: () => T): T {
  const origNodeEnv = process.env.NODE_ENV;
  const origJestWorker = process.env.JEST_WORKER_ID;
  const origJest = (global as any).jest;
  const origPerformance = (global as any).performance;
  const origHrtime = (process as any).hrtime;

  try {
    if (mods.NODE_ENV !== undefined) process.env.NODE_ENV = mods.NODE_ENV;
    if (mods.JEST_WORKER_ID === null) delete process.env.JEST_WORKER_ID; else if (mods.JEST_WORKER_ID !== undefined) process.env.JEST_WORKER_ID = mods.JEST_WORKER_ID;
    (global as any).jest = mods.jestGlobal;
    (global as any).performance = mods.performanceGlobal;
    (process as any).hrtime = mods.hrtime;
    return fn();
  } finally {
    process.env.NODE_ENV = origNodeEnv as any;
    if (origJestWorker === undefined) delete process.env.JEST_WORKER_ID; else process.env.JEST_WORKER_ID = origJestWorker;
    (global as any).jest = origJest;
    (global as any).performance = origPerformance;
    (process as any).hrtime = origHrtime;
  }
}

describe('ResilientTiming - production-path coverage', () => {
  it('uses process.hrtime() path when performance is unavailable in non-Jest env', () => {
    const result = withEnv({ NODE_ENV: 'production', JEST_WORKER_ID: null, jestGlobal: undefined, performanceGlobal: undefined, hrtime: process.hrtime }, () => {
      const timer = new ResilientTimer({ enableFallbacks: true });
      const ctx = timer.start(); // should use getCurrentTime() -> hrtime
      const timing = ctx.end();  // should use getCurrentTime() -> hrtime
      return timing;
    });

    expect(result.duration).toBeGreaterThan(0);
    expect(result.method === 'process' || result.method === 'date').toBe(true); // On some Node versions, hrtime may map to 'process'
  });

  it('falls back to Date.now() when both performance and hrtime are unavailable', () => {
    const result = withEnv({ NODE_ENV: 'production', JEST_WORKER_ID: null, jestGlobal: undefined, performanceGlobal: undefined, hrtime: undefined }, () => {
      const timer = new ResilientTimer({ enableFallbacks: true });
      const ctx = timer.start(); // getCurrentTime() -> Date.now()
      const timing = ctx.end();  // getCurrentTime() -> Date.now()
      return timing;
    });

    expect(result.duration).toBeGreaterThan(0);
    expect(result.method).toBe('date');
  });
});

