/**
 * ResilientTiming – Direct getCurrentTime coverage
 * Purpose: Hit lines 201-214 (hrtime path and Date.now fallback) regardless of Jest env.
 */

import { ResilientTimingContext, ResilientTimer } from '../../utils/ResilientTiming';

function withGlobals<T>(mods: { performanceGlobal?: any; hrtime?: any }, fn: () => T): T {
  const origPerformance = (global as any).performance;
  const origHrtime = (process as any).hrtime;
  try {
    (global as any).performance = mods.performanceGlobal;
    (process as any).hrtime = mods.hrtime;
    return fn();
  } finally {
    (global as any).performance = origPerformance;
    (process as any).hrtime = origHrtime;
  }
}

describe('ResilientTiming.getCurrentTime – direct private access', () => {
  it('covers hrtime path (201-206) when performance is unavailable', () => {
    const ctx = new ResilientTimer({ enableFallbacks: true }).start() as unknown as ResilientTimingContext;
    const hr = () => [Math.floor(Date.now() / 1000), (Date.now() % 1000) * 1e6] as [number, number];
    const result = withGlobals({ performanceGlobal: undefined, hrtime: hr }, () => (ctx as any).getCurrentTime());
    expect(result.method).toBe('process');
    expect(typeof result.time).toBe('number');
  });

  it('covers Date.now fallback (213-214) when both perf and hrtime are unavailable', () => {
    const ctx = new ResilientTimer({ enableFallbacks: true }).start() as unknown as ResilientTimingContext;
    const result = withGlobals({ performanceGlobal: undefined, hrtime: undefined }, () => (ctx as any).getCurrentTime());
    expect(result.method).toBe('date');
    expect(result.time).toBeGreaterThan(0);
  });

  it('covers performance.now path (189-195) when available', () => {
    const ctx = new ResilientTimer({ enableFallbacks: true }).start() as unknown as ResilientTimingContext;
    const perfShim = { now: () => 123.456 };
    const result = withGlobals({ performanceGlobal: perfShim, hrtime: undefined }, () => (ctx as any).getCurrentTime());
    expect(result.method).toBe('performance');
    expect(result.time).toBe(123.456);
  });
});

