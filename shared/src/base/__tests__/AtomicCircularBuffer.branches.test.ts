/**
 * @file AtomicCircularBuffer Branch Coverage - Surgical Precision
 * @description Targets uncovered lines/branches: 203-204, 420, 431-495, 507-528
 */

import { AtomicCircularBuffer } from '../AtomicCircularBuffer';

describe('AtomicCircularBuffer - Branch Coverage (Surgical Precision)', () => {
  const saveEnv = () => ({ NODE_ENV: process.env.NODE_ENV, JEST_WORKER_ID: process.env.JEST_WORKER_ID });
  const restoreEnv = (env: { NODE_ENV?: string; JEST_WORKER_ID?: string }) => {
    if (typeof env.NODE_ENV === 'undefined') delete (process.env as any).NODE_ENV; else process.env.NODE_ENV = env.NODE_ENV;
    if (typeof env.JEST_WORKER_ID === 'undefined') delete (process.env as any).JEST_WORKER_ID; else process.env.JEST_WORKER_ID = env.JEST_WORKER_ID as any;
  };

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('should create validation interval in production during initialize (lines 203-204)', async () => {
    const env = saveEnv();
    try {
      process.env.NODE_ENV = 'production';
      delete (process.env as any).JEST_WORKER_ID; // ensure not detected as test

      const buffer = new AtomicCircularBuffer<string>(5);

      // Spy on createSafeInterval to avoid real timer and verify call
      const spy = jest
        .spyOn(buffer as any, 'createSafeInterval')
        .mockImplementation((cb: any, interval: any, name: any) => {
          // Execute callback once to cover line 204
          try { cb(); } catch { /* ignore */ }
          return 1 as any;
        });

      await buffer.initialize();

      expect(spy).toHaveBeenCalledWith(expect.any(Function), 60000, 'sync-validation');

      spy.mockRestore();
      await buffer.shutdown();
    } finally {
      restoreEnv(env);
    }
  });

  it('should throw operation lock timeout in test env (line 420)', async () => {
    const buffer = new AtomicCircularBuffer<string>(2);

    // Ensure test environment branch in _withLock
    process.env.NODE_ENV = 'test';
    process.env.JEST_WORKER_ID = process.env.JEST_WORKER_ID || '1';

    // Force lock to stay engaged to hit timeout throw after 1000 attempts
    (buffer as any)._operationLock = true;

    await expect(buffer.addItem('k', 'v')).rejects.toThrow('Operation lock timeout');

    // Cleanup state
    (buffer as any)._operationLock = false;
    await buffer.shutdown();
  });

  it('should execute production path of _withLock (lines 431-441)', async () => {
    const env = saveEnv();
    try {
      process.env.NODE_ENV = 'production';
      delete (process.env as any).JEST_WORKER_ID;

      const buffer = new AtomicCircularBuffer<string>(2);
      await buffer.initialize();

      // No lock held, should pass through production while-loop branch quickly
      await expect(buffer.addItem('a', 'b')).resolves.not.toThrow();

      await buffer.shutdown();
    } finally {
      restoreEnv(env);
    }
  });

  it('should perform emergency resync on size mismatch in production (lines 458-476, 521-528) and throw on immediate sync mismatch (507-509)', async () => {
    const env = saveEnv();
    try {
      process.env.NODE_ENV = 'production';
      delete (process.env as any).JEST_WORKER_ID;

      const buffer = new AtomicCircularBuffer<string>(5);
      await buffer.initialize();

      // Create size mismatch: items has entry, insertionOrder empty
      (buffer as any)._items.set('x', '1');
      (buffer as any)._insertionOrder = [];

      // Immediate validation should throw (lines 507-509)
      expect(() => (buffer as any)._validateSyncImmediate()).toThrow(/Immediate sync validation failed/);

      // Production validation should detect mismatch and resync (lines 458-476, 521-528)
      (buffer as any)._validateSynchronization();
      expect((buffer as any)._insertionOrder.length).toBe((buffer as any)._items.size);

      await buffer.shutdown();
    } finally {
      restoreEnv(env);
    }
  });

  it('should expose logging wrappers without throwing (function coverage boost)', async () => {
    const buffer = new AtomicCircularBuffer<string>(2);
    await buffer.initialize();
    buffer.logInfo('info', { k: 'v' });
    buffer.logWarning('warn');
    buffer.logDebug('debug', { a: 1 });
    buffer.logError('error', new Error('e1'), { code: 500 });
    buffer.logError('error-nonerror', 'string-error');
    await buffer.shutdown();
  });


  it('should exercise production wait loop in _withLock (line 432)', async () => {
    const env = saveEnv();
    try {
      process.env.NODE_ENV = 'production';
      delete (process.env as any).JEST_WORKER_ID;

      const buffer = new AtomicCircularBuffer<string>(2);

      // Hold the lock first so while-loop executes at least once
      (buffer as any)._operationLock = true;

      // Mock setTimeout used inside _withLock to immediately resolve and release lock
      const timeoutSpy = jest
        .spyOn(global as any, 'setTimeout')
        .mockImplementation((cb: any, ms?: any) => {
          // Release lock before resolving awaited timer
          (buffer as any)._operationLock = false;
          try { cb(); } catch {}
          return 1 as any;
        });

      const p = buffer.addItem('loop', 'v');

      await expect(p).resolves.toBeUndefined();
      timeoutSpy.mockRestore();
      await buffer.shutdown();
    } finally {
      restoreEnv(env);
    }
  });

  it('should execute test-env skip branch in _validateSynchronization (lines 454-455)', async () => {
    const env = saveEnv();
    try {
      process.env.NODE_ENV = 'test';
      process.env.JEST_WORKER_ID = process.env.JEST_WORKER_ID || '1';

      const buffer = new AtomicCircularBuffer<string>(2);
      const before = buffer.getMetrics().syncValidations;

      // Invoke private method directly
      (buffer as any)._validateSynchronization();

      const after = buffer.getMetrics().syncValidations;
      expect(after).toBe(before + 1);

      await buffer.shutdown();
    } finally {
      restoreEnv(env);
    }
  });

  it('should perform emergency resync on key integrity mismatch with equal sizes (lines 478-495, 521-528)', async () => {
    const env = saveEnv();
    try {
      process.env.NODE_ENV = 'production';
      delete (process.env as any).JEST_WORKER_ID;

      const buffer = new AtomicCircularBuffer<string>(5);
      await buffer.initialize();

      // Equal sizes but different keys
      (buffer as any)._items.set('a', '1');
      (buffer as any)._insertionOrder = ['b'];

      (buffer as any)._validateSynchronization();

      // After resync, insertionOrder should be rebuilt from items keys
      const keys = Array.from((buffer as any)._items.keys());
      expect((buffer as any)._insertionOrder.sort()).toEqual(keys.sort());

      await buffer.shutdown();
    } finally {
      restoreEnv(env);
    }
  });
});

