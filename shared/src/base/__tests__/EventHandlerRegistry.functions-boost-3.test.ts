/**
 * EventHandlerRegistry – Functions ≥95% Boost (Part 3)
 * Covers remaining wrapper and early-return paths to push function coverage.
 */

import { EventHandlerRegistry } from '../EventHandlerRegistry';

describe('EventHandlerRegistry – Functions Boost Part 3', () => {
  it('unregisterClientHandlers early return for clients without handlers (lines 368-369)', () => {
    const reg = new EventHandlerRegistry({} as any);
    const removed = reg.unregisterClientHandlers('no-client');
    expect(removed).toBe(0);
  });

  it('logging wrapper methods execute: logInfo, logWarning, logDebug', () => {
    const reg = new EventHandlerRegistry({} as any);
    reg.logInfo('info message', { a: 1 });
    reg.logWarning('warn message', { b: 2 });
    reg.logDebug('debug message', { c: 3 });
  });
});

