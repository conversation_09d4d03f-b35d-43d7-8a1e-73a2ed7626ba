/**
 * TimerCoordinationService – Additional Coverage Tests
 * Targets uncovered lines: 166, 237, 305, 326, 359, 368, 381, 407-411, 524
 */

import { TimerCoordinationService } from '../TimerCoordinationService';

describe('TimerCoordinationService – Additional Coverage', () => {
  const realEnv = { ...process.env } as NodeJS.ProcessEnv;
  const realGc = (global as any).gc;
  let warnSpy: jest.SpyInstance;
  let errorSpy: jest.SpyInstance;
  let infoSpy: jest.SpyInstance;
  let debugSpy: jest.SpyInstance;

  beforeEach(() => {
    jest.restoreAllMocks();
    process.env = { ...realEnv };
    (global as any).gc = undefined;
    warnSpy = jest.spyOn(console, 'warn').mockImplementation(() => {});
    errorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    infoSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
    debugSpy = jest.spyOn(console, 'debug').mockImplementation(() => {});
    TimerCoordinationService.resetInstance();
  });

  afterEach(() => {
    jest.useRealTimers();
    TimerCoordinationService.resetInstance();
    process.env = { ...realEnv };
    (global as any).gc = realGc;
    jest.restoreAllMocks();
  });

  it('resetInstance logs warning when emergencyCleanup throws (line ~166)', () => {
    const svc = TimerCoordinationService.getInstance();
    jest.spyOn(svc, 'emergencyCleanup').mockImplementation(() => {
      throw new Error('cleanup-fail');
    });

    TimerCoordinationService.resetInstance();

    expect(warnSpy).toHaveBeenCalledWith('Reset cleanup error:', expect.any(Error));
  });

  it('direct interval callback error is caught and logged (line ~237)', () => {
    jest.useFakeTimers();
    process.env.NODE_ENV = 'test';
    const svc = TimerCoordinationService.getInstance();

    const id = svc.createCoordinatedInterval(() => {
      throw new Error('timer-callback-fail');
    }, 5, 'svcA', 't1');

    // Advance to trigger direct interval execution
    jest.advanceTimersByTime(1000);

    expect(errorSpy).toHaveBeenCalled();

    // cleanup
    svc.removeCoordinatedTimer(id);
  });

  it('clearServiceTimers catches removal errors and warns (line ~305)', () => {
    process.env.NODE_ENV = 'test';
    const svc = TimerCoordinationService.getInstance();

    const id = svc.createCoordinatedInterval(() => {}, 5, 'svcB', 't1');

    jest.spyOn(svc, 'removeCoordinatedTimer').mockImplementation(() => {
      throw new Error('remove-fail');
    });

    svc.clearServiceTimers('svcB');

    expect(warnSpy).toHaveBeenCalledWith(
      expect.stringContaining('Failed to remove timer during cleanup'),
      expect.objectContaining({ compositeId: expect.any(String), error: expect.any(Error) })
    );

    // cleanup underlying map in case
    jest.restoreAllMocks();
    svc.removeCoordinatedTimer(id);
  });

  it('clearAllTimers triggers a single gc cycle when available (line ~326)', () => {
    const gcSpy = jest.fn();
    (global as any).gc = gcSpy;
    const svc = TimerCoordinationService.getInstance();

    // add some internal state
    svc.createCoordinatedInterval(() => {}, 5, 'svcC', 't1');

    svc.clearAllTimers();

    expect(gcSpy).toHaveBeenCalledTimes(1);
  });

  it('emergencyCleanup triggers multiple gc cycles (line ~359) and clears direct intervals', () => {
    const gcSpy = jest.fn();
    (global as any).gc = gcSpy;
    process.env.NODE_ENV = 'test';
    const svc = TimerCoordinationService.getInstance();

    svc.createCoordinatedInterval(() => {}, 5, 'svcD', 't1');

    svc.emergencyCleanup();

    expect(gcSpy).toHaveBeenCalled();
  });

  it('initialize() calls doInitialize and logs (line ~368)', async () => {
    const svc = TimerCoordinationService.getInstance();
    await svc.initialize();
    expect(infoSpy).toHaveBeenCalledWith(
      expect.stringContaining('TimerCoordinationService initialized successfully'),
      expect.anything()
    );
  });

  it('shutdown clears direct intervals and logs info (line ~381)', async () => {
    process.env.NODE_ENV = 'test';
    const svc = TimerCoordinationService.getInstance();

    svc.createCoordinatedInterval(() => {}, 5, 'svcE', 't1');

    await svc.shutdown();

    expect(infoSpy).toHaveBeenCalledWith(
      expect.stringContaining('Direct interval cleared during shutdown'),
      expect.objectContaining({ id: expect.any(String) })
    );
  });

  it('logging wrapper methods delegate to logger (lines ~407-411)', () => {
    const svc = TimerCoordinationService.getInstance();

    // Error path with non-Error object to exercise defensive formatting downstream
    svc.logError('wrapper-error', { bad: true }, { ctx: 'x' });
    expect(errorSpy).toHaveBeenCalled();

    // Debug gated by environment
    process.env.NODE_ENV = 'development';
    svc.logDebug('wrapper-debug');
    expect(debugSpy).toHaveBeenCalledWith(
      expect.stringContaining('[DEBUG] TimerCoordinationService: wrapper-debug'),
      ''
    );
  });

  it('creates audit interval in non-test environment (line ~524)', () => {
    // Ensure non-test env so audit path is taken for first timer
    process.env.NODE_ENV = 'production';
    const svc = TimerCoordinationService.getInstance();

    const spySafe = jest
      .spyOn<any, any>(svc as any, 'createSafeInterval')
      .mockImplementation(() => {});

    svc.createCoordinatedInterval(() => {}, 50, 'svcF', 't1');

    // Expect audit interval creation call among createSafeInterval calls
    expect(spySafe).toHaveBeenCalledWith(expect.any(Function), expect.any(Number), 'timer-audit');

    spySafe.mockRestore();
  });
});

