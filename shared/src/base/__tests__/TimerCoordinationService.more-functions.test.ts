/**
 * TimerCoordinationService – Additional Function Coverage
 */

import { TimerCoordinationService } from '../TimerCoordinationService';

describe('TimerCoordinationService – More Functions', () => {
  afterEach(() => {
    TimerCoordinationService.resetInstance();
    jest.useRealTimers();
  });

  it('covers ensureInitialized, forceInitializationStatus, forceHealthyStatus, isHealthy, emergencyCleanup', () => {
    const svc = TimerCoordinationService.getInstance({ maxTimersPerService: 5, maxGlobalTimers: 50, minIntervalMs: 5 });

    // ensureInitialized path
    svc.ensureInitialized();

    // forceInitializationStatus path
    svc.forceInitializationStatus(false);
    svc.forceInitializationStatus(true);

    // forceHealthyStatus + isHealthy override
    svc.forceHealthyStatus();
    expect(svc.isHealthy()).toBe(true);

    // Create a direct interval by running in test env path (default)
    jest.useFakeTimers();
    const id = svc.createCoordinatedInterval(() => {}, 5, 'svc', 'id');
    expect(typeof id).toBe('string');

    // emergencyCleanup clears direct and registry
    svc.emergencyCleanup();
    const stats = svc.getTimerStatistics();
    expect(stats.totalTimers).toBe(0);
  });
});

