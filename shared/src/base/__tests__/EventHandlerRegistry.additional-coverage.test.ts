/**
 * EventHandlerRegistry – Additional Coverage Tests
 */

import { EventHandlerRegistry, resetEventHandlerRegistry } from '../EventHandlerRegistry';

describe('EventHandlerRegistry – Additional Coverage', () => {
  let registry: EventHandlerRegistry;

  beforeEach(async () => {
    await resetEventHandlerRegistry();
    registry = EventHandlerRegistry.getInstance({
      handlerTimeoutMs: 1, // to exercise prune quickly
      orphanDetectionIntervalMs: 10
    } as any);
    await registry.initialize();
  });

  afterEach(async () => {
    await registry.shutdown();
    await resetEventHandlerRegistry();
  });

  it('exercises getHandlersForEvent timestamp update and metrics recalculation', () => {
    const id = registry.registerHandler('c1', 'e1', () => {});
    const before = registry.getHandler(id)!;
    const beforeTime = before.lastUsed.getTime();

    const handlers = registry.getHandlersForEvent('e1');
    expect(handlers.length).toBe(1);
    const after = registry.getHandler(id)!;
    expect(after.lastUsed.getTime()).toBeGreaterThanOrEqual(beforeTime);

    const metrics = registry.getMetrics();
    expect(metrics.totalHandlers).toBe(1);
    expect(metrics.handlersByType['e1']).toBe(1);
    expect(metrics.handlersByClient['c1']).toBe(1);
  });

  it('triggers emergency cleanup when global limit exceeded', () => {
    const reg = EventHandlerRegistry.getInstance({ maxGlobalHandlers: 1 } as any);
    const id1 = reg.registerHandler('c1', 'e1', () => {});
    // Exceed should trigger emergency cleanup but still allow registration
    const id2 = reg.registerHandler('c2', 'e2', () => {});

    expect(id1).toBeTruthy();
    expect(id2).toBeTruthy();
  });
});

