/**
 * TimerCoordinationService – Function Coverage Boost
 */

import { TimerCoordinationService, getTimerCoordinator } from '../TimerCoordinationService';

describe('TimerCoordinationService – Function Coverage', () => {
  afterEach(() => {
    TimerCoordinationService.resetInstance();
    jest.useRealTimers();
  });

  it('covers getHealthDetails, clearServiceTimers, and clearAllTimers paths', () => {
    const svc = TimerCoordinationService.getInstance({ maxTimersPerService: 5, maxGlobalTimers: 100, minIntervalMs: 10 });

    // Create two timers for one service and one for another
    jest.useFakeTimers();
    const id1 = svc.createCoordinatedInterval(() => {}, 10, 'svc1', 't1');
    const id2 = svc.createCoordinatedInterval(() => {}, 10, 'svc1', 't2');
    const id3 = svc.createCoordinatedInterval(() => {}, 10, 'svc2', 't3');

    // Touch wrapped callback to update executionCount and lastExecution
    jest.advanceTimersByTime(10);

    // getHealthDetails exercises metrics and isHealthy path
    const details = svc.getHealthDetails();
    expect(details).toBeDefined();

    // clearServiceTimers removes only svc1 timers
    (svc as any).clearServiceTimers('svc1');
    const statsAfterClear = svc.getTimerStatistics();
    expect(statsAfterClear.totalTimers).toBe(1);

    // clearAllTimers clears remaining
    svc.clearAllTimers();
    const statsAfterAllClear = svc.getTimerStatistics();
    expect(statsAfterAllClear.totalTimers).toBe(0);

    // Avoid unused variable warnings
    expect([id1, id2, id3].every(Boolean)).toBe(true);
  });

  it('covers environment gating for direct vs safe interval and audit guard', () => {
    // Force production to exercise audit interval creation guard path
    const oldEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'production';

    const svc = TimerCoordinationService.getInstance({ maxTimersPerService: 5, maxGlobalTimers: 100, minIntervalMs: 10, timerAuditIntervalMs: 1000 });
    const spy = jest.spyOn(svc as any, 'createSafeInterval').mockImplementation((cb: any, ms: number, name: string) => 'id');

    // First timer triggers audit creation attempt; catch block is covered in branches test already
    svc.createCoordinatedInterval(() => {}, 5, 'prod', 'audit');

    expect(spy).toHaveBeenCalled();

    // Restore env
    process.env.NODE_ENV = oldEnv;
  });

  it('covers removeCoordinatedTimer non-existent path and decrement path', () => {
    const svc = getTimerCoordinator();
    // Non-existent removal path
    (svc as any).removeCoordinatedTimer('nope:missing');

    // Create then remove to hit decrement path
    const id = svc.createCoordinatedInterval(() => {}, 10, 'svc', 'to-remove');
    (svc as any).removeCoordinatedTimer(id);

    const stats = svc.getTimerStatistics();
    expect(stats.totalTimers).toBe(0);
  });
});

