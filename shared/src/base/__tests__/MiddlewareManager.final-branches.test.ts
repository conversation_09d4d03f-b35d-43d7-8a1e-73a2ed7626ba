/**
 * MiddlewareManager – Final Branches touch-ups
 * Target remaining branch edges: handler success + afterHandlerExecution success path
 */

import { MiddlewareManager } from '../event-handler-registry/modules/MiddlewareManager';

const makeHandler = (id: string, clientId = 'c1') => ({
  id,
  clientId,
  eventType: 'evt',
  callback: async (data: any) => ({ ok: true, data }),
  registeredAt: new Date(),
  lastUsed: new Date(),
  metadata: {}
});

describe('MiddlewareManager – Final Branches', () => {
  it('after-handler executes successfully when handler succeeded', async () => {
    const mm = new MiddlewareManager({ enableTiming: true });
    await mm.initialize();

    const seen: any[] = [];
    mm.addMiddleware({
      name: 'after-ok',
      priority: 1,
      beforeHandlerExecution: async () => true,
      afterHandlerExecution: async (_ctx, res) => { seen.push(res); }
    } as any);

    const res = await mm.executeHandlerWithMiddleware(makeHandler('h5') as any, { a: 1 }, 'evt');
    expect(res.success).toBe(true);
    expect(seen.length).toBe(1);

    await mm.shutdown();
  });
});

