/**
 * MiddlewareManager – Branches Boost (Quick Win)
 * Goal: Cover short-circuit vs continue paths for beforeHandlerExecution and onHandlerError
 * Business value: Validates middleware gatekeeping and error recovery in realistic flows
 */

import { MiddlewareManager } from '../event-handler-registry/modules/MiddlewareManager';

// Minimal IRegisteredHandler shape for this test
const makeHandler = (id: string, clientId = 'c1') => ({
  id,
  clientId,
  eventType: 'evt',
  callback: async (data: any) => {
    if (data && data.throw) throw new Error('handler-failure');
    return { ok: true, data };
  },
  registeredAt: new Date(),
  lastUsed: new Date(),
  metadata: {}
});

describe('MiddlewareManager – Branches Boost', () => {
  it('short-circuits when beforeHandlerExecution returns false (skip path)', async () => {
    const mm = new MiddlewareManager({ enableTiming: true });
    await mm.initialize();

    mm.addMiddleware({
      name: 'gatekeeper',
      priority: 10,
      beforeHandlerExecution: async () => false // short-circuit branch
    } as any);

    const handler = makeHandler('h1');
    const res = await mm.executeHandlerWithMiddleware(handler as any, { x: 1 }, 'evt');

    expect(res.success).toBe(true); // skipped success
    expect(res.skippedByMiddleware).toBe('gatekeeper');

    await mm.shutdown();
  });

  it('continues when beforeHandlerExecution returns true and recovers handler error via onHandlerError', async () => {
    const mm = new MiddlewareManager({ enableTiming: true });
    await mm.initialize();

    mm.addMiddleware({
      name: 'recovery',
      priority: 5,
      beforeHandlerExecution: async () => true, // continue branch
      onHandlerError: async (_ctx, _err) => true // handle the error → convert to success
    } as any);

    const handler = makeHandler('h2');
    const res = await mm.executeHandlerWithMiddleware(handler as any, { throw: true }, 'evt');

    expect(res.success).toBe(true);
    expect(res.skippedByMiddleware).toBeUndefined();

    await mm.shutdown();
  });

  it('propagates when onHandlerError returns false and no other middleware handles it', async () => {
    const mm = new MiddlewareManager({ enableTiming: true });
    await mm.initialize();

    mm.addMiddleware({
      name: 'observer',
      priority: 1,
      beforeHandlerExecution: async () => true,
      onHandlerError: async () => false // not handled
    } as any);

    const handler = makeHandler('h3');
    const res = await mm.executeHandlerWithMiddleware(handler as any, { throw: true }, 'evt');

    expect(res.success).toBe(false);
    await mm.shutdown();
  });
});

