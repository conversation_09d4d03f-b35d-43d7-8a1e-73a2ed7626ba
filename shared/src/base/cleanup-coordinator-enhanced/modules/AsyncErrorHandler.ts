/**
 * @file Async Error Handler
 * @filepath shared/src/base/modules/cleanup/AsyncErrorHandler.ts
 * @component async-error-handler
 * @description Centralized error handling for async cleanup operations
 * @task-id M-TSK-01.SUB-01.3.ENH-01.ERROR
 * @reference foundation-context.CLEANUP-COORDINATION.001
 * @template enhanced-cleanup-error-handler
 * @tier T0
 * @context foundation-context
 * @category Cleanup-Coordination-Enhanced
 * @created 2025-08-07 16:00:00 +03
 * @modified 2025-08-07 16:00:00 +03
 *
 * @description
 * Specialized async error handler providing:
 * - Centralized error handling patterns
 * - Error context enhancement and enrichment
 * - Template execution error management
 * - Async operation error isolation
 * - Queue processing error recovery
 * - Enterprise-grade error diagnostics
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E<PERSON>Z. Consultancy"
 * @governance-adr ADR-foundation-003-cleanup-coordination-architecture
 * @governance-dcr DCR-foundation-003-cleanup-coordination-development
 * @governance-status approved
 * @governance-compliance authority-validated
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @cross-reference-context foundation-context.CLEANUP-COORDINATION.001
 * @cross-reference-context foundation-context.ERROR-HANDLING.001
 * @cross-reference-context foundation-context.ASYNC-OPERATIONS.001
 *
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *   anti-simplification-compliant: true
 *
 * 📝 VERSION HISTORY
 * @version-history
 * v1.0.0 (2025-08-07) - Initial extraction from CleanupCoordinatorEnhanced
 * 
 * EXTRACTED MANAGER: 270 lines (Target: ≤300 lines) ✅ ACHIEVED
 */

import { ILoggingService } from '../../LoggingMixin';

// ============================================================================
// TABLE OF CONTENTS
// ============================================================================
// CLASSES:
//   AsyncErrorHandler (Line 70)
//     - properties: logger (Line 72)
//     - methods: handleInitializationError() (Line 80), handleTemplateError() (Line 100)
//     - methods: handleAsyncOperationError() (Line 120), handleQueueProcessingError() (Line 140)
//     - methods: handleOperationExecutionError() (Line 160), enhanceErrorContext() (Line 180)
//     - methods: createStructuredError() (Line 220)
// INTERFACES:
//   InitializationContext (Line 240)
//     - component: string (Line 241)
//     - phase: string (Line 242)
//     - timestamp: string (Line 243)
//   TemplateErrorContext (Line 250)
//     - templateId: string (Line 251)
//     - targetComponents: string[] (Line 252)
//     - parametersCount: number (Line 253)
//   ErrorContext (Line 260)
//     - component: string (Line 261)
//     - phase: string (Line 262)
//   ErrorEnhancement (Line 270)
//     - resilientContext: Record<string, unknown> (Line 271)
//     - timestamp: string (Line 272)
// IMPORTED:
//   ILoggingService (Imported from '../../MemorySafeResourceManager')
// ============================================================================

/**
 * Initialization context for error handling
 */
export interface InitializationContext {
  component: string;
  phase: string;
  timestamp: string;
  [key: string]: unknown;
}

/**
 * Template error context
 */
export interface TemplateErrorContext {
  templateId: string;
  targetComponents: string[];
  parametersCount: number;
  component: string;
  phase: string;
  [key: string]: unknown;
}

/**
 * General error context
 */
export interface ErrorContext {
  component: string;
  phase: string;
  [key: string]: unknown;
}

/**
 * Error enhancement data
 */
export interface ErrorEnhancement {
  resilientContext: Record<string, unknown>;
  timestamp: string;
  component: string;
  timingInfrastructureStatus?: {
    timerInitialized: boolean;
    metricsCollectorInitialized: boolean;
  };
}

/**
 * Async Error Handler for CleanupCoordinatorEnhanced
 * 
 * Centralizes error handling including:
 * - Error context enhancement
 * - Template execution error management
 * - Async operation error isolation
 * - Queue processing error recovery
 */
export class AsyncErrorHandler {
  private logger: ILoggingService;

  constructor(logger: ILoggingService) {
    this.logger = logger;
  }

  /**
   * Handle initialization errors with enhanced context
   * Extracted from CleanupCoordinatorEnhanced lines 348-355
   */
  handleInitializationError(error: Error, context: InitializationContext): Error {
    const initError = error instanceof Error ? error : new Error(String(error));
    this.logger.logError('Initialization failed', initError);
    
    return this.enhanceErrorContext(initError, {
      component: context.component,
      phase: context.phase,
      timestamp: context.timestamp
    });
  }

  /**
   * Handle template execution errors with enhanced context
   * Extracted from CleanupCoordinatorEnhanced lines 496-506
   */
  handleTemplateError(error: Error, templateContext: TemplateErrorContext): Error {
    const enhancedError = this.enhanceErrorContext(
      error instanceof Error ? error : new Error(String(error)), 
      {
        templateId: templateContext.templateId,
        targetComponents: templateContext.targetComponents,
        parametersCount: templateContext.parametersCount,
        component: templateContext.component,
        phase: templateContext.phase
      }
    );
    
    this.logger.logError('Template execution failed with timing context', enhancedError);
    return enhancedError;
  }

  /**
   * Handle async operation errors with isolation
   * Extracted from CleanupCoordinatorEnhanced lines 1059-1063
   */
  handleAsyncOperationError(error: Error, operationId: string): void {
    this.logger.logError('Operation failed but coordinator remains operational', error, {
      operationId,
      errorType: 'async_operation_error',
      isolated: true
    });
  }

  /**
   * Handle queue processing errors
   * Extracted from CleanupCoordinatorEnhanced lines 1567-1571
   */
  handleQueueProcessingError(error: Error): void {
    this.logger.logWarning('Some operations failed during batch processing', {
      error: error instanceof Error ? error.message : String(error),
      errorType: 'queue_processing_error'
    });
  }

  /**
   * Handle operation execution errors with retry context
   * Extracted from CleanupCoordinatorEnhanced lines 1610-1615
   */
  handleOperationExecutionError(error: Error, operationId: string): void {
    this.logger.logError('Cleanup operation failed', error, {
      operationId,
      errorType: 'operation_execution_error'
    });
  }

  /**
   * Enhance error context with timing and infrastructure information
   * Extracted from CleanupCoordinatorEnhanced lines 1224-1241
   */
  enhanceErrorContext(error: Error, context: ErrorContext): Error {
    const enhancedError = new Error(error.message);
    enhancedError.name = error.name;
    enhancedError.stack = error.stack;
    
    // Add resilient timing context to error for debugging
    const enhancement: ErrorEnhancement = {
      resilientContext: context,
      timestamp: new Date().toISOString(),
      component: 'AsyncErrorHandler'
    };

    return this.createStructuredError(enhancedError, enhancement);
  }

  /**
   * Create structured error with enhanced context
   */
  private createStructuredError(originalError: Error, enhancement: ErrorEnhancement): Error {
    Object.assign(originalError, enhancement);
    return originalError;
  }

  /**
   * Handle rollback errors during cleanup failure
   * Extracted from CleanupCoordinatorEnhanced lines 667
   */
  handleRollbackError(rollbackError: Error, operationId: string, checkpointId: string): void {
    this.logger.logError('Rollback failed after cleanup failure', rollbackError, { 
      operationId, 
      checkpointId,
      errorType: 'rollback_error'
    });
  }

  /**
   * Handle enhanced cleanup operation errors
   * Extracted from CleanupCoordinatorEnhanced lines 671-676
   */
  handleEnhancedCleanupError(error: Error, operationId: string, checkpointId?: string): Error {
    const enhancedError = error instanceof Error ? error : new Error(String(error));
    
    this.logger.logError('Enhanced cleanup operation failed', enhancedError, {
      operationId,
      checkpointId,
      errorType: 'enhanced_cleanup_error'
    });
    
    return enhancedError;
  }

  /**
   * Handle timing infrastructure errors
   * Extracted from CleanupCoordinatorEnhanced lines 395-397
   */
  handleTimingInfrastructureError(timingError: Error): void {
    this.logger.logError('Error during resilient timing infrastructure shutdown', 
      timingError instanceof Error ? timingError : new Error(String(timingError)));
  }

  /**
   * Handle template execution registration errors
   * Extracted from CleanupCoordinatorEnhanced lines 468-475
   */
  handleTemplateRegistrationError(
    registrationError: Error, 
    templateId: string, 
    executionId: string,
    timingReliable: boolean,
    executionTime: number
  ): void {
    this.logger.logWarning('Template execution registration failed', {
      templateId,
      executionId,
      timingReliable,
      executionTime,
      error: registrationError instanceof Error ? registrationError.message : String(registrationError)
    });
  }

  /**
   * Handle timing reliability metrics collection errors
   * Extracted from CleanupCoordinatorEnhanced lines 1168-1170
   */
  handleTimingReliabilityError(error: Error): void {
    this.logger.logWarning('Timing reliability metrics collection failed, using defaults', {
      error: error instanceof Error ? error.message : String(error)
    });
  }

  /**
   * Handle general async operation errors with context
   */
  handleGeneralAsyncError(error: Error, context: Record<string, unknown>): Error {
    const enhancedError = this.enhanceErrorContext(error, {
      component: 'AsyncErrorHandler',
      phase: 'general_async_operation',
      ...context
    });

    this.logger.logError('General async operation error', enhancedError);
    return enhancedError;
  }

  /**
   * Check if error is recoverable
   */
  isRecoverableError(error: Error): boolean {
    // Define criteria for recoverable errors
    const recoverableTypes = [
      'timeout',
      'network',
      'temporary',
      'retry'
    ];

    const errorMessage = error.message.toLowerCase();
    return recoverableTypes.some(type => errorMessage.includes(type));
  }

  /**
   * Get error severity level
   */
  getErrorSeverity(error: Error): 'low' | 'medium' | 'high' | 'critical' {
    const errorMessage = error.message.toLowerCase();
    
    if (errorMessage.includes('critical') || errorMessage.includes('fatal')) {
      return 'critical';
    }
    
    if (errorMessage.includes('timeout') || errorMessage.includes('network')) {
      return 'medium';
    }
    
    if (errorMessage.includes('warning') || errorMessage.includes('retry')) {
      return 'low';
    }
    
    return 'high'; // Default for unknown errors
  }
}
