/**
 * @file HealthStatusManager Test Suite
 * @filepath shared/src/base/modules/cleanup/__tests__/HealthStatusManager.test.ts
 * @description Comprehensive test suite for HealthStatusManager
 */

import { HealthStatusManager, ModuleStatusInfo, HealthStatusInfo } from '../HealthStatusManager';
import { IEnhancedCleanupConfig, ICleanupTemplate } from '../../../types/CleanupTypes';
import { ICleanupOperation, ICleanupMetrics, CleanupStatus, CleanupOperationType, CleanupPriority } from '../../../CleanupCoordinatorEnhanced';
import { ILoggingService } from '../../../LoggingMixin';
import { CleanupTemplateManager } from '../CleanupTemplateManager';
import { DependencyResolver } from '../DependencyResolver';
import { RollbackManager } from '../RollbackManager';
import { SystemOrchestrator } from '../SystemOrchestrator';

// Mock dependencies
jest.mock('../CleanupTemplateManager');
jest.mock('../DependencyResolver');
jest.mock('../RollbackManager');
jest.mock('../SystemOrchestrator');

describe('HealthStatusManager', () => {
  let healthStatusManager: HealthStatusManager;
  let mockLogger: jest.Mocked<ILoggingService>;
  let mockTemplateManager: jest.Mocked<CleanupTemplateManager>;
  let mockDependencyResolver: jest.Mocked<DependencyResolver>;
  let mockRollbackManager: jest.Mocked<RollbackManager>;
  let mockSystemOrchestrator: jest.Mocked<SystemOrchestrator>;
  let enhancedConfig: Required<IEnhancedCleanupConfig>;

  beforeEach(() => {
    // Setup mock logger
    mockLogger = {
      logInfo: jest.fn(),
      logError: jest.fn(),
      logWarning: jest.fn(),
      logDebug: jest.fn()
    };

    // Setup configuration
    enhancedConfig = {
      maxConcurrentOperations: 10,
      defaultTimeout: 30000,
      maxRetries: 3,
      conflictDetectionEnabled: true,
      metricsEnabled: true,
      cleanupIntervalMs: 300000,
      testMode: false,
      templateValidationEnabled: true,
      dependencyOptimizationEnabled: true,
      rollbackEnabled: true,
      maxCheckpoints: 100,
      checkpointRetentionDays: 7,
      phaseIntegrationEnabled: true,
      performanceMonitoringEnabled: true
    };

    // Setup mock components
    mockTemplateManager = {} as jest.Mocked<CleanupTemplateManager>;
    mockDependencyResolver = {} as jest.Mocked<DependencyResolver>;
    mockRollbackManager = {} as jest.Mocked<RollbackManager>;
    mockSystemOrchestrator = {
      performHealthCheck: jest.fn().mockResolvedValue({
        healthy: true,
        issues: [],
        metrics: {}
      }),
      getSystemStatus: jest.fn().mockReturnValue({ status: 'operational' })
    } as any;

    // Create HealthStatusManager instance
    healthStatusManager = new HealthStatusManager(
      enhancedConfig,
      mockTemplateManager,
      mockDependencyResolver,
      mockRollbackManager,
      mockSystemOrchestrator,
      mockLogger
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getModuleStatus', () => {
    it('should return module status for all components', async () => {
      const status = await healthStatusManager.getModuleStatus();

      expect(status).toEqual({
        CleanupTemplateManager: { initialized: true, operational: true },
        DependencyResolver: { initialized: true, operational: true },
        RollbackManager: { initialized: true, operational: true },
        SystemOrchestrator: { initialized: true, operational: true },
        TemplateDependencies: { initialized: true, operational: true },
        RollbackSnapshots: { initialized: true, operational: true },
        RollbackUtilities: { initialized: true, operational: true },
        CleanupConfiguration: { initialized: true, operational: true },
        UtilityAnalysis: { initialized: true, operational: true },
        UtilityValidation: { initialized: true, operational: true }
      });
    });

    it('should detect uninitialized modules', async () => {
      // Create manager with undefined components
      const managerWithUndefined = new HealthStatusManager(
        enhancedConfig,
        undefined as any,
        mockDependencyResolver,
        mockRollbackManager,
        mockSystemOrchestrator,
        mockLogger
      );

      const status = await managerWithUndefined.getModuleStatus();

      expect(status.CleanupTemplateManager.initialized).toBe(false);
      expect(status.TemplateDependencies.initialized).toBe(false);
    });
  });

  describe('getHealthStatus', () => {
    const createMockData = () => ({
      operations: new Map<string, ICleanupOperation>(),
      operationQueue: [] as ICleanupOperation[],
      templates: [] as ICleanupTemplate[],
      runningOperations: new Set<string>(),
      isInitialized: true,
      isShuttingDown: false,
      isHealthy: jest.fn().mockReturnValue(true)
    });

    it('should return healthy status in production mode', async () => {
      const mockData = createMockData();
      
      const status = await healthStatusManager.getHealthStatus(
        mockData.operations,
        mockData.operationQueue,
        mockData.templates,
        mockData.runningOperations,
        mockData.isInitialized,
        mockData.isShuttingDown,
        mockData.isHealthy
      );

      expect(status.operational).toBe(true);
      expect(status.issues).toEqual([]);
      expect(status.memoryUsage).toBeGreaterThan(0);
    });

    it('should return healthy status in test mode', async () => {
      enhancedConfig.testMode = true;
      const mockData = createMockData();
      
      const status = await healthStatusManager.getHealthStatus(
        mockData.operations,
        mockData.operationQueue,
        mockData.templates,
        mockData.runningOperations,
        mockData.isInitialized,
        mockData.isShuttingDown,
        mockData.isHealthy
      );

      expect(status.operational).toBe(true);
      expect(status.issues).toEqual([]);
    });

    it('should detect excessive concurrent operations', async () => {
      const mockData = createMockData();
      // Add many running operations
      for (let i = 0; i < 35; i++) {
        mockData.runningOperations.add(`op-${i}`);
      }
      
      const status = await healthStatusManager.getHealthStatus(
        mockData.operations,
        mockData.operationQueue,
        mockData.templates,
        mockData.runningOperations,
        mockData.isInitialized,
        mockData.isShuttingDown,
        mockData.isHealthy
      );

      expect(status.issues).toContain('Excessive concurrent operations detected');
    });

    it('should detect large operation queue', async () => {
      const mockData = createMockData();
      // Add many queued operations - need to exceed 2000 for test mode
      for (let i = 0; i < 2001; i++) {
        mockData.operationQueue.push({} as ICleanupOperation);
      }
      
      const status = await healthStatusManager.getHealthStatus(
        mockData.operations,
        mockData.operationQueue,
        mockData.templates,
        mockData.runningOperations,
        mockData.isInitialized,
        mockData.isShuttingDown,
        mockData.isHealthy
      );

      expect(status.issues).toContain('Operation queue is extremely large');
    });

    it('should detect high memory usage', async () => {
      // Test the memory calculation directly instead of creating massive data
      const memoryUsage = healthStatusManager.calculateMemoryUsage(
        510000, // 510K operations * 1KB = 510MB + base memory
        0,      // no queue
        0       // no templates
      );

      // Verify the calculation exceeds 500MB threshold
      expect(memoryUsage).toBeGreaterThan(500 * 1024 * 1024);

      // Now test with the actual getHealthStatus method using a smaller dataset
      // but mock the calculateMemoryUsage method to return high value
      const mockData = createMockData();
      jest.spyOn(healthStatusManager, 'calculateMemoryUsage').mockReturnValue(600 * 1024 * 1024); // 600MB

      const status = await healthStatusManager.getHealthStatus(
        mockData.operations,
        mockData.operationQueue,
        mockData.templates,
        mockData.runningOperations,
        mockData.isInitialized,
        mockData.isShuttingDown,
        mockData.isHealthy
      );

      expect(status.issues).toContain('Very high memory usage detected');
    });

    it('should return non-operational when shutting down', async () => {
      const mockData = createMockData();
      mockData.isShuttingDown = true;
      
      const status = await healthStatusManager.getHealthStatus(
        mockData.operations,
        mockData.operationQueue,
        mockData.templates,
        mockData.runningOperations,
        mockData.isInitialized,
        mockData.isShuttingDown,
        mockData.isHealthy
      );

      expect(status.operational).toBe(false);
    });
  });

  describe('resetToOperationalState', () => {
    it('should reset state in test mode', () => {
      enhancedConfig.testMode = true;
      const setInitialized = jest.fn();
      const runningOperations = new Set<string>();
      const operationQueue: ICleanupOperation[] = [];
      const metrics: ICleanupMetrics = {
        totalOperations: 0,
        queuedOperations: 0,
        runningOperations: 0,
        completedOperations: 0,
        failedOperations: 0,
        averageExecutionTime: 0,
        longestOperation: 0,
        operationsByType: {
          'timer-cleanup': 0,
          'event-handler-cleanup': 0,
          'buffer-cleanup': 0,
          'resource-cleanup': 0,
          'memory-cleanup': 0,
          'shutdown-cleanup': 0
        } as Record<CleanupOperationType, number>,
        operationsByPriority: {
          1: 0, // LOW
          2: 0, // NORMAL
          3: 0, // HIGH
          4: 0, // CRITICAL
          5: 0  // EMERGENCY
        } as Record<CleanupPriority, number>,
        conflictsPrevented: 0,
        lastCleanupTime: null
      };

      healthStatusManager.resetToOperationalState(
        false, // isShuttingDown
        setInitialized,
        runningOperations,
        operationQueue,
        metrics
      );

      expect(setInitialized).toHaveBeenCalledWith(true);
      expect(mockLogger.logInfo).toHaveBeenCalledWith(
        'Coordinator reset to operational state for test recovery'
      );
    });

    it('should not reset when shutting down', () => {
      enhancedConfig.testMode = true;
      const setInitialized = jest.fn();
      const runningOperations = new Set<string>();
      const operationQueue: ICleanupOperation[] = [];
      const metrics = {} as ICleanupMetrics;

      healthStatusManager.resetToOperationalState(
        true, // isShuttingDown
        setInitialized,
        runningOperations,
        operationQueue,
        metrics
      );

      expect(setInitialized).not.toHaveBeenCalled();
    });

    it('should log warning in production mode', () => {
      // Mock NODE_ENV to not be 'test' for this test
      const originalNodeEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      try {
        // Create a new mock logger for this test to avoid interference
        const productionMockLogger = {
          logInfo: jest.fn(),
          logError: jest.fn(),
          logWarning: jest.fn(),
          logDebug: jest.fn()
        };

        // Create a new manager with production config
        const productionConfig = { ...enhancedConfig, testMode: false };
        const productionManager = new HealthStatusManager(
          productionConfig,
          mockTemplateManager,
          mockDependencyResolver,
          mockRollbackManager,
          mockSystemOrchestrator,
          productionMockLogger
        );

        const setInitialized = jest.fn();
        const runningOperations = new Set<string>();
        const operationQueue: ICleanupOperation[] = [];
        const metrics = {} as ICleanupMetrics;

        productionManager.resetToOperationalState(
          false,
          setInitialized,
          runningOperations,
          operationQueue,
          metrics
        );

        expect(productionMockLogger.logWarning).toHaveBeenCalledWith(
          'resetToOperationalState called in production mode - this should be rare'
        );
      } finally {
        // Restore original NODE_ENV
        process.env.NODE_ENV = originalNodeEnv;
      }
    });
  });

  describe('calculateMemoryUsage', () => {
    it('should calculate memory usage correctly', () => {
      const usage = healthStatusManager.calculateMemoryUsage(100, 50, 10);
      
      // Base memory (10MB) + operations (100KB) + queue (25KB) + templates (20KB)
      const expected = 10 * 1024 * 1024 + 100 * 1024 + 50 * 512 + 10 * 2048;
      expect(usage).toBe(expected);
    });
  });

  describe('assessSystemHealth', () => {
    it('should return healthy when all modules are available', () => {
      const health = healthStatusManager.assessSystemHealth();
      expect(health).toBe('healthy');
    });

    it('should return unhealthy when core modules are missing', () => {
      const unhealthyManager = new HealthStatusManager(
        enhancedConfig,
        undefined as any,
        undefined as any,
        undefined as any,
        undefined as any,
        mockLogger
      );

      const health = unhealthyManager.assessSystemHealth();
      expect(health).toBe('unhealthy');
    });

    it('should return degraded on error', () => {
      // Create manager that will throw error during assessment
      const errorManager = new HealthStatusManager(
        enhancedConfig,
        mockTemplateManager,
        mockDependencyResolver,
        mockRollbackManager,
        mockSystemOrchestrator,
        mockLogger
      );

      // Mock to throw error - need to mock the original implementation
      const originalMethod = errorManager.assessSystemHealth;
      jest.spyOn(errorManager, 'assessSystemHealth').mockImplementation(() => {
        try {
          throw new Error('Assessment error');
        } catch (error) {
          mockLogger.logError('Error assessing system health', error);
          return 'degraded';
        }
      });

      const health = errorManager.assessSystemHealth();
      expect(health).toBe('degraded');
    });
  });

  describe('performSystemHealthCheck', () => {
    it('should delegate to system orchestrator', async () => {
      const expectedResult = {
        healthy: true,
        issues: [],
        metrics: { test: 'value' }
      };

      mockSystemOrchestrator.performHealthCheck.mockResolvedValue(expectedResult);

      const result = await healthStatusManager.performSystemHealthCheck();
      expect(result).toBe(expectedResult);
      expect(mockSystemOrchestrator.performHealthCheck).toHaveBeenCalled();
    });

    it('should handle orchestrator errors', async () => {
      const error = new Error('Health check failed');
      mockSystemOrchestrator.performHealthCheck.mockRejectedValue(error);

      const result = await healthStatusManager.performSystemHealthCheck();
      
      expect(result).toEqual({
        healthy: false,
        issues: ['System health check failed'],
        metrics: {}
      });
      expect(mockLogger.logError).toHaveBeenCalledWith('System health check failed', error);
    });
  });

  describe('getSystemStatus', () => {
    it('should delegate to system orchestrator', () => {
      const expectedStatus = { status: 'operational', uptime: 12345 };
      mockSystemOrchestrator.getSystemStatus.mockReturnValue(expectedStatus);

      const result = healthStatusManager.getSystemStatus();
      expect(result).toBe(expectedStatus);
      expect(mockSystemOrchestrator.getSystemStatus).toHaveBeenCalled();
    });

    it('should handle orchestrator errors', () => {
      const error = new Error('Status retrieval failed');
      mockSystemOrchestrator.getSystemStatus.mockImplementation(() => {
        throw error;
      });

      const result = healthStatusManager.getSystemStatus();

      expect(result).toEqual({ error: 'Failed to get system status' });
      expect(mockLogger.logError).toHaveBeenCalledWith('Failed to get system status', error);
    });
  });

  // ============================================================================
  // SURGICAL PRECISION TESTS - TARGETING UNCOVERED LINES (82.75% → 100%)
  // ============================================================================

  describe('🎯 Production Mode Coverage - Lines 248-269', () => {
    it('should execute production mode health status logic (lines 248-269)', async () => {
      // Create production mode configuration
      const productionConfig = {
        ...enhancedConfig,
        testMode: false
      };

      // Create production mode manager
      const productionManager = new HealthStatusManager(
        productionConfig,
        mockTemplateManager,
        mockDependencyResolver,
        mockRollbackManager,
        mockSystemOrchestrator,
        mockLogger
      );

      // Mock NODE_ENV to be production (not test)
      const originalNodeEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      try {
        // Create test data that will trigger production mode logic
        const operations = new Map();
        const operationQueue: ICleanupOperation[] = [];
        const templates: ICleanupTemplate[] = [];
        const runningOperations = new Set<string>();

        // Add operations to trigger production thresholds (lines 257-267)
        for (let i = 0; i < 25; i++) { // More than maxConcurrentOperations * 2 (10 * 2 = 20)
          runningOperations.add(`operation-${i}`);
        }

        // Add operations to queue to trigger queue threshold (line 261-263)
        for (let i = 0; i < 1500; i++) { // More than 1000 threshold
          operationQueue.push({
            id: `queue-op-${i}`,
            type: CleanupOperationType.RESOURCE_CLEANUP,
            priority: CleanupPriority.LOW,
            status: CleanupStatus.QUEUED,
            componentId: 'test',
            operation: jest.fn().mockResolvedValue(undefined),
            dependencies: [],
            timeout: 5000,
            retryCount: 0,
            maxRetries: 3,
            createdAt: new Date()
          });
        }

        const isInitialized = true;
        const isShuttingDown = false;
        const isHealthy = () => true;

        // Call getHealthStatus to hit production mode lines 248-269
        const result = await productionManager.getHealthStatus(
          operations,
          operationQueue,
          templates,
          runningOperations,
          isInitialized,
          isShuttingDown,
          isHealthy
        );

        // Verify production mode logic was executed
        expect(result.operational).toBe(true); // Line 248-254 logic
        expect(result.issues).toContain('Too many concurrent operations'); // Line 257-259
        expect(result.issues).toContain('Operation queue is very large'); // Line 261-263
        expect(result.memoryUsage).toBeGreaterThan(0); // Memory calculation

        // Verify production mode thresholds are different from test mode
        expect(result.issues.length).toBeGreaterThan(0);
      } finally {
        // Restore NODE_ENV
        process.env.NODE_ENV = originalNodeEnv;
      }
    });

    it('should trigger high memory usage detection in production mode (line 265-267)', async () => {
      // Create production mode configuration
      const productionConfig = {
        ...enhancedConfig,
        testMode: false
      };

      const productionManager = new HealthStatusManager(
        productionConfig,
        mockTemplateManager,
        mockDependencyResolver,
        mockRollbackManager,
        mockSystemOrchestrator,
        mockLogger
      );

      // Mock NODE_ENV to be production
      const originalNodeEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      try {
        // Mock calculateMemoryUsage to return high memory usage
        const originalCalculateMemory = productionManager.calculateMemoryUsage;
        jest.spyOn(productionManager, 'calculateMemoryUsage').mockReturnValue(250 * 1024 * 1024); // 250MB > 200MB threshold

        const operations = new Map();
        const operationQueue: ICleanupOperation[] = [];
        const templates: ICleanupTemplate[] = [];
        const runningOperations = new Set<string>();
        const isInitialized = true;
        const isShuttingDown = false;
        const isHealthy = () => true;

        const result = await productionManager.getHealthStatus(
          operations,
          operationQueue,
          templates,
          runningOperations,
          isInitialized,
          isShuttingDown,
          isHealthy
        );

        // Verify high memory usage detection (lines 265-267)
        expect(result.issues).toContain('High memory usage detected');
        expect(result.memoryUsage).toBe(250 * 1024 * 1024);
      } finally {
        process.env.NODE_ENV = originalNodeEnv;
      }
    });

    it('should handle production mode with missing components (lines 248-254)', async () => {
      // Create production mode configuration
      const productionConfig = {
        ...enhancedConfig,
        testMode: false
      };

      // Create manager with missing components
      const productionManager = new HealthStatusManager(
        productionConfig,
        undefined as any, // Missing templateManager
        mockDependencyResolver,
        mockRollbackManager,
        mockSystemOrchestrator,
        mockLogger
      );

      // Mock NODE_ENV to be production
      const originalNodeEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      try {
        const operations = new Map();
        const operationQueue: ICleanupOperation[] = [];
        const templates: ICleanupTemplate[] = [];
        const runningOperations = new Set<string>();
        const isInitialized = true;
        const isShuttingDown = false;
        const isHealthy = () => true;

        const result = await productionManager.getHealthStatus(
          operations,
          operationQueue,
          templates,
          runningOperations,
          isInitialized,
          isShuttingDown,
          isHealthy
        );

        // Verify production mode operational check (lines 248-254)
        // Should be false because templateManager is undefined
        expect(result.operational).toBe(false);
      } finally {
        process.env.NODE_ENV = originalNodeEnv;
      }
    });
  });

  describe('🎯 Error Handling Coverage - Lines 348-349', () => {
    it('should handle errors in assessSystemHealth and log them (lines 348-349)', () => {
      // Create a manager with a corrupted templateManager that throws when accessed
      const corruptedTemplateManager = {};
      Object.defineProperty(corruptedTemplateManager, 'toString', {
        get() {
          throw new Error('Property access error during health assessment');
        }
      });

      const errorManager = new HealthStatusManager(
        enhancedConfig,
        corruptedTemplateManager as any,
        mockDependencyResolver,
        mockRollbackManager,
        mockSystemOrchestrator,
        mockLogger
      );

      // Mock the templateManager property to throw when accessed
      Object.defineProperty(errorManager, 'templateManager', {
        get() {
          throw new Error('Template manager access error');
        }
      });

      // Call assessSystemHealth to trigger the catch block (lines 347-350)
      const result = errorManager.assessSystemHealth();

      // Verify error handling (lines 348-349)
      expect(mockLogger.logError).toHaveBeenCalledWith(
        'Error assessing system health',
        expect.any(Error)
      );
      expect(result).toBe('degraded');
    });

    it('should handle non-Error objects in assessSystemHealth catch block (line 348)', () => {
      const errorManager = new HealthStatusManager(
        enhancedConfig,
        mockTemplateManager,
        mockDependencyResolver,
        mockRollbackManager,
        mockSystemOrchestrator,
        mockLogger
      );

      // Mock to throw non-Error object when accessing templateManager
      const nonErrorObject = 'string-error-for-assessment';
      Object.defineProperty(errorManager, 'templateManager', {
        get() {
          throw nonErrorObject;
        }
      });

      const result = errorManager.assessSystemHealth();

      // Verify error logging with non-Error object (line 348)
      expect(mockLogger.logError).toHaveBeenCalledWith(
        'Error assessing system health',
        nonErrorObject
      );
      expect(result).toBe('degraded');
    });
  });

  // ============================================================================
  // BRANCH COVERAGE ENHANCEMENT - TARGETING CONDITIONAL LOGIC
  // ============================================================================

  describe('🎯 Branch Coverage Enhancement - Conditional Logic Paths', () => {
    it('should test all combinations of operational status conditions', async () => {
      const productionConfig = { ...enhancedConfig, testMode: false };
      const productionManager = new HealthStatusManager(
        productionConfig,
        mockTemplateManager,
        mockDependencyResolver,
        mockRollbackManager,
        mockSystemOrchestrator,
        mockLogger
      );

      const originalNodeEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      try {
        const operations = new Map();
        const operationQueue: ICleanupOperation[] = [];
        const templates: ICleanupTemplate[] = [];
        const runningOperations = new Set<string>();

        // Test case 1: isInitialized = false
        let result = await productionManager.getHealthStatus(
          operations, operationQueue, templates, runningOperations,
          false, false, () => true
        );
        expect(result.operational).toBe(false);

        // Test case 2: isShuttingDown = true
        result = await productionManager.getHealthStatus(
          operations, operationQueue, templates, runningOperations,
          true, true, () => true
        );
        expect(result.operational).toBe(false);

        // Test case 3: isHealthy() = false
        result = await productionManager.getHealthStatus(
          operations, operationQueue, templates, runningOperations,
          true, false, () => false
        );
        expect(result.operational).toBe(false);

        // Test case 4: All conditions true
        result = await productionManager.getHealthStatus(
          operations, operationQueue, templates, runningOperations,
          true, false, () => true
        );
        expect(result.operational).toBe(true);
      } finally {
        process.env.NODE_ENV = originalNodeEnv;
      }
    });

    it('should test edge cases for threshold conditions', async () => {
      const productionConfig = { ...enhancedConfig, testMode: false };
      const productionManager = new HealthStatusManager(
        productionConfig,
        mockTemplateManager,
        mockDependencyResolver,
        mockRollbackManager,
        mockSystemOrchestrator,
        mockLogger
      );

      const originalNodeEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      try {
        const operations = new Map();
        const operationQueue: ICleanupOperation[] = [];
        const templates: ICleanupTemplate[] = [];
        const runningOperations = new Set<string>();

        // Test exact threshold boundaries
        // Concurrent operations: exactly maxConcurrentOperations * 2
        for (let i = 0; i < 20; i++) { // Exactly 10 * 2
          runningOperations.add(`boundary-op-${i}`);
        }

        let result = await productionManager.getHealthStatus(
          operations, operationQueue, templates, runningOperations,
          true, false, () => true
        );
        expect(result.issues).not.toContain('Too many concurrent operations');

        // Add one more to exceed threshold
        runningOperations.add('boundary-op-20');
        result = await productionManager.getHealthStatus(
          operations, operationQueue, templates, runningOperations,
          true, false, () => true
        );
        expect(result.issues).toContain('Too many concurrent operations');
      } finally {
        process.env.NODE_ENV = originalNodeEnv;
      }
    });
  });
});
