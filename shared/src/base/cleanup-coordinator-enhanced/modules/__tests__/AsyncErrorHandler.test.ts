/**
 * @file AsyncErrorHandler Test Suite
 * @filepath shared/src/base/modules/cleanup/__tests__/AsyncErrorHandler.test.ts
 * @description Comprehensive test suite for AsyncErrorHandler
 */

import { 
  AsyncErrorHandler, 
  InitializationContext, 
  TemplateErrorContext, 
  ErrorContext,
  ErrorEnhancement 
} from '../AsyncErrorHandler';
import { ILoggingService } from '../../../LoggingMixin';

describe('AsyncErrorHandler', () => {
  let asyncErrorHandler: AsyncErrorHandler;
  let mockLogger: jest.Mocked<ILoggingService>;

  beforeEach(() => {
    // Setup mock logger
    mockLogger = {
      logInfo: jest.fn(),
      logError: jest.fn(),
      logWarning: jest.fn(),
      logDebug: jest.fn()
    };

    // Create AsyncErrorHandler instance
    asyncErrorHandler = new AsyncErrorHandler(mockLogger);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('handleInitializationError', () => {
    it('should handle initialization errors with enhanced context', () => {
      const originalError = new Error('Initialization failed');
      const context: InitializationContext = {
        component: 'TestComponent',
        phase: 'initialization',
        timestamp: '2025-08-07T16:00:00.000Z'
      };

      const result = asyncErrorHandler.handleInitializationError(originalError, context);

      expect(mockLogger.logError).toHaveBeenCalledWith('Initialization failed', originalError);
      expect(result).toBeInstanceOf(Error);
      expect(result.message).toBe('Initialization failed');
      expect((result as any).component).toBe('AsyncErrorHandler');
    });

    it('should handle non-Error objects', () => {
      const context: InitializationContext = {
        component: 'TestComponent',
        phase: 'initialization',
        timestamp: '2025-08-07T16:00:00.000Z'
      };

      const result = asyncErrorHandler.handleInitializationError('String error' as any, context);

      expect(result).toBeInstanceOf(Error);
      expect(result.message).toBe('String error');
    });
  });

  describe('handleTemplateError', () => {
    it('should handle template execution errors', () => {
      const originalError = new Error('Template execution failed');
      const templateContext: TemplateErrorContext = {
        templateId: 'test-template',
        targetComponents: ['comp1', 'comp2'],
        parametersCount: 5,
        component: 'TemplateManager',
        phase: 'execution'
      };

      const result = asyncErrorHandler.handleTemplateError(originalError, templateContext);

      expect(mockLogger.logError).toHaveBeenCalledWith(
        'Template execution failed with timing context',
        expect.any(Error)
      );
      expect(result).toBeInstanceOf(Error);
      expect(result.message).toBe('Template execution failed');
    });
  });

  describe('handleAsyncOperationError', () => {
    it('should handle async operation errors with isolation', () => {
      const error = new Error('Async operation failed');
      const operationId = 'test-operation-123';

      asyncErrorHandler.handleAsyncOperationError(error, operationId);

      expect(mockLogger.logError).toHaveBeenCalledWith(
        'Operation failed but coordinator remains operational',
        error,
        {
          operationId,
          errorType: 'async_operation_error',
          isolated: true
        }
      );
    });
  });

  describe('handleQueueProcessingError', () => {
    it('should handle queue processing errors', () => {
      const error = new Error('Queue processing failed');

      asyncErrorHandler.handleQueueProcessingError(error);

      expect(mockLogger.logWarning).toHaveBeenCalledWith(
        'Some operations failed during batch processing',
        {
          error: 'Queue processing failed',
          errorType: 'queue_processing_error'
        }
      );
    });

    it('should handle non-Error objects in queue processing', () => {
      asyncErrorHandler.handleQueueProcessingError('String error' as any);

      expect(mockLogger.logWarning).toHaveBeenCalledWith(
        'Some operations failed during batch processing',
        {
          error: 'String error',
          errorType: 'queue_processing_error'
        }
      );
    });
  });

  describe('handleOperationExecutionError', () => {
    it('should handle operation execution errors', () => {
      const error = new Error('Operation execution failed');
      const operationId = 'exec-op-456';

      asyncErrorHandler.handleOperationExecutionError(error, operationId);

      expect(mockLogger.logError).toHaveBeenCalledWith(
        'Cleanup operation failed',
        error,
        {
          operationId,
          errorType: 'operation_execution_error'
        }
      );
    });
  });

  describe('enhanceErrorContext', () => {
    it('should enhance error context with timing information', () => {
      const originalError = new Error('Test error');
      const context: ErrorContext = {
        component: 'TestComponent',
        phase: 'test_phase',
        customData: 'test_value'
      };

      const result = asyncErrorHandler.enhanceErrorContext(originalError, context);

      expect(result).toBeInstanceOf(Error);
      expect(result.message).toBe('Test error');
      expect(result.name).toBe(originalError.name);
      expect(result.stack).toBe(originalError.stack);
      expect((result as any).resilientContext).toEqual(context);
      expect((result as any).component).toBe('AsyncErrorHandler');
      expect((result as any).timestamp).toBeDefined();
    });
  });

  describe('handleRollbackError', () => {
    it('should handle rollback errors', () => {
      const rollbackError = new Error('Rollback failed');
      const operationId = 'rollback-op-789';
      const checkpointId = 'checkpoint-123';

      asyncErrorHandler.handleRollbackError(rollbackError, operationId, checkpointId);

      expect(mockLogger.logError).toHaveBeenCalledWith(
        'Rollback failed after cleanup failure',
        rollbackError,
        {
          operationId,
          checkpointId,
          errorType: 'rollback_error'
        }
      );
    });
  });

  describe('handleEnhancedCleanupError', () => {
    it('should handle enhanced cleanup operation errors', () => {
      const error = new Error('Enhanced cleanup failed');
      const operationId = 'enhanced-op-101';
      const checkpointId = 'checkpoint-456';

      const result = asyncErrorHandler.handleEnhancedCleanupError(error, operationId, checkpointId);

      expect(mockLogger.logError).toHaveBeenCalledWith(
        'Enhanced cleanup operation failed',
        error,
        {
          operationId,
          checkpointId,
          errorType: 'enhanced_cleanup_error'
        }
      );
      expect(result).toBe(error);
    });

    it('should handle non-Error objects in enhanced cleanup', () => {
      const result = asyncErrorHandler.handleEnhancedCleanupError('String error' as any, 'op-id');

      expect(result).toBeInstanceOf(Error);
      expect(result.message).toBe('String error');
    });
  });

  describe('handleTimingInfrastructureError', () => {
    it('should handle timing infrastructure errors', () => {
      const timingError = new Error('Timing infrastructure error');

      asyncErrorHandler.handleTimingInfrastructureError(timingError);

      expect(mockLogger.logError).toHaveBeenCalledWith(
        'Error during resilient timing infrastructure shutdown',
        timingError
      );
    });
  });

  describe('handleTemplateRegistrationError', () => {
    it('should handle template execution registration errors', () => {
      const registrationError = new Error('Registration failed');
      const templateId = 'template-123';
      const executionId = 'exec-456';
      const timingReliable = true;
      const executionTime = 150;

      asyncErrorHandler.handleTemplateRegistrationError(
        registrationError,
        templateId,
        executionId,
        timingReliable,
        executionTime
      );

      expect(mockLogger.logWarning).toHaveBeenCalledWith(
        'Template execution registration failed',
        {
          templateId,
          executionId,
          timingReliable,
          executionTime,
          error: 'Registration failed'
        }
      );
    });
  });

  describe('handleTimingReliabilityError', () => {
    it('should handle timing reliability metrics collection errors', () => {
      const error = new Error('Reliability metrics failed');

      asyncErrorHandler.handleTimingReliabilityError(error);

      expect(mockLogger.logWarning).toHaveBeenCalledWith(
        'Timing reliability metrics collection failed, using defaults',
        {
          error: 'Reliability metrics failed'
        }
      );
    });
  });

  describe('handleGeneralAsyncError', () => {
    it('should handle general async operation errors', () => {
      const error = new Error('General async error');
      const context = { operationType: 'test', retryCount: 2 };

      const result = asyncErrorHandler.handleGeneralAsyncError(error, context);

      expect(mockLogger.logError).toHaveBeenCalledWith('General async operation error', result);
      expect((result as any).resilientContext.operationType).toBe('test');
      expect((result as any).resilientContext.retryCount).toBe(2);
    });
  });

  describe('isRecoverableError', () => {
    it('should identify recoverable errors', () => {
      const timeoutError = new Error('Operation timeout occurred');
      const networkError = new Error('Network connection failed');
      const retryError = new Error('Retry limit exceeded');
      const fatalError = new Error('Fatal system error');

      expect(asyncErrorHandler.isRecoverableError(timeoutError)).toBe(true);
      expect(asyncErrorHandler.isRecoverableError(networkError)).toBe(true);
      expect(asyncErrorHandler.isRecoverableError(retryError)).toBe(true);
      expect(asyncErrorHandler.isRecoverableError(fatalError)).toBe(false);
    });
  });

  describe('getErrorSeverity', () => {
    it('should classify error severity correctly', () => {
      const criticalError = new Error('Critical system failure');
      const fatalError = new Error('Fatal error occurred');
      const timeoutError = new Error('Request timeout');
      const networkError = new Error('Network error');
      const warningError = new Error('Warning: deprecated method');
      const retryError = new Error('Retry attempt failed');
      const unknownError = new Error('Unknown error');

      expect(asyncErrorHandler.getErrorSeverity(criticalError)).toBe('critical');
      expect(asyncErrorHandler.getErrorSeverity(fatalError)).toBe('critical');
      expect(asyncErrorHandler.getErrorSeverity(timeoutError)).toBe('medium');
      expect(asyncErrorHandler.getErrorSeverity(networkError)).toBe('medium');
      expect(asyncErrorHandler.getErrorSeverity(warningError)).toBe('low');
      expect(asyncErrorHandler.getErrorSeverity(retryError)).toBe('low');
      expect(asyncErrorHandler.getErrorSeverity(unknownError)).toBe('high');
    });
  });

  // ============================================================================
  // SURGICAL PRECISION TESTS - TARGETING UNCOVERED BRANCHES (82.6% → 95%+)
  // ============================================================================

  describe('🎯 Branch Coverage - Ternary Operator FALSE Branches', () => {
    it('should cover Line 162 - new Error(String(error)) branch in handleTemplateError', () => {
      // Target line 162: error instanceof Error ? error : new Error(String(error))
      // We need to force the FALSE branch of the ternary operator
      const nonErrorObject = 'string-error-for-line-162';
      const templateContext: TemplateErrorContext = {
        templateId: 'test-template',
        targetComponents: ['comp1'],
        parametersCount: 1,
        component: 'TemplateManager',
        phase: 'execution'
      };

      const result = asyncErrorHandler.handleTemplateError(nonErrorObject as any, templateContext);

      // Verify line 162 FALSE branch was executed: new Error(String(error))
      expect(result).toBeInstanceOf(Error);
      expect(result.message).toBe('string-error-for-line-162');

      // Verify logging occurred with the converted Error
      expect(mockLogger.logError).toHaveBeenCalledWith(
        'Template execution failed with timing context',
        expect.any(Error)
      );
    });

    it('should cover Line 271 - new Error(String(timingError)) branch in handleTimingInfrastructureError', () => {
      // Target line 271: timingError instanceof Error ? timingError : new Error(String(timingError))
      // We need to force the FALSE branch of the ternary operator
      const nonErrorObject = { customError: 'timing-infrastructure-failure', code: 500 };

      asyncErrorHandler.handleTimingInfrastructureError(nonErrorObject as any);

      // Verify line 271 FALSE branch was executed: new Error(String(timingError))
      expect(mockLogger.logError).toHaveBeenCalledWith(
        'Error during resilient timing infrastructure shutdown',
        expect.any(Error) // Should be new Error instance from FALSE branch
      );

      // Verify the logged error has the correct message from String(timingError)
      const loggedError = mockLogger.logError.mock.calls.find(call =>
        call[0] === 'Error during resilient timing infrastructure shutdown'
      )?.[1] as Error;
      expect(loggedError?.message).toBe('[object Object]'); // String(object) result
    });

    it('should cover Line 290 - String(registrationError) branch in handleTemplateRegistrationError', () => {
      // Target line 290: registrationError instanceof Error ? registrationError.message : String(registrationError)
      // We need to force the FALSE branch of the ternary operator
      const nonErrorObject = 'registration-failure-string';
      const templateId = 'template-123';
      const executionId = 'exec-456';
      const timingReliable = true;
      const executionTime = 150;

      asyncErrorHandler.handleTemplateRegistrationError(
        nonErrorObject as any,
        templateId,
        executionId,
        timingReliable,
        executionTime
      );

      // Verify line 290 FALSE branch was executed: String(registrationError)
      expect(mockLogger.logWarning).toHaveBeenCalledWith(
        'Template execution registration failed',
        {
          templateId,
          executionId,
          timingReliable,
          executionTime,
          error: 'registration-failure-string' // String(registrationError) result
        }
      );
    });

    it('should cover Line 300 - String(error) branch in handleTimingReliabilityError', () => {
      // Target line 300: error instanceof Error ? error.message : String(error)
      // We need to force the FALSE branch of the ternary operator
      const nonErrorObject = { reliabilityError: 'metrics-collection-failed', timestamp: Date.now() };

      asyncErrorHandler.handleTimingReliabilityError(nonErrorObject as any);

      // Verify line 300 FALSE branch was executed: String(error)
      expect(mockLogger.logWarning).toHaveBeenCalledWith(
        'Timing reliability metrics collection failed, using defaults',
        {
          error: '[object Object]' // String(error) result for object
        }
      );
    });
  });

  // ============================================================================
  // COMPREHENSIVE ERROR TYPE TESTING - EDGE CASES
  // ============================================================================

  describe('🎯 Error Type Edge Cases - Complete Branch Coverage', () => {
    it('should handle null and undefined values in error conversion', () => {
      // Test null error handling
      const result1 = asyncErrorHandler.handleEnhancedCleanupError(null as any, 'op-null');
      expect(result1).toBeInstanceOf(Error);
      expect(result1.message).toBe('null');

      // Test undefined error handling
      const result2 = asyncErrorHandler.handleEnhancedCleanupError(undefined as any, 'op-undefined');
      expect(result2).toBeInstanceOf(Error);
      expect(result2.message).toBe('undefined');
    });

    it('should handle complex object errors in all methods', () => {
      const complexError = {
        type: 'ComplexError',
        details: { code: 500, message: 'Complex failure' },
        stack: ['frame1', 'frame2']
      };

      // Test in handleTimingInfrastructureError
      asyncErrorHandler.handleTimingInfrastructureError(complexError as any);

      // Test in handleTemplateRegistrationError
      asyncErrorHandler.handleTemplateRegistrationError(
        complexError as any,
        'template-complex',
        'exec-complex',
        false,
        200
      );

      // Test in handleTimingReliabilityError
      asyncErrorHandler.handleTimingReliabilityError(complexError as any);

      // Verify all methods handled the complex object correctly
      expect(mockLogger.logError).toHaveBeenCalled();
      expect(mockLogger.logWarning).toHaveBeenCalled();
    });

    it('should handle array and primitive error types', () => {
      // Test array error
      const arrayError = ['error1', 'error2', 'error3'];
      asyncErrorHandler.handleTimingReliabilityError(arrayError as any);

      // Test number error
      const numberError = 404;
      asyncErrorHandler.handleTimingInfrastructureError(numberError as any);

      // Test boolean error
      const booleanError = false;
      asyncErrorHandler.handleTemplateRegistrationError(
        booleanError as any,
        'template-bool',
        'exec-bool',
        true,
        100
      );

      // Verify all primitive types were converted to strings correctly
      expect(mockLogger.logWarning).toHaveBeenCalledWith(
        'Timing reliability metrics collection failed, using defaults',
        { error: 'error1,error2,error3' }
      );

      expect(mockLogger.logError).toHaveBeenCalledWith(
        'Error during resilient timing infrastructure shutdown',
        expect.any(Error)
      );

      expect(mockLogger.logWarning).toHaveBeenCalledWith(
        'Template execution registration failed',
        expect.objectContaining({ error: 'false' })
      );
    });
  });

  // ============================================================================
  // ADVANCED ASYNC ERROR SCENARIOS - PRODUCTION READINESS
  // ============================================================================

  describe('🎯 Advanced Async Error Scenarios - Production Testing', () => {
    it('should handle concurrent error processing', async () => {
      // Test concurrent error handling to ensure thread safety
      const errors = [
        new Error('Concurrent error 1'),
        new Error('Concurrent error 2'),
        new Error('Concurrent error 3')
      ];

      const promises = errors.map((error, index) =>
        Promise.resolve(asyncErrorHandler.handleGeneralAsyncError(error, {
          concurrentId: index,
          timestamp: Date.now()
        }))
      );

      const results = await Promise.all(promises);

      // Verify all errors were processed correctly
      expect(results).toHaveLength(3);
      results.forEach((result, index) => {
        expect(result).toBeInstanceOf(Error);
        expect(result.message).toBe(`Concurrent error ${index + 1}`);
        expect((result as any).resilientContext.concurrentId).toBe(index);
      });

      // Verify logging occurred for all errors
      expect(mockLogger.logError).toHaveBeenCalledTimes(3);
    });

    it('should handle error context with circular references', () => {
      // Create circular reference object
      const circularContext: any = { name: 'circular' };
      circularContext.self = circularContext;

      const error = new Error('Circular reference error');

      // This should not throw despite circular reference
      const result = asyncErrorHandler.handleGeneralAsyncError(error, circularContext);

      expect(result).toBeInstanceOf(Error);
      expect(result.message).toBe('Circular reference error');
      expect(mockLogger.logError).toHaveBeenCalled();
    });

    it('should handle very large error contexts', () => {
      // Create large context object
      const largeContext = {
        largeArray: new Array(1000).fill('large-data-item'),
        largeString: 'x'.repeat(10000),
        nestedObject: {
          level1: { level2: { level3: { data: 'deep-nested-data' } } }
        }
      };

      const error = new Error('Large context error');
      const result = asyncErrorHandler.handleGeneralAsyncError(error, largeContext);

      expect(result).toBeInstanceOf(Error);
      expect(result.message).toBe('Large context error');
      expect((result as any).resilientContext.largeArray).toHaveLength(1000);
      expect((result as any).resilientContext.largeString).toHaveLength(10000);
    });

    it('should handle error enhancement with special characters and unicode', () => {
      const unicodeError = new Error('Unicode error: 🚨 特殊字符 العربية русский');
      const context: ErrorContext = {
        component: 'UnicodeComponent',
        phase: 'unicode_processing',
        specialChars: '!@#$%^&*()_+-=[]{}|;:,.<>?',
        emoji: '🎯🔥💯✅❌🚀'
      };

      const result = asyncErrorHandler.enhanceErrorContext(unicodeError, context);

      expect(result).toBeInstanceOf(Error);
      expect(result.message).toBe('Unicode error: 🚨 特殊字符 العربية русский');
      expect((result as any).resilientContext.specialChars).toBe('!@#$%^&*()_+-=[]{}|;:,.<>?');
      expect((result as any).resilientContext.emoji).toBe('🎯🔥💯✅❌🚀');
    });
  });

  // ============================================================================
  // ERROR CLASSIFICATION EDGE CASES - COMPREHENSIVE COVERAGE
  // ============================================================================

  describe('🎯 Error Classification Edge Cases - Complete Coverage', () => {
    it('should handle edge cases in isRecoverableError', () => {
      // Test case sensitivity
      const upperCaseError = new Error('TIMEOUT OCCURRED');
      const mixedCaseError = new Error('Network Connection Failed');
      const partialMatchError = new Error('This is a temporary issue');

      expect(asyncErrorHandler.isRecoverableError(upperCaseError)).toBe(true);
      expect(asyncErrorHandler.isRecoverableError(mixedCaseError)).toBe(true);
      expect(asyncErrorHandler.isRecoverableError(partialMatchError)).toBe(true);

      // Test non-recoverable edge cases
      const emptyError = new Error('');
      const spaceError = new Error('   ');
      const specialCharError = new Error('!@#$%^&*()');

      expect(asyncErrorHandler.isRecoverableError(emptyError)).toBe(false);
      expect(asyncErrorHandler.isRecoverableError(spaceError)).toBe(false);
      expect(asyncErrorHandler.isRecoverableError(specialCharError)).toBe(false);
    });

    it('should handle edge cases in getErrorSeverity', () => {
      // Test case sensitivity and partial matches
      const upperCriticalError = new Error('CRITICAL SYSTEM FAILURE');
      const partialFatalError = new Error('This is a fatal error in the system');
      const multipleKeywordError = new Error('Critical timeout network warning');

      expect(asyncErrorHandler.getErrorSeverity(upperCriticalError)).toBe('critical');
      expect(asyncErrorHandler.getErrorSeverity(partialFatalError)).toBe('critical');
      expect(asyncErrorHandler.getErrorSeverity(multipleKeywordError)).toBe('critical'); // First match wins

      // Test edge cases
      const emptyMessageError = new Error('');
      const whitespaceError = new Error('   ');
      const numbersOnlyError = new Error('12345');

      expect(asyncErrorHandler.getErrorSeverity(emptyMessageError)).toBe('high');
      expect(asyncErrorHandler.getErrorSeverity(whitespaceError)).toBe('high');
      expect(asyncErrorHandler.getErrorSeverity(numbersOnlyError)).toBe('high');
    });

    it('should handle template error with missing optional checkpoint', () => {
      // Test handleEnhancedCleanupError without checkpointId
      const error = new Error('Enhanced cleanup without checkpoint');
      const operationId = 'op-no-checkpoint';

      const result = asyncErrorHandler.handleEnhancedCleanupError(error, operationId);

      expect(mockLogger.logError).toHaveBeenCalledWith(
        'Enhanced cleanup operation failed',
        error,
        {
          operationId,
          checkpointId: undefined,
          errorType: 'enhanced_cleanup_error'
        }
      );
      expect(result).toBe(error);
    });
  });

  // ============================================================================
  // PRIVATE METHOD TESTING - COMPLETE COVERAGE
  // ============================================================================

  describe('🎯 Private Method Testing - Internal Implementation', () => {
    it('should test createStructuredError private method directly', () => {
      // Access private method for complete coverage
      const createStructuredError = (asyncErrorHandler as any).createStructuredError.bind(asyncErrorHandler);

      const originalError = new Error('Test structured error');
      const enhancement: ErrorEnhancement = {
        resilientContext: { test: 'context' },
        timestamp: '2025-08-07T16:00:00.000Z',
        component: 'TestComponent',
        timingInfrastructureStatus: {
          timerInitialized: true,
          metricsCollectorInitialized: false
        }
      };

      const result = createStructuredError(originalError, enhancement);

      expect(result).toBe(originalError); // Same reference
      expect((result as any).resilientContext).toEqual({ test: 'context' });
      expect((result as any).timestamp).toBe('2025-08-07T16:00:00.000Z');
      expect((result as any).component).toBe('TestComponent');
      expect((result as any).timingInfrastructureStatus).toEqual({
        timerInitialized: true,
        metricsCollectorInitialized: false
      });
    });

    it('should test error enhancement with all possible context properties', () => {
      const error = new Error('Comprehensive context test');
      const context: ErrorContext = {
        component: 'ComprehensiveComponent',
        phase: 'comprehensive_testing',
        // Test all possible context properties
        operationId: 'op-comprehensive',
        templateId: 'template-comprehensive',
        checkpointId: 'checkpoint-comprehensive',
        executionId: 'exec-comprehensive',
        retryCount: 3,
        maxRetries: 5,
        timeout: 30000,
        priority: 'high',
        metadata: {
          nested: {
            deeply: {
              value: 'deep-value'
            }
          }
        }
      };

      const result = asyncErrorHandler.enhanceErrorContext(error, context);

      expect(result).toBeInstanceOf(Error);
      expect((result as any).resilientContext).toEqual(context);
      expect((result as any).component).toBe('AsyncErrorHandler');
      expect((result as any).timestamp).toBeDefined();
    });
  });
});
