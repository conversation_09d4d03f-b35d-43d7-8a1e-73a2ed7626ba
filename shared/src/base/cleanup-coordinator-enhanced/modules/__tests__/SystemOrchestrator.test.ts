/**
 * SystemOrchestrator Test Suite
 * Tests system orchestration and coordination functionality
 */

import { SystemOrchestrator } from '../SystemOrchestrator';
import { ITemplateExecution } from '../../../types/CleanupTypes';

describe('SystemOrchestrator', () => {
  let orchestrator: SystemOrchestrator;

  beforeEach(async () => {
    orchestrator = new SystemOrchestrator({
      maxConcurrentOperations: 5,
      defaultTimeout: 30000,
      performanceMonitoringEnabled: true,
      testMode: true // Enable test mode for Jest compatibility
    });
  });

  afterEach(async () => {
    if (orchestrator) {
      await orchestrator.shutdown();
    }
  });

  describe('Initialization and Configuration', () => {
    it('should initialize with default configuration', async () => {
      const defaultOrchestrator = new SystemOrchestrator();
      expect(defaultOrchestrator).toBeDefined();
      await defaultOrchestrator.shutdown();
    });

    it('should initialize with custom configuration', () => {
      expect(orchestrator).toBeDefined();
    });
  });

  describe('System Status Management', () => {
    it('should get system status', () => {
      const status = orchestrator.getSystemStatus();
      
      expect(status).toBeDefined();
      expect(typeof status).toBe('object');
      expect(status.phaseIntegration).toBeDefined();
      expect(status.executions).toBeDefined();
      expect(status.monitoring).toBeDefined();
      expect(status.system).toBeDefined();
    });

    it('should perform health check', async () => {
      const healthCheck = await orchestrator.performHealthCheck();
      
      expect(healthCheck).toBeDefined();
      expect(typeof healthCheck.healthy).toBe('boolean');
      expect(Array.isArray(healthCheck.issues)).toBe(true);
      expect(typeof healthCheck.metrics).toBe('object');
    });

    it('should create system snapshot', async () => {
      const snapshotId = 'test-snapshot';
      const snapshot = await orchestrator.createSystemSnapshot(snapshotId);
      
      expect(snapshot).toBeDefined();
      expect(typeof snapshot).toBe('object');
      expect(snapshot.timestamp).toBeDefined();
      expect(snapshot.activeOperations).toBeDefined();
      expect(snapshot.systemMetrics).toBeDefined();
    });
  });

  describe('Template Execution Tracking', () => {
    it('should register template execution', () => {
      const mockExecution: ITemplateExecution = {
        id: 'test-execution-1',
        templateId: 'test-template',
        targetComponents: ['component1', 'component2'],
        parameters: {},
        startTime: new Date(),
        status: 'running',
        stepResults: new Map(),
        rollbackExecuted: false,
        metrics: {
          totalSteps: 2,
          executedSteps: 0,
          failedSteps: 0,
          skippedSteps: 0,
          averageStepTime: 0,
          longestStepTime: 0,
          dependencyResolutionTime: 0,
          validationTime: 0,
          totalExecutionTime: 0
        }
      };
      
      expect(() => {
        orchestrator.registerTemplateExecution(mockExecution);
      }).not.toThrow();
    });

    it('should update template execution status', () => {
      const mockExecution: ITemplateExecution = {
        id: 'test-execution-2',
        templateId: 'test-template',
        targetComponents: ['component1'],
        parameters: {},
        startTime: new Date(),
        status: 'running',
        stepResults: new Map(),
        rollbackExecuted: false,
        metrics: {
          totalSteps: 1,
          executedSteps: 0,
          failedSteps: 0,
          skippedSteps: 0,
          averageStepTime: 0,
          longestStepTime: 0,
          dependencyResolutionTime: 0,
          validationTime: 0,
          totalExecutionTime: 0
        }
      };
      
      orchestrator.registerTemplateExecution(mockExecution);
      
      expect(() => {
        orchestrator.updateTemplateExecution('test-execution-2', { 
          status: 'completed',
          endTime: new Date()
        });
      }).not.toThrow();
      
      const status = orchestrator.getSystemStatus();
      expect(status.executions.active).toBeGreaterThanOrEqual(0);
    });

    it('should get template metrics', () => {
      const templateId = 'metrics-template';
      const metrics = orchestrator.getTemplateMetrics(templateId);
      
      expect(metrics).toBeDefined();
      expect(typeof metrics).toBe('object');
      expect(metrics.totalSteps).toBeDefined();
      expect(metrics.executedSteps).toBeDefined();
      expect(metrics.failedSteps).toBeDefined();
    });
  });

  describe('System Snapshot Management', () => {
    it('should create and retrieve system snapshot', async () => {
      const snapshotId = 'retrieve-test-snapshot';
      const snapshot = await orchestrator.createSystemSnapshot(snapshotId);
      
      expect(snapshot).toBeDefined();
      
      const retrievedSnapshot = orchestrator.getSystemSnapshot(snapshotId);
      expect(retrievedSnapshot).toBeDefined();
      expect(retrievedSnapshot?.timestamp).toEqual(snapshot.timestamp);
    });

    it('should list system snapshots', async () => {
      await orchestrator.createSystemSnapshot('snapshot-1');
      await orchestrator.createSystemSnapshot('snapshot-2');
      
      const snapshots = orchestrator.listSystemSnapshots();
      expect(Array.isArray(snapshots)).toBe(true);
      expect(snapshots.length).toBeGreaterThanOrEqual(2);
    });

    it('should cleanup old system snapshots', async () => {
      await orchestrator.createSystemSnapshot('old-snapshot');
      
      const oldDate = new Date(Date.now() + 1000); // 1 second in the future to ensure cleanup
      const cleanedCount = await orchestrator.cleanupSystemSnapshots(oldDate);
      
      expect(typeof cleanedCount).toBe('number');
      expect(cleanedCount).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Performance Monitoring', () => {
    it('should monitor system performance', async () => {
      const healthCheck = await orchestrator.performHealthCheck();
      
      expect(healthCheck.metrics).toBeDefined();
      expect(typeof healthCheck.metrics).toBe('object');
    });

    it('should track resource utilization', () => {
      const status = orchestrator.getSystemStatus();
      
      expect(status).toBeDefined();
      expect(status.system).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    it('should handle template execution failures gracefully', () => {
      const mockExecution: ITemplateExecution = {
        id: 'failing-execution',
        templateId: 'failing-template',
        targetComponents: ['component1'],
        parameters: {},
        startTime: new Date(),
        status: 'running',
        stepResults: new Map(),
        rollbackExecuted: false,
        metrics: {
          totalSteps: 1,
          executedSteps: 0,
          failedSteps: 0,
          skippedSteps: 0,
          averageStepTime: 0,
          longestStepTime: 0,
          dependencyResolutionTime: 0,
          validationTime: 0,
          totalExecutionTime: 0
        }
      };
      
      orchestrator.registerTemplateExecution(mockExecution);
      
      expect(() => {
        orchestrator.updateTemplateExecution('failing-execution', { 
          status: 'failed',
          endTime: new Date()
        });
      }).not.toThrow();
    });

    it('should handle shutdown gracefully', async () => {
      await expect(orchestrator.shutdown()).resolves.not.toThrow();
    });
  });

  describe('Resilient Timing Integration', () => {
    it('should initialize resilient timing infrastructure synchronously', () => {
      const newOrchestrator = new SystemOrchestrator({
        defaultTimeout: 25000,
        testMode: true
      });

      // Verify timing infrastructure is available immediately after construction
      expect((newOrchestrator as any)._resilientTimer).toBeDefined();
      expect((newOrchestrator as any)._metricsCollector).toBeDefined();

      newOrchestrator.shutdown();
    });

    it('should handle timing infrastructure initialization failures gracefully', () => {
      // Mock ResilientTimer to throw error during initialization
      const originalTimer = require('../../../utils/ResilientTiming').ResilientTimer;
      require('../../../utils/ResilientTiming').ResilientTimer = jest.fn().mockImplementation(() => {
        throw new Error('Timer initialization failed');
      });

      const newOrchestrator = new SystemOrchestrator({ testMode: true });

      // Should have fallback timing infrastructure
      expect((newOrchestrator as any)._resilientTimer).toBeDefined();
      expect((newOrchestrator as any)._metricsCollector).toBeDefined();

      // Restore original
      require('../../../utils/ResilientTiming').ResilientTimer = originalTimer;

      newOrchestrator.shutdown();
    });

    it('should handle non-Error timing initialization failures', () => {
      // Mock ResilientTimer to throw non-Error object
      const originalTimer = require('../../../utils/ResilientTiming').ResilientTimer;
      require('../../../utils/ResilientTiming').ResilientTimer = jest.fn().mockImplementation(() => {
        throw 'String error instead of Error object';
      });

      const newOrchestrator = new SystemOrchestrator({ testMode: true });

      // Should have fallback timing infrastructure
      expect((newOrchestrator as any)._resilientTimer).toBeDefined();
      expect((newOrchestrator as any)._metricsCollector).toBeDefined();

      // Restore original
      require('../../../utils/ResilientTiming').ResilientTimer = originalTimer;

      newOrchestrator.shutdown();
    });

    it('should reconfigure timing infrastructure during doInitialize', async () => {
      const newOrchestrator = new SystemOrchestrator({
        defaultTimeout: 45000,
        testMode: true
      });

      // Mock reconfigure methods
      const mockReconfigure = jest.fn();
      (newOrchestrator as any)._resilientTimer.reconfigure = mockReconfigure;
      (newOrchestrator as any)._metricsCollector.reconfigure = mockReconfigure;

      await (newOrchestrator as any).doInitialize();

      // Verify reconfigure was called
      expect(mockReconfigure).toHaveBeenCalled();

      await newOrchestrator.shutdown();
    });

    it('should handle timing infrastructure reconfiguration failures', async () => {
      const newOrchestrator = new SystemOrchestrator({
        defaultTimeout: 45000,
        testMode: true
      });

      // Mock reconfigure methods to throw errors
      (newOrchestrator as any)._resilientTimer.reconfigure = jest.fn().mockImplementation(() => {
        throw new Error('Reconfiguration failed');
      });

      // Should not throw during doInitialize
      await expect((newOrchestrator as any).doInitialize()).resolves.not.toThrow();

      await newOrchestrator.shutdown();
    });

    it('should handle non-Error reconfiguration failures', async () => {
      const newOrchestrator = new SystemOrchestrator({
        defaultTimeout: 45000,
        testMode: true
      });

      // Mock reconfigure methods to throw non-Error objects
      (newOrchestrator as any)._resilientTimer.reconfigure = jest.fn().mockImplementation(() => {
        throw 'String error instead of Error object';
      });

      // Should not throw during doInitialize
      await expect((newOrchestrator as any).doInitialize()).resolves.not.toThrow();

      await newOrchestrator.shutdown();
    });
  });

  describe('Lifecycle Management', () => {
    it('should handle doInitialize method', async () => {
      const newOrchestrator = new SystemOrchestrator({
        phaseIntegrationEnabled: true,
        performanceMonitoringEnabled: false,
        testMode: true
      });

      await expect((newOrchestrator as any).doInitialize()).resolves.not.toThrow();

      await newOrchestrator.shutdown();
    });

    it('should handle doShutdown method', async () => {
      const newOrchestrator = new SystemOrchestrator({ testMode: true });

      await (newOrchestrator as any).doInitialize();
      await expect((newOrchestrator as any).doShutdown()).resolves.not.toThrow();
    });

    it('should handle timing infrastructure cleanup during shutdown', async () => {
      const newOrchestrator = new SystemOrchestrator({ testMode: true });

      await (newOrchestrator as any).doInitialize();

      // Verify shutdown completes without errors
      await expect((newOrchestrator as any).doShutdown()).resolves.not.toThrow();
    });

    it('should handle timing infrastructure errors during shutdown', async () => {
      const newOrchestrator = new SystemOrchestrator({ testMode: true });

      // Mock metrics collector to throw error
      (newOrchestrator as any)._metricsCollector.createSnapshot = jest.fn().mockImplementation(() => {
        throw new Error('Metrics snapshot failed');
      });

      await (newOrchestrator as any).doInitialize();

      // Should not throw during shutdown
      await expect((newOrchestrator as any).doShutdown()).resolves.not.toThrow();
    });

    it('should handle non-Error timing infrastructure errors during shutdown', async () => {
      const newOrchestrator = new SystemOrchestrator({ testMode: true });

      // Mock metrics collector to throw non-Error object
      (newOrchestrator as any)._metricsCollector.createSnapshot = jest.fn().mockImplementation(() => {
        throw 'String error instead of Error object';
      });

      await (newOrchestrator as any).doInitialize();

      // Should not throw during shutdown
      await expect((newOrchestrator as any).doShutdown()).resolves.not.toThrow();
    });
  });

  describe('Logging Interface', () => {
    it('should implement ILoggingService interface', () => {
      expect(typeof orchestrator.logInfo).toBe('function');
      expect(typeof orchestrator.logWarning).toBe('function');
      expect(typeof orchestrator.logError).toBe('function');
      expect(typeof orchestrator.logDebug).toBe('function');
    });

    it('should log info messages', () => {
      expect(() => {
        orchestrator.logInfo('Test info message', { test: 'metadata' });
      }).not.toThrow();
    });

    it('should log warning messages', () => {
      expect(() => {
        orchestrator.logWarning('Test warning message', { test: 'metadata' });
      }).not.toThrow();
    });

    it('should log error messages', () => {
      const testError = new Error('Test error');
      expect(() => {
        orchestrator.logError('Test error message', testError, { test: 'metadata' });
      }).not.toThrow();
    });

    it('should log debug messages', () => {
      expect(() => {
        orchestrator.logDebug('Test debug message', { test: 'metadata' });
      }).not.toThrow();
    });
  });

  describe('Phase Integration', () => {
    it('should initialize phase integrations', async () => {
      const newOrchestrator = new SystemOrchestrator({
        phaseIntegrationEnabled: true,
        testMode: true
      });

      await (newOrchestrator as any).doInitialize();

      // Verify phase integration was initialized
      const status = newOrchestrator.getSystemStatus();
      expect(status.phaseIntegration).toBeDefined();

      await newOrchestrator.shutdown();
    });

    it('should handle phase integration with disabled configuration', async () => {
      const newOrchestrator = new SystemOrchestrator({
        phaseIntegrationEnabled: false,
        testMode: true
      });

      await (newOrchestrator as any).doInitialize();

      const status = newOrchestrator.getSystemStatus();
      expect(status.phaseIntegration).toBeDefined();

      await newOrchestrator.shutdown();
    });
  });

  describe('Enhanced Monitoring', () => {
    it('should start enhanced monitoring when enabled and not in test mode', async () => {
      const newOrchestrator = new SystemOrchestrator({
        performanceMonitoringEnabled: true,
        testMode: false // Not in test mode to enable monitoring
      });

      await (newOrchestrator as any).doInitialize();

      // Verify monitoring was started
      const status = newOrchestrator.getSystemStatus();
      expect(status.monitoring).toBeDefined();

      await newOrchestrator.shutdown();
    });

    it('should not start enhanced monitoring in test mode', async () => {
      const newOrchestrator = new SystemOrchestrator({
        performanceMonitoringEnabled: true,
        testMode: true // Test mode should prevent monitoring
      });

      await (newOrchestrator as any).doInitialize();

      const status = newOrchestrator.getSystemStatus();
      expect(status.monitoring).toBeDefined();

      await newOrchestrator.shutdown();
    });

    it('should not start enhanced monitoring when disabled', async () => {
      const newOrchestrator = new SystemOrchestrator({
        performanceMonitoringEnabled: false,
        testMode: false
      });

      await (newOrchestrator as any).doInitialize();

      const status = newOrchestrator.getSystemStatus();
      expect(status.monitoring).toBeDefined();

      await newOrchestrator.shutdown();
    });
  });

  describe('Template Execution Management', () => {
    it('should handle template execution with timing metrics', () => {
      const mockExecution: ITemplateExecution = {
        id: 'timing-execution',
        templateId: 'timing-template',
        targetComponents: ['component1'],
        parameters: { param1: 'value1' },
        startTime: new Date(),
        status: 'running',
        stepResults: new Map([
          ['step1', {
            stepId: 'step1',
            componentId: 'component1',
            executionTime: 100,
            result: { success: true, output: 'result1' },
            success: true,
            error: undefined,
            retryCount: 0,
            skipped: false,
            rollbackRequired: false
          }]
        ]),
        rollbackExecuted: false,
        metrics: {
          totalSteps: 3,
          executedSteps: 1,
          failedSteps: 0,
          skippedSteps: 0,
          averageStepTime: 100,
          longestStepTime: 150,
          dependencyResolutionTime: 50,
          validationTime: 25,
          totalExecutionTime: 275
        }
      };

      orchestrator.registerTemplateExecution(mockExecution);

      // The getTemplateMetrics method returns default values for non-existent templates
      // So we test that it returns the default structure
      const metrics = orchestrator.getTemplateMetrics('timing-template');
      expect(metrics).toBeDefined();
      expect(typeof metrics).toBe('object');
      expect(metrics.totalSteps).toBeDefined();
      expect(metrics.executedSteps).toBeDefined();
    });

    it('should handle template execution updates with complex status changes', () => {
      const mockExecution: ITemplateExecution = {
        id: 'complex-execution',
        templateId: 'complex-template',
        targetComponents: ['component1', 'component2', 'component3'],
        parameters: {
          complexParam: { nested: { value: 'test' } },
          arrayParam: [1, 2, 3]
        },
        startTime: new Date(),
        status: 'running',
        stepResults: new Map(),
        rollbackExecuted: false,
        metrics: {
          totalSteps: 5,
          executedSteps: 0,
          failedSteps: 0,
          skippedSteps: 0,
          averageStepTime: 0,
          longestStepTime: 0,
          dependencyResolutionTime: 0,
          validationTime: 0,
          totalExecutionTime: 0
        }
      };

      orchestrator.registerTemplateExecution(mockExecution);

      // Update through various status changes
      orchestrator.updateTemplateExecution('complex-execution', {
        status: 'running', // Use valid status
        metrics: {
          ...mockExecution.metrics,
          executedSteps: 2
        }
      });

      orchestrator.updateTemplateExecution('complex-execution', {
        status: 'running'
      });

      orchestrator.updateTemplateExecution('complex-execution', {
        status: 'completed',
        endTime: new Date(),
        metrics: {
          ...mockExecution.metrics,
          executedSteps: 5,
          totalExecutionTime: 1500
        }
      });

      const status = orchestrator.getSystemStatus();
      expect(status.executions).toBeDefined();
    });
  });

  describe('Private Method Testing', () => {
    it('should test private _initializePhaseIntegrations method', async () => {
      const newOrchestrator = new SystemOrchestrator({
        phaseIntegrationEnabled: true,
        testMode: true
      });

      // Call private method directly
      await expect((newOrchestrator as any)._initializePhaseIntegrations()).resolves.not.toThrow();

      await newOrchestrator.shutdown();
    });

    it('should test private _startEnhancedMonitoring method', () => {
      const newOrchestrator = new SystemOrchestrator({
        performanceMonitoringEnabled: true,
        testMode: true
      });

      // Call private method directly
      expect(() => {
        (newOrchestrator as any)._startEnhancedMonitoring();
      }).not.toThrow();

      newOrchestrator.shutdown();
    });

    it('should test private _collectEnhancedMetrics method', () => {
      const newOrchestrator = new SystemOrchestrator({ testMode: true });

      expect(() => {
        (newOrchestrator as any)._collectEnhancedMetrics();
      }).not.toThrow();

      newOrchestrator.shutdown();
    });

    it('should test private _monitorTemplateExecutions method', () => {
      const newOrchestrator = new SystemOrchestrator({ testMode: true });

      expect(() => {
        (newOrchestrator as any)._monitorTemplateExecutions();
      }).not.toThrow();

      newOrchestrator.shutdown();
    });

    it('should test private _initializeEnhancedSystems method', () => {
      const newOrchestrator = new SystemOrchestrator({ testMode: true });

      expect(() => {
        (newOrchestrator as any)._initializeEnhancedSystems();
      }).not.toThrow();

      newOrchestrator.shutdown();
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle null template execution gracefully', () => {
      expect(() => {
        orchestrator.registerTemplateExecution(null as any);
      }).toThrow();
    });

    it('should handle undefined template execution gracefully', () => {
      expect(() => {
        orchestrator.registerTemplateExecution(undefined as any);
      }).toThrow();
    });

    it('should handle template execution with missing properties', () => {
      const incompleteExecution = {
        id: 'incomplete-execution',
        templateId: 'incomplete-template'
        // Missing required properties
      } as any;

      expect(() => {
        orchestrator.registerTemplateExecution(incompleteExecution);
      }).toThrow();
    });

    it('should handle update of non-existent template execution', () => {
      expect(() => {
        orchestrator.updateTemplateExecution('non-existent-id', {
          status: 'completed',
          endTime: new Date()
        });
      }).not.toThrow();
    });

    it('should handle metrics request for non-existent template', () => {
      const metrics = orchestrator.getTemplateMetrics('non-existent-template');

      expect(metrics).toBeDefined();
      expect(typeof metrics).toBe('object');
    });

    it('should handle snapshot retrieval for non-existent snapshot', () => {
      const snapshot = orchestrator.getSystemSnapshot('non-existent-snapshot');

      expect(snapshot).toBeUndefined();
    });

    it('should handle cleanup with invalid date', async () => {
      const cleanedCount = await orchestrator.cleanupSystemSnapshots(null as any);

      expect(typeof cleanedCount).toBe('number');
      expect(cleanedCount).toBeGreaterThanOrEqual(0);
    });

    it('should handle very large number of template executions', () => {
      // Register many executions to test performance
      for (let i = 0; i < 100; i++) {
        const mockExecution: ITemplateExecution = {
          id: `bulk-execution-${i}`,
          templateId: `bulk-template-${i % 10}`, // 10 different templates
          targetComponents: [`component${i % 5}`], // 5 different components
          parameters: { index: i },
          startTime: new Date(),
          status: 'running',
          stepResults: new Map(),
          rollbackExecuted: false,
          metrics: {
            totalSteps: 1,
            executedSteps: 0,
            failedSteps: 0,
            skippedSteps: 0,
            averageStepTime: 0,
            longestStepTime: 0,
            dependencyResolutionTime: 0,
            validationTime: 0,
            totalExecutionTime: 0
          }
        };

        orchestrator.registerTemplateExecution(mockExecution);
      }

      const status = orchestrator.getSystemStatus();
      expect(status.executions.active).toBeGreaterThanOrEqual(100);
    });

    it('should handle concurrent template execution updates', () => {
      const executionId = 'concurrent-execution';
      const mockExecution: ITemplateExecution = {
        id: executionId,
        templateId: 'concurrent-template',
        targetComponents: ['component1'],
        parameters: {},
        startTime: new Date(),
        status: 'running',
        stepResults: new Map(),
        rollbackExecuted: false,
        metrics: {
          totalSteps: 1,
          executedSteps: 0,
          failedSteps: 0,
          skippedSteps: 0,
          averageStepTime: 0,
          longestStepTime: 0,
          dependencyResolutionTime: 0,
          validationTime: 0,
          totalExecutionTime: 0
        }
      };

      orchestrator.registerTemplateExecution(mockExecution);

      // Simulate concurrent updates
      expect(() => {
        orchestrator.updateTemplateExecution(executionId, { status: 'running' }); // Use valid status
        orchestrator.updateTemplateExecution(executionId, { status: 'running' });
        orchestrator.updateTemplateExecution(executionId, { status: 'completed', endTime: new Date() });
      }).not.toThrow();
    });

    it('should handle template execution monitoring with running executions', () => {
      // Register a running execution to trigger monitoring logic
      const runningExecution: ITemplateExecution = {
        id: 'running-execution',
        templateId: 'running-template',
        targetComponents: ['component1'],
        parameters: {},
        startTime: new Date(Date.now() - 5000), // Started 5 seconds ago
        status: 'running',
        stepResults: new Map(),
        rollbackExecuted: false,
        metrics: {
          totalSteps: 1,
          executedSteps: 0,
          failedSteps: 0,
          skippedSteps: 0,
          averageStepTime: 0,
          longestStepTime: 0,
          dependencyResolutionTime: 0,
          validationTime: 0,
          totalExecutionTime: 0
        }
      };

      orchestrator.registerTemplateExecution(runningExecution);

      // Call the private monitoring method to trigger the uncovered lines
      expect(() => {
        (orchestrator as any)._monitorTemplateExecutions();
      }).not.toThrow();
    });
  });

  describe('Branch Coverage - Surgical Precision Tests', () => {
    it('should cover fallback timing infrastructure creation', () => {
      // Mock ResilientTimer to throw error and trigger fallback creation
      const originalTimer = require('../../../utils/ResilientTiming').ResilientTimer;
      require('../../../utils/ResilientTiming').ResilientTimer = jest.fn().mockImplementation(() => {
        throw new Error('Timer initialization failed');
      });

      const newOrchestrator = new SystemOrchestrator({ testMode: true });

      // Verify fallback timing infrastructure was created
      const timer = (newOrchestrator as any)._resilientTimer;
      expect(timer).toBeDefined();

      // Test the fallback timer functionality
      const context = timer.start();
      const result = context.end();

      expect(result.duration).toBe(0);
      expect(result.reliable).toBe(false);
      expect(result.method).toBe('fallback');
      expect(result.fallbackUsed).toBe(true);

      // Restore original
      require('../../../utils/ResilientTiming').ResilientTimer = originalTimer;

      newOrchestrator.shutdown();
    });

    it('should cover fallback metrics collector creation', () => {
      // Mock ResilientTimer to throw error and trigger fallback creation
      const originalTimer = require('../../../utils/ResilientTiming').ResilientTimer;
      require('../../../utils/ResilientTiming').ResilientTimer = jest.fn().mockImplementation(() => {
        throw new Error('Timer initialization failed');
      });

      const newOrchestrator = new SystemOrchestrator({ testMode: true });

      // Verify fallback metrics collector was created
      const metricsCollector = (newOrchestrator as any)._metricsCollector;
      expect(metricsCollector).toBeDefined();

      // Test the fallback metrics collector functionality
      expect(() => {
        metricsCollector.recordTiming('test', 100);
      }).not.toThrow();

      const snapshot = metricsCollector.createSnapshot();
      expect(snapshot.metrics).toBeInstanceOf(Map);
      expect(snapshot.reliable).toBe(false);

      // Restore original
      require('../../../utils/ResilientTiming').ResilientTimer = originalTimer;

      newOrchestrator.shutdown();
    });

    it('should cover enhanced monitoring timer creation', async () => {
      const newOrchestrator = new SystemOrchestrator({
        performanceMonitoringEnabled: true,
        testMode: false // Disable test mode to enable monitoring
      });

      await (newOrchestrator as any).doInitialize();

      // Verify monitoring was started
      const status = newOrchestrator.getSystemStatus();
      expect(status.monitoring).toBeDefined();

      await newOrchestrator.shutdown();
    });

    it('should cover template execution status filtering', () => {
      // Register executions with different statuses
      const completedExecution: ITemplateExecution = {
        id: 'completed-execution',
        templateId: 'completed-template',
        targetComponents: ['component1'],
        parameters: {},
        startTime: new Date(),
        status: 'completed',
        stepResults: new Map(),
        rollbackExecuted: false,
        metrics: {
          totalSteps: 1,
          executedSteps: 1,
          failedSteps: 0,
          skippedSteps: 0,
          averageStepTime: 100,
          longestStepTime: 100,
          dependencyResolutionTime: 0,
          validationTime: 0,
          totalExecutionTime: 100
        }
      };

      const runningExecution: ITemplateExecution = {
        id: 'running-execution-2',
        templateId: 'running-template-2',
        targetComponents: ['component1'],
        parameters: {},
        startTime: new Date(),
        status: 'running',
        stepResults: new Map(),
        rollbackExecuted: false,
        metrics: {
          totalSteps: 1,
          executedSteps: 0,
          failedSteps: 0,
          skippedSteps: 0,
          averageStepTime: 0,
          longestStepTime: 0,
          dependencyResolutionTime: 0,
          validationTime: 0,
          totalExecutionTime: 0
        }
      };

      orchestrator.registerTemplateExecution(completedExecution);
      orchestrator.registerTemplateExecution(runningExecution);

      // Call monitoring to trigger the filtering logic
      expect(() => {
        (orchestrator as any)._monitorTemplateExecutions();
      }).not.toThrow();
    });

    it('should cover undefined defaultTimeout configuration', () => {
      const newOrchestrator = new SystemOrchestrator({
        defaultTimeout: undefined,
        testMode: true
      });

      // Should use the fallback value of 30000
      expect(newOrchestrator).toBeDefined();

      newOrchestrator.shutdown();
    });

    it('should cover line 306 - timer cleanup in doShutdown', async () => {
      // Use Jest fake timers to control timer creation and cleanup
      jest.useFakeTimers();

      const newOrchestrator = new SystemOrchestrator({
        performanceMonitoringEnabled: true,
        testMode: false // Enable monitoring to create timers
      });

      await (newOrchestrator as any).doInitialize();

      // Manually add timer IDs to ensure line 306 is executed
      const monitoringTimerIds = (newOrchestrator as any)._monitoringTimerIds;
      monitoringTimerIds.push(1); // Add a fake timer ID
      monitoringTimerIds.push(2); // Add another fake timer ID

      // This should trigger line 306 - clearInterval(timerId) for each timer
      await (newOrchestrator as any).doShutdown();

      // Restore real timers
      jest.useRealTimers();
    });

    it('should cover line 357 - error handling in phase integration initialization', async () => {
      const newOrchestrator = new SystemOrchestrator({
        phaseIntegrationEnabled: true,
        testMode: true
      });

      // Mock Promise.resolve to throw error and trigger catch block
      const originalResolve = Promise.resolve;
      Promise.resolve = jest.fn().mockImplementation(() => {
        throw new Error('Phase integration initialization failed');
      });

      // This should trigger line 357 - error handling in _initializePhaseIntegrations
      await expect((newOrchestrator as any)._initializePhaseIntegrations()).resolves.not.toThrow();

      // Restore original
      Promise.resolve = originalResolve;

      await newOrchestrator.shutdown();
    });

    it('should cover lines 399-402 - error handling in enhanced metrics collection', async () => {
      // Use Jest fake timers to control interval execution
      jest.useFakeTimers();

      const newOrchestrator = new SystemOrchestrator({
        performanceMonitoringEnabled: true,
        testMode: false // Enable monitoring to create intervals
      });

      // Mock _collectEnhancedMetrics to throw error
      (newOrchestrator as any)._collectEnhancedMetrics = jest.fn().mockImplementation(() => {
        throw new Error('Enhanced metrics collection failed');
      });

      await (newOrchestrator as any).doInitialize();

      // Fast-forward time to trigger the interval
      jest.advanceTimersByTime(60000); // 1 minute

      await newOrchestrator.shutdown();

      // Restore real timers
      jest.useRealTimers();
    });

    it('should cover lines 411-414 - error handling in template execution monitoring', async () => {
      // Use Jest fake timers to control interval execution
      jest.useFakeTimers();

      const newOrchestrator = new SystemOrchestrator({
        performanceMonitoringEnabled: true,
        testMode: false // Enable monitoring to create intervals
      });

      // Mock _monitorTemplateExecutions to throw error
      (newOrchestrator as any)._monitorTemplateExecutions = jest.fn().mockImplementation(() => {
        throw new Error('Template execution monitoring failed');
      });

      await (newOrchestrator as any).doInitialize();

      // Fast-forward time to trigger the interval
      jest.advanceTimersByTime(30000); // 30 seconds

      await newOrchestrator.shutdown();

      // Restore real timers
      jest.useRealTimers();
    });

    it('should cover lines 477-479 - long-running execution detection', () => {
      // Register a long-running execution (started more than 5 minutes ago)
      const longRunningExecution: ITemplateExecution = {
        id: 'long-running-execution',
        templateId: 'long-running-template',
        targetComponents: ['component1'],
        parameters: {},
        startTime: new Date(Date.now() - 6 * 60 * 1000), // 6 minutes ago
        status: 'running',
        stepResults: new Map(),
        rollbackExecuted: false,
        metrics: {
          totalSteps: 1,
          executedSteps: 0,
          failedSteps: 0,
          skippedSteps: 0,
          averageStepTime: 0,
          longestStepTime: 0,
          dependencyResolutionTime: 0,
          validationTime: 0,
          totalExecutionTime: 0
        }
      };

      orchestrator.registerTemplateExecution(longRunningExecution);

      // This should trigger lines 477-479 - long-running execution warning
      expect(() => {
        (orchestrator as any)._monitorTemplateExecutions();
      }).not.toThrow();
    });

    it('should cover lines 555-559 - getTemplateMetrics all metrics return', () => {
      // Add some template metrics first
      const templateMetrics = {
        totalSteps: 5,
        executedSteps: 3,
        failedSteps: 1,
        skippedSteps: 1,
        averageStepTime: 150,
        longestStepTime: 300,
        dependencyResolutionTime: 50,
        validationTime: 25,
        totalExecutionTime: 750
      };

      (orchestrator as any)._templateMetrics.set('template1', templateMetrics);
      (orchestrator as any)._templateMetrics.set('template2', templateMetrics);

      // Call without templateId to get all metrics - triggers lines 555-559
      const allMetrics = orchestrator.getTemplateMetrics();

      expect(typeof allMetrics).toBe('object');
      expect(allMetrics).toHaveProperty('template1');
      expect(allMetrics).toHaveProperty('template2');
    });
  });

  describe('Advanced Coverage - Complex Scenarios', () => {
    it('should cover lines 565-594 - updateTemplateMetrics with existing metrics', () => {
      const templateId = 'metrics-update-template';

      // Set existing metrics first
      const existingMetrics = {
        totalSteps: 3,
        executedSteps: 2,
        failedSteps: 1,
        skippedSteps: 0,
        averageStepTime: 100,
        longestStepTime: 200,
        dependencyResolutionTime: 30,
        validationTime: 20,
        totalExecutionTime: 300
      };
      (orchestrator as any)._templateMetrics.set(templateId, existingMetrics);

      // Create execution with step results to trigger complex metrics calculation
      const execution: ITemplateExecution = {
        id: 'metrics-execution',
        templateId: templateId,
        targetComponents: ['component1'],
        parameters: {},
        startTime: new Date(Date.now() - 5000),
        endTime: new Date(), // Has end time for execution time calculation
        status: 'completed',
        stepResults: new Map([
          ['step1', {
            stepId: 'step1',
            componentId: 'component1',
            executionTime: 150,
            result: { success: true },
            success: true,
            error: undefined,
            retryCount: 0,
            skipped: false,
            rollbackRequired: false
          }],
          ['step2', {
            stepId: 'step2',
            componentId: 'component1',
            executionTime: 200,
            result: { success: false },
            success: false,
            error: new Error('Step failed'),
            retryCount: 1,
            skipped: false,
            rollbackRequired: true
          }],
          ['step3', {
            stepId: 'step3',
            componentId: 'component1',
            executionTime: 0,
            result: { skipped: true },
            success: true,
            error: undefined,
            retryCount: 0,
            skipped: true,
            rollbackRequired: false
          }]
        ]),
        rollbackExecuted: false,
        metrics: {
          totalSteps: 3,
          executedSteps: 2,
          failedSteps: 1,
          skippedSteps: 1,
          averageStepTime: 175,
          longestStepTime: 200,
          dependencyResolutionTime: 40,
          validationTime: 30,
          totalExecutionTime: 5000
        }
      };

      // This should trigger lines 565-594 - complex metrics update with existing metrics
      expect(() => {
        orchestrator.updateTemplateMetrics(templateId, execution);
      }).not.toThrow();

      const updatedMetrics = orchestrator.getTemplateMetrics(templateId);
      expect(updatedMetrics).toBeDefined();
    });

    it('should cover line 715 - high number of active executions health check', async () => {
      // Register more than 50 active executions to trigger the health check warning
      for (let i = 0; i < 55; i++) {
        const execution: ITemplateExecution = {
          id: `active-execution-${i}`,
          templateId: `active-template-${i}`,
          targetComponents: ['component1'],
          parameters: {},
          startTime: new Date(),
          status: 'running', // Keep them running to count as active
          stepResults: new Map(),
          rollbackExecuted: false,
          metrics: {
            totalSteps: 1,
            executedSteps: 0,
            failedSteps: 0,
            skippedSteps: 0,
            averageStepTime: 0,
            longestStepTime: 0,
            dependencyResolutionTime: 0,
            validationTime: 0,
            totalExecutionTime: 0
          }
        };
        orchestrator.registerTemplateExecution(execution);
      }

      // This should trigger line 715 - high number of active executions warning
      const healthCheck = await orchestrator.performHealthCheck();

      expect(healthCheck.issues).toContain('High number of active executions may impact performance');
    });

    it('should cover line 720 - high memory usage health check', async () => {
      // Mock process.memoryUsage to return high memory usage
      const originalMemoryUsage = process.memoryUsage;
      (process as any).memoryUsage = jest.fn().mockReturnValue({
        rss: 600 * 1024 * 1024,
        heapTotal: 600 * 1024 * 1024,
        heapUsed: 600 * 1024 * 1024, // 600MB - above 500MB threshold
        external: 0,
        arrayBuffers: 0
      });

      // This should trigger line 720 - high memory usage warning
      const healthCheck = await orchestrator.performHealthCheck();

      expect(healthCheck.issues).toContain('High memory usage detected');

      // Restore original
      (process as any).memoryUsage = originalMemoryUsage;
    });

    it('should cover line 724 - large number of snapshots health check', async () => {
      // Create more than 100 system snapshots to trigger the health check warning
      for (let i = 0; i < 105; i++) {
        await orchestrator.createSystemSnapshot(`snapshot-${i}`);
      }

      // This should trigger line 724 - large number of snapshots warning
      const healthCheck = await orchestrator.performHealthCheck();

      expect(healthCheck.issues).toContain('Large number of system snapshots - consider cleanup');
    });

    it('should cover non-Error object handling in phase integration', async () => {
      const newOrchestrator = new SystemOrchestrator({
        phaseIntegrationEnabled: true,
        testMode: true
      });

      // Mock Promise.resolve to throw non-Error object
      const originalResolve = Promise.resolve;
      Promise.resolve = jest.fn().mockImplementation(() => {
        throw 'String error instead of Error object'; // Non-Error object
      });

      // This should trigger error handling with non-Error object
      await expect((newOrchestrator as any)._initializePhaseIntegrations()).resolves.not.toThrow();

      // Restore original
      Promise.resolve = originalResolve;

      await newOrchestrator.shutdown();
    });

    it('should cover non-Error object handling in metrics collection', async () => {
      // Use Jest fake timers to control interval execution
      jest.useFakeTimers();

      const newOrchestrator = new SystemOrchestrator({
        performanceMonitoringEnabled: true,
        testMode: false
      });

      // Mock _collectEnhancedMetrics to throw non-Error object
      (newOrchestrator as any)._collectEnhancedMetrics = jest.fn().mockImplementation(() => {
        throw 'String error instead of Error object'; // Non-Error object
      });

      await (newOrchestrator as any).doInitialize();

      // Fast-forward time to trigger the interval
      jest.advanceTimersByTime(60000); // 1 minute

      await newOrchestrator.shutdown();

      // Restore real timers
      jest.useRealTimers();
    });

    it('should cover non-Error object handling in template monitoring', async () => {
      // Use Jest fake timers to control interval execution
      jest.useFakeTimers();

      const newOrchestrator = new SystemOrchestrator({
        performanceMonitoringEnabled: true,
        testMode: false
      });

      // Mock _monitorTemplateExecutions to throw non-Error object
      (newOrchestrator as any)._monitorTemplateExecutions = jest.fn().mockImplementation(() => {
        throw 'String error instead of Error object'; // Non-Error object
      });

      await (newOrchestrator as any).doInitialize();

      // Fast-forward time to trigger the interval
      jest.advanceTimersByTime(30000); // 30 seconds

      await newOrchestrator.shutdown();

      // Restore real timers
      jest.useRealTimers();
    });
  });

  describe('Phase 1: 100% Line Coverage - Interval Error Handling', () => {
    it('should cover lines 399-402 - enhanced metrics collection interval error handling', async () => {
      // Use Jest fake timers to control interval execution
      jest.useFakeTimers();

      const newOrchestrator = new SystemOrchestrator({
        performanceMonitoringEnabled: true,
        testMode: false, // Enable monitoring to create intervals
        cleanupIntervalMs: 60000 // Set specific interval for predictable timing
      });

      // Initialize to create the intervals
      await (newOrchestrator as any).doInitialize();

      // Mock _collectEnhancedMetrics to throw Error object
      const originalCollectMetrics = (newOrchestrator as any)._collectEnhancedMetrics;
      (newOrchestrator as any)._collectEnhancedMetrics = jest.fn().mockImplementation(() => {
        throw new Error('Enhanced metrics collection failed - Error object');
      });

      // Fast-forward time to trigger the metrics collection interval (line 399-402)
      jest.advanceTimersByTime(60000); // 1 minute - matches cleanupIntervalMs

      // Verify the mock was called (confirming interval executed)
      expect((newOrchestrator as any)._collectEnhancedMetrics).toHaveBeenCalled();

      // Restore original method
      (newOrchestrator as any)._collectEnhancedMetrics = originalCollectMetrics;

      await newOrchestrator.shutdown();
      jest.useRealTimers();
    });

    it('should cover lines 399-402 - enhanced metrics collection interval non-Error handling', async () => {
      // Use Jest fake timers to control interval execution
      jest.useFakeTimers();

      const newOrchestrator = new SystemOrchestrator({
        performanceMonitoringEnabled: true,
        testMode: false, // Enable monitoring to create intervals
        cleanupIntervalMs: 60000 // Set specific interval for predictable timing
      });

      // Initialize to create the intervals
      await (newOrchestrator as any).doInitialize();

      // Mock _collectEnhancedMetrics to throw non-Error object
      const originalCollectMetrics = (newOrchestrator as any)._collectEnhancedMetrics;
      (newOrchestrator as any)._collectEnhancedMetrics = jest.fn().mockImplementation(() => {
        throw 'String error instead of Error object'; // Non-Error object
      });

      // Fast-forward time to trigger the metrics collection interval (line 399-402)
      jest.advanceTimersByTime(60000); // 1 minute - matches cleanupIntervalMs

      // Verify the mock was called (confirming interval executed)
      expect((newOrchestrator as any)._collectEnhancedMetrics).toHaveBeenCalled();

      // Restore original method
      (newOrchestrator as any)._collectEnhancedMetrics = originalCollectMetrics;

      await newOrchestrator.shutdown();
      jest.useRealTimers();
    });

    it('should cover lines 411-414 - template execution monitoring interval error handling', async () => {
      // Use Jest fake timers to control interval execution
      jest.useFakeTimers();

      const newOrchestrator = new SystemOrchestrator({
        performanceMonitoringEnabled: true,
        testMode: false, // Enable monitoring to create intervals
        cleanupIntervalMs: 60000 // Set specific interval for predictable timing
      });

      // Initialize to create the intervals
      await (newOrchestrator as any).doInitialize();

      // Mock _monitorTemplateExecutions to throw Error object
      const originalMonitorExecutions = (newOrchestrator as any)._monitorTemplateExecutions;
      (newOrchestrator as any)._monitorTemplateExecutions = jest.fn().mockImplementation(() => {
        throw new Error('Template execution monitoring failed - Error object');
      });

      // Fast-forward time to trigger the monitoring interval (line 411-414)
      // Monitoring interval is half of cleanup interval: 30000ms
      jest.advanceTimersByTime(30000); // 30 seconds - matches monitoring interval

      // Verify the mock was called (confirming interval executed)
      expect((newOrchestrator as any)._monitorTemplateExecutions).toHaveBeenCalled();

      // Restore original method
      (newOrchestrator as any)._monitorTemplateExecutions = originalMonitorExecutions;

      await newOrchestrator.shutdown();
      jest.useRealTimers();
    });

    it('should cover lines 411-414 - template execution monitoring interval non-Error handling', async () => {
      // Use Jest fake timers to control interval execution
      jest.useFakeTimers();

      const newOrchestrator = new SystemOrchestrator({
        performanceMonitoringEnabled: true,
        testMode: false, // Enable monitoring to create intervals
        cleanupIntervalMs: 60000 // Set specific interval for predictable timing
      });

      // Initialize to create the intervals
      await (newOrchestrator as any).doInitialize();

      // Mock _monitorTemplateExecutions to throw non-Error object
      const originalMonitorExecutions = (newOrchestrator as any)._monitorTemplateExecutions;
      (newOrchestrator as any)._monitorTemplateExecutions = jest.fn().mockImplementation(() => {
        throw { message: 'Object error instead of Error instance' }; // Non-Error object
      });

      // Fast-forward time to trigger the monitoring interval (line 411-414)
      // Monitoring interval is half of cleanup interval: 30000ms
      jest.advanceTimersByTime(30000); // 30 seconds - matches monitoring interval

      // Verify the mock was called (confirming interval executed)
      expect((newOrchestrator as any)._monitorTemplateExecutions).toHaveBeenCalled();

      // Restore original method
      (newOrchestrator as any)._monitorTemplateExecutions = originalMonitorExecutions;

      await newOrchestrator.shutdown();
      jest.useRealTimers();
    });

    it('should cover interval error handling with multiple interval executions', async () => {
      // Use Jest fake timers to control interval execution
      jest.useFakeTimers();

      const newOrchestrator = new SystemOrchestrator({
        performanceMonitoringEnabled: true,
        testMode: false, // Enable monitoring to create intervals
        cleanupIntervalMs: 30000 // Shorter interval for faster testing
      });

      // Initialize to create the intervals
      await (newOrchestrator as any).doInitialize();

      // Mock both methods to throw errors
      const originalCollectMetrics = (newOrchestrator as any)._collectEnhancedMetrics;
      const originalMonitorExecutions = (newOrchestrator as any)._monitorTemplateExecutions;

      (newOrchestrator as any)._collectEnhancedMetrics = jest.fn().mockImplementation(() => {
        throw new Error('Metrics collection error');
      });

      (newOrchestrator as any)._monitorTemplateExecutions = jest.fn().mockImplementation(() => {
        throw new Error('Template monitoring error');
      });

      // Fast-forward time to trigger both intervals multiple times
      jest.advanceTimersByTime(15000); // 15 seconds - triggers monitoring interval
      jest.advanceTimersByTime(15000); // 30 seconds total - triggers both intervals
      jest.advanceTimersByTime(30000); // 60 seconds total - triggers both intervals again

      // Verify both mocks were called multiple times
      expect((newOrchestrator as any)._collectEnhancedMetrics).toHaveBeenCalled();
      expect((newOrchestrator as any)._monitorTemplateExecutions).toHaveBeenCalled();

      // Restore original methods
      (newOrchestrator as any)._collectEnhancedMetrics = originalCollectMetrics;
      (newOrchestrator as any)._monitorTemplateExecutions = originalMonitorExecutions;

      await newOrchestrator.shutdown();
      jest.useRealTimers();
    });
  });

  describe('Phase 2: Branch Coverage Improvement - Surgical Precision', () => {
    it('should cover line 251 - defaultTimeout undefined branch in reconfigure', async () => {
      const newOrchestrator = new SystemOrchestrator({
        defaultTimeout: undefined, // Force undefined to test || 30000 branch
        testMode: true
      });

      // Mock reconfigure to verify it's called with the fallback value
      const mockReconfigure = jest.fn();
      (newOrchestrator as any)._resilientTimer.reconfigure = mockReconfigure;

      await (newOrchestrator as any).doInitialize();

      // Verify reconfigure was called with fallback value 30000
      expect(mockReconfigure).toHaveBeenCalledWith(expect.objectContaining({
        maxExpectedDuration: 30000 // Should use fallback when defaultTimeout is undefined
      }));

      await newOrchestrator.shutdown();
    });

    it('should cover line 374 - process.memoryUsage().heapUsed fallback branch', () => {
      // Mock process.memoryUsage to return undefined heapUsed
      const originalMemoryUsage = process.memoryUsage;
      (process as any).memoryUsage = jest.fn().mockReturnValue({
        rss: 100 * 1024 * 1024,
        heapTotal: 100 * 1024 * 1024,
        heapUsed: undefined, // Force undefined to test || 0 branch
        external: 0,
        arrayBuffers: 0
      });

      const newOrchestrator = new SystemOrchestrator({ testMode: true });

      // Call _initializeEnhancedSystems to trigger line 374
      expect(() => {
        (newOrchestrator as any)._initializeEnhancedSystems();
      }).not.toThrow();

      // Restore original
      (process as any).memoryUsage = originalMemoryUsage;

      newOrchestrator.shutdown();
    });

    it('should cover line 405 - cleanupIntervalMs undefined branch in Math.max', async () => {
      const newOrchestrator = new SystemOrchestrator({
        performanceMonitoringEnabled: true,
        testMode: false,
        cleanupIntervalMs: undefined // Force undefined to test || 60000 branch
      });

      await (newOrchestrator as any).doInitialize();

      // The Math.max should use 60000 as fallback when cleanupIntervalMs is undefined
      const status = newOrchestrator.getSystemStatus();
      expect(status.monitoring).toBeDefined();

      await newOrchestrator.shutdown();
    });

    it('should cover line 417 - cleanupIntervalMs undefined branch in monitoring interval', async () => {
      const newOrchestrator = new SystemOrchestrator({
        performanceMonitoringEnabled: true,
        testMode: false,
        cleanupIntervalMs: undefined // Force undefined to test || 60000 branch
      });

      await (newOrchestrator as any).doInitialize();

      // The monitoring interval calculation should use 60000 as fallback
      // (this._config.cleanupIntervalMs || 60000) / 2 = 30000
      const status = newOrchestrator.getSystemStatus();
      expect(status.monitoring).toBeDefined();

      await newOrchestrator.shutdown();
    });

    it('should cover line 439 - process.memoryUsage().heapUsed fallback in metrics collection', () => {
      // Mock process.memoryUsage to return undefined heapUsed
      const originalMemoryUsage = process.memoryUsage;
      (process as any).memoryUsage = jest.fn().mockReturnValue({
        rss: 100 * 1024 * 1024,
        heapTotal: 100 * 1024 * 1024,
        heapUsed: undefined, // Force undefined to test || 0 branch
        external: 0,
        arrayBuffers: 0
      });

      const newOrchestrator = new SystemOrchestrator({ testMode: true });

      // Call _collectEnhancedMetrics to trigger line 439
      expect(() => {
        (newOrchestrator as any)._collectEnhancedMetrics();
      }).not.toThrow();

      // Restore original
      (process as any).memoryUsage = originalMemoryUsage;

      newOrchestrator.shutdown();
    });

    it('should cover line 567 - execution.endTime undefined branch in ternary operator', () => {
      const templateId = 'ternary-test-template';

      // Create execution without endTime to test the ternary operator
      const execution: ITemplateExecution = {
        id: 'ternary-execution',
        templateId: templateId,
        targetComponents: ['component1'],
        parameters: {},
        startTime: new Date(),
        endTime: undefined, // Force undefined to test ternary operator
        status: 'running',
        stepResults: new Map(),
        rollbackExecuted: false,
        metrics: {
          totalSteps: 1,
          executedSteps: 0,
          failedSteps: 0,
          skippedSteps: 0,
          averageStepTime: 0,
          longestStepTime: 0,
          dependencyResolutionTime: 0,
          validationTime: 0,
          totalExecutionTime: 0
        }
      };

      // This should trigger line 567 - execution.endTime ? ... : 0
      expect(() => {
        orchestrator.updateTemplateMetrics(templateId, execution);
      }).not.toThrow();
    });

    it('should cover lines 576-578 - stepResults.length === 0 branches in ternary operators', () => {
      const templateId = 'empty-steps-template';

      // Create execution with empty stepResults to test ternary operators
      const execution: ITemplateExecution = {
        id: 'empty-steps-execution',
        templateId: templateId,
        targetComponents: ['component1'],
        parameters: {},
        startTime: new Date(),
        endTime: new Date(),
        status: 'completed',
        stepResults: new Map(), // Empty stepResults to test stepResults.length > 0 ? ... : 0
        rollbackExecuted: false,
        metrics: {
          totalSteps: 0,
          executedSteps: 0,
          failedSteps: 0,
          skippedSteps: 0,
          averageStepTime: 0,
          longestStepTime: 0,
          dependencyResolutionTime: 0,
          validationTime: 0,
          totalExecutionTime: 0
        }
      };

      // This should trigger lines 576-578 - stepResults.length > 0 ? ... : 0
      expect(() => {
        orchestrator.updateTemplateMetrics(templateId, execution);
      }).not.toThrow();
    });

    it('should cover line 718 - process.memoryUsage().heapUsed fallback in health check', async () => {
      // Mock process.memoryUsage to return undefined heapUsed
      const originalMemoryUsage = process.memoryUsage;
      (process as any).memoryUsage = jest.fn().mockReturnValue({
        rss: 100 * 1024 * 1024,
        heapTotal: 100 * 1024 * 1024,
        heapUsed: undefined, // Force undefined to test || 0 branch
        external: 0,
        arrayBuffers: 0
      });

      // This should trigger line 718 - process.memoryUsage().heapUsed || 0
      const healthCheck = await orchestrator.performHealthCheck();

      expect(healthCheck).toBeDefined();
      expect(healthCheck.healthy).toBeDefined();

      // Restore original
      (process as any).memoryUsage = originalMemoryUsage;
    });
  });
});