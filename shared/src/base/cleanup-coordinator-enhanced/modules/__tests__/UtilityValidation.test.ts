/**
 * @file UtilityValidation Comprehensive Test Suite
 * @description Comprehensive test coverage for UtilityValidation.ts using surgical precision testing methodologies
 * @coverage-target 100% lines, 100% branches, 100% functions
 * @testing-approach Surgical precision testing with direct method access and strategic error injection
 * @compliance MEM-SAFE-002, Anti-Simplification Policy, Enterprise-grade quality
 */

import {
  validateTemplate,
  evaluateStepCondition,
  validateConfigurationCompleteness,
  ValidationUtils,
  UtilityValidationEnhanced
} from '../UtilityValidation';

import {
  ICleanupTemplate,
  IValidationResult,
  IValidationIssue,
  IStepCondition,
  IStepExecutionContext,
  IEnhancedCleanupConfig,
  ICleanupTemplateStep
} from '../../../types/CleanupTypes';

import { CleanupOperationType, CleanupPriority } from '../../../CleanupCoordinatorEnhanced';

import { DEFAULT_TEMPLATE_CONSTANTS } from '../CleanupConfiguration';

// Mock resilient timing infrastructure - must be done before importing the module
jest.mock('../../../utils/ResilientTiming', () => ({
  ResilientTimer: jest.fn().mockImplementation(() => ({
    start: jest.fn().mockReturnValue({
      end: jest.fn().mockReturnValue({
        duration: 150,
        reliable: true,
        method: 'performance'
      })
    })
  }))
}));

jest.mock('../../../utils/ResilientMetrics', () => ({
  ResilientMetricsCollector: jest.fn().mockImplementation(() => ({
    recordTiming: jest.fn()
  }))
}));

describe('UtilityValidation', () => {
  // Helper functions to access mocked instances
  const getMockTimer = () => require('../../../utils/ResilientTiming').ResilientTimer;
  const getMockMetrics = () => require('../../../utils/ResilientMetrics').ResilientMetricsCollector;

  // Test data setup
  const createMockTemplate = (overrides: Partial<ICleanupTemplate> = {}): ICleanupTemplate => ({
    id: 'test-template-123',
    name: 'Test Template',
    description: 'Test template for validation',
    version: '1.0.0',
    operations: [
      {
        id: 'step-1',
        type: CleanupOperationType.RESOURCE_CLEANUP,
        componentPattern: '^test-.*',
        operationName: 'test-operation',
        parameters: {},
        timeout: 5000,
        retryPolicy: {
          maxRetries: 3,
          retryDelay: 1000,
          backoffMultiplier: 2,
          maxRetryDelay: 10000,
          retryOnErrors: ['error']
        },
        dependsOn: [],
        rollbackOperation: 'test-rollback',
        priority: CleanupPriority.NORMAL,
        estimatedDuration: 5000,
        description: 'Test step'
      }
    ],
    conditions: [],
    rollbackSteps: [],
    metadata: {
      customField: 'test'
    },
    tags: ['test'],
    createdAt: new Date(),
    modifiedAt: new Date(),
    author: 'test-author',
    validationRules: [],
    ...overrides
  });

  const createMockStepCondition = (overrides: Partial<IStepCondition> = {}): IStepCondition => ({
    type: 'always',
    ...overrides
  });

  const createMockTemplateStep = (overrides: Partial<ICleanupTemplateStep> = {}): ICleanupTemplateStep => ({
    id: 'test-step',
    type: CleanupOperationType.RESOURCE_CLEANUP,
    componentPattern: '^test-.*',
    operationName: 'test-operation',
    parameters: {},
    timeout: 5000,
    retryPolicy: {
      maxRetries: 3,
      retryDelay: 1000,
      backoffMultiplier: 2,
      maxRetryDelay: 10000,
      retryOnErrors: ['error']
    },
    dependsOn: [],
    rollbackOperation: 'test-rollback',
    priority: CleanupPriority.NORMAL,
    estimatedDuration: 5000,
    description: 'Test step',
    ...overrides
  });

  const createMockExecutionContext = (overrides: Partial<IStepExecutionContext> = {}): IStepExecutionContext => ({
    stepId: 'test-step',
    templateId: 'test-template',
    executionId: 'test-execution',
    componentId: 'test-component',
    parameters: {},
    previousResults: new Map(),
    executionAttempt: 1,
    startTime: new Date(),
    globalContext: {
      executionId: 'test-execution',
      templateId: 'test-template',
      targetComponents: ['component1', 'component2'],
      parameters: {},
      systemState: {},
      timestamp: new Date()
    },
    ...overrides
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('🔍 Template Validation', () => {
    describe('validateTemplate', () => {
      it('should validate a correct template successfully', () => {
        const template = createMockTemplate();
        const result = validateTemplate(template);

        expect(result.valid).toBe(true);
        expect(result.issues).toHaveLength(0);
        expect(result.warnings).toHaveLength(0);
        expect(result.suggestions).toHaveLength(0);
      });

      it('should detect invalid template ID (too short)', () => {
        const template = createMockTemplate({ id: 'ab' }); // Less than minimum length
        const result = validateTemplate(template);

        expect(result.valid).toBe(false);
        expect(result.issues).toHaveLength(1);
        expect(result.issues[0].type).toBe('invalid_id');
        expect(result.issues[0].severity).toBe('error');
        expect(result.issues[0].message).toContain(`at least ${DEFAULT_TEMPLATE_CONSTANTS.MIN_TEMPLATE_ID_LENGTH} characters`);
      });

      it('should detect missing template ID', () => {
        const template = createMockTemplate({ id: '' });
        const result = validateTemplate(template);

        expect(result.valid).toBe(false);
        expect(result.issues.some(issue => issue.type === 'invalid_id')).toBe(true);
      });

      it('should detect invalid template name (too long)', () => {
        const longName = 'a'.repeat(DEFAULT_TEMPLATE_CONSTANTS.MAX_TEMPLATE_NAME_LENGTH + 1);
        const template = createMockTemplate({ name: longName });
        const result = validateTemplate(template);

        expect(result.valid).toBe(false);
        expect(result.issues).toHaveLength(1);
        expect(result.issues[0].type).toBe('invalid_name');
        expect(result.issues[0].severity).toBe('error');
        expect(result.issues[0].message).toContain(`≤${DEFAULT_TEMPLATE_CONSTANTS.MAX_TEMPLATE_NAME_LENGTH} characters`);
      });

      it('should detect missing template name', () => {
        const template = createMockTemplate({ name: '' });
        const result = validateTemplate(template);

        expect(result.valid).toBe(false);
        expect(result.issues.some(issue => issue.type === 'invalid_name')).toBe(true);
      });

      it('should detect missing operations', () => {
        const template = createMockTemplate({ operations: [] });
        const result = validateTemplate(template);

        expect(result.valid).toBe(false);
        expect(result.issues).toHaveLength(1);
        expect(result.issues[0].type).toBe('no_operations');
        expect(result.issues[0].severity).toBe('error');
        expect(result.issues[0].message).toBe('Template must contain at least one operation');
      });

      it('should detect undefined operations', () => {
        const template = createMockTemplate({ operations: undefined as any });
        const result = validateTemplate(template);

        expect(result.valid).toBe(false);
        expect(result.issues.some(issue => issue.type === 'no_operations')).toBe(true);
      });
    });
  });

  describe('🎯 Step Condition Evaluation', () => {
    describe('evaluateStepCondition', () => {
      it('should evaluate "always" condition as true', () => {
        const condition = createMockStepCondition({ type: 'always' });
        const context = createMockExecutionContext();

        const result = evaluateStepCondition(condition, context);
        expect(result).toBe(true);
      });

      it('should evaluate "on_success" condition with no previous results', () => {
        const condition = createMockStepCondition({ type: 'on_success' });
        const context = createMockExecutionContext({ previousResults: new Map() });

        const result = evaluateStepCondition(condition, context);
        expect(result).toBe(true);
      });

      it('should evaluate "on_success" condition with all successful results', () => {
        const condition = createMockStepCondition({ type: 'on_success' });
        const previousResults = new Map([
          ['step1', { success: true, result: 'ok' }],
          ['step2', { success: true, result: 'ok' }]
        ]);
        const context = createMockExecutionContext({ previousResults });

        const result = evaluateStepCondition(condition, context);
        expect(result).toBe(true);
      });

      it('should evaluate "on_success" condition with some failed results', () => {
        const condition = createMockStepCondition({ type: 'on_success' });
        const previousResults = new Map([
          ['step1', { success: true, result: 'ok' }],
          ['step2', { success: false, result: 'error' }]
        ]);
        const context = createMockExecutionContext({ previousResults });

        const result = evaluateStepCondition(condition, context);
        expect(result).toBe(false);
      });

      it('should evaluate "on_failure" condition with some failed results', () => {
        const condition = createMockStepCondition({ type: 'on_failure' });
        const previousResults = new Map([
          ['step1', { success: true, result: 'ok' }],
          ['step2', { success: false, result: 'error' }]
        ]);
        const context = createMockExecutionContext({ previousResults });

        const result = evaluateStepCondition(condition, context);
        expect(result).toBe(true);
      });

      it('should evaluate "on_failure" condition with all successful results', () => {
        const condition = createMockStepCondition({ type: 'on_failure' });
        const previousResults = new Map([
          ['step1', { success: true, result: 'ok' }],
          ['step2', { success: true, result: 'ok' }]
        ]);
        const context = createMockExecutionContext({ previousResults });

        const result = evaluateStepCondition(condition, context);
        expect(result).toBe(false);
      });

      it('should evaluate "component_exists" condition with existing component', () => {
        const condition = createMockStepCondition({ 
          type: 'component_exists', 
          componentId: 'component1' 
        });
        const context = createMockExecutionContext({
          globalContext: {
            executionId: 'test',
            templateId: 'test-template',
            targetComponents: ['component1', 'component2'],
            parameters: {},
            systemState: {},
            timestamp: new Date()
          }
        });

        const result = evaluateStepCondition(condition, context);
        expect(result).toBe(true);
      });

      it('should evaluate "component_exists" condition with non-existing component', () => {
        const condition = createMockStepCondition({ 
          type: 'component_exists', 
          componentId: 'component3' 
        });
        const context = createMockExecutionContext({
          globalContext: {
            executionId: 'test',
            templateId: 'test-template',
            targetComponents: ['component1', 'component2'],
            parameters: {},
            systemState: {},
            timestamp: new Date()
          }
        });

        const result = evaluateStepCondition(condition, context);
        expect(result).toBe(false);
      });

      it('should evaluate "component_exists" condition with undefined componentId', () => {
        const condition = createMockStepCondition({
          type: 'component_exists',
          componentId: undefined
        });
        const context = createMockExecutionContext();

        const result = evaluateStepCondition(condition, context);
        expect(result).toBe(true); // Returns true when componentId is undefined
      });

      it('should evaluate "resource_available" condition with sufficient memory', () => {
        const condition = createMockStepCondition({
          type: 'resource_available',
          resourceThreshold: 1000 // 1000 MB threshold
        });
        const context = createMockExecutionContext();

        // Mock process.memoryUsage to return low memory usage
        const originalMemoryUsage = process.memoryUsage;
        process.memoryUsage = jest.fn().mockReturnValue({
          rss: 100 * 1024 * 1024,
          heapTotal: 100 * 1024 * 1024,
          heapUsed: 100 * 1024 * 1024, // 100 MB
          external: 0,
          arrayBuffers: 0
        }) as any;

        const result = evaluateStepCondition(condition, context);
        expect(result).toBe(true);

        // Restore original
        process.memoryUsage = originalMemoryUsage;
      });

      it('should evaluate "resource_available" condition with insufficient memory', () => {
        const condition = createMockStepCondition({
          type: 'resource_available',
          resourceThreshold: 50 // 50 MB threshold
        });
        const context = createMockExecutionContext();

        // Mock process.memoryUsage to return high memory usage
        const originalMemoryUsage = process.memoryUsage;
        process.memoryUsage = jest.fn().mockReturnValue({
          rss: 100 * 1024 * 1024,
          heapTotal: 100 * 1024 * 1024,
          heapUsed: 100 * 1024 * 1024, // 100 MB
          external: 0,
          arrayBuffers: 0
        }) as any;

        const result = evaluateStepCondition(condition, context);
        expect(result).toBe(false);

        // Restore original
        process.memoryUsage = originalMemoryUsage;
      });

      it('should evaluate "resource_available" condition with undefined resourceThreshold', () => {
        const condition = createMockStepCondition({
          type: 'resource_available',
          resourceThreshold: undefined
        });
        const context = createMockExecutionContext();

        const result = evaluateStepCondition(condition, context);
        expect(result).toBe(true); // Returns true when resourceThreshold is undefined
      });

      it('should evaluate "custom" condition with custom function', () => {
        const customCondition = jest.fn().mockReturnValue(true);
        const condition = createMockStepCondition({
          type: 'custom',
          customCondition
        });
        const context = createMockExecutionContext();

        const result = evaluateStepCondition(condition, context);
        expect(result).toBe(true);
        expect(customCondition).toHaveBeenCalledWith(context);
      });

      it('should evaluate "custom" condition with undefined customCondition', () => {
        const condition = createMockStepCondition({
          type: 'custom',
          customCondition: undefined
        });
        const context = createMockExecutionContext();

        const result = evaluateStepCondition(condition, context);
        expect(result).toBe(true); // Returns true when customCondition is undefined
      });

      it('should handle unknown condition types', () => {
        const condition = createMockStepCondition({ type: 'unknown_type' as any });
        const context = createMockExecutionContext();

        const result = evaluateStepCondition(condition, context);
        expect(result).toBe(true); // Default case returns true
      });

      it('should handle errors in condition evaluation', () => {
        const customCondition = jest.fn().mockImplementation(() => {
          throw new Error('Custom condition error');
        });
        const condition = createMockStepCondition({
          type: 'custom',
          customCondition
        });
        const context = createMockExecutionContext();

        const result = evaluateStepCondition(condition, context);
        expect(result).toBe(false); // Returns false on error
      });

      it('should handle memory usage errors gracefully', () => {
        const condition = createMockStepCondition({
          type: 'resource_available',
          resourceThreshold: 100
        });
        const context = createMockExecutionContext();

        // Mock process.memoryUsage to throw error
        const originalMemoryUsage = process.memoryUsage;
        process.memoryUsage = jest.fn().mockImplementation(() => {
          throw new Error('Memory usage error');
        }) as any;

        const result = evaluateStepCondition(condition, context);
        expect(result).toBe(false); // Returns false on error

        // Restore original
        process.memoryUsage = originalMemoryUsage;
      });
    });
  });

  describe('⚙️ Configuration Validation', () => {
    describe('validateConfigurationCompleteness', () => {
      it('should validate complete configuration without issues', () => {
        const config: Partial<IEnhancedCleanupConfig> = {
          rollbackEnabled: true,
          maxCheckpoints: 10,
          performanceMonitoringEnabled: true,
          cleanupIntervalMs: 30000,
          templateValidationEnabled: true
        };

        const issues = validateConfigurationCompleteness(config);
        expect(issues).toHaveLength(0);
      });

      it('should detect missing maxCheckpoints when rollback is enabled', () => {
        const config: Partial<IEnhancedCleanupConfig> = {
          rollbackEnabled: true,
          // maxCheckpoints missing
        };

        const issues = validateConfigurationCompleteness(config);
        expect(issues).toContain('maxCheckpoints should be specified when rollback is enabled');
      });

      it('should detect missing cleanupIntervalMs when performance monitoring is enabled', () => {
        const config: Partial<IEnhancedCleanupConfig> = {
          performanceMonitoringEnabled: true,
          // cleanupIntervalMs missing
        };

        const issues = validateConfigurationCompleteness(config);
        expect(issues).toContain('cleanupIntervalMs should be specified when performance monitoring is enabled');
      });

      it('should detect undefined templateValidationEnabled', () => {
        const config: Partial<IEnhancedCleanupConfig> = {
          // templateValidationEnabled undefined
        };

        const issues = validateConfigurationCompleteness(config);
        expect(issues).toContain('templateValidationEnabled should be explicitly set');
      });

      it('should handle multiple configuration issues', () => {
        const config: Partial<IEnhancedCleanupConfig> = {
          rollbackEnabled: true,
          performanceMonitoringEnabled: true,
          // Missing maxCheckpoints, cleanupIntervalMs, and templateValidationEnabled
        };

        const issues = validateConfigurationCompleteness(config);
        expect(issues).toHaveLength(3);
        expect(issues).toContain('maxCheckpoints should be specified when rollback is enabled');
        expect(issues).toContain('cleanupIntervalMs should be specified when performance monitoring is enabled');
        expect(issues).toContain('templateValidationEnabled should be explicitly set');
      });

      it('should handle empty configuration', () => {
        const config: Partial<IEnhancedCleanupConfig> = {};

        const issues = validateConfigurationCompleteness(config);
        expect(issues).toContain('templateValidationEnabled should be explicitly set');
      });

      it('should handle configuration with rollback disabled', () => {
        const config: Partial<IEnhancedCleanupConfig> = {
          rollbackEnabled: false,
          templateValidationEnabled: true
        };

        const issues = validateConfigurationCompleteness(config);
        expect(issues).toHaveLength(0);
      });

      it('should handle configuration with performance monitoring disabled', () => {
        const config: Partial<IEnhancedCleanupConfig> = {
          performanceMonitoringEnabled: false,
          templateValidationEnabled: true
        };

        const issues = validateConfigurationCompleteness(config);
        expect(issues).toHaveLength(0);
      });
    });
  });

  describe('🔧 Advanced Template Validation', () => {
    describe('validateTemplate - Advanced Cases', () => {
      it('should detect duplicate step IDs', () => {
        const template = createMockTemplate({
          operations: [
            createMockTemplateStep({ id: 'duplicate-step' }),
            createMockTemplateStep({ id: 'duplicate-step' }) // Duplicate ID
          ]
        });

        const result = validateTemplate(template);
        expect(result.valid).toBe(false);
        expect(result.issues.some(issue => issue.type === 'duplicate_step_id')).toBe(true);
        expect(result.issues.find(issue => issue.type === 'duplicate_step_id')?.stepId).toBe('duplicate-step');
      });

      it('should detect unknown step dependencies', () => {
        const template = createMockTemplate({
          operations: [
            createMockTemplateStep({
              id: 'step-1',
              dependsOn: ['unknown-step'] // Unknown dependency
            })
          ]
        });

        const result = validateTemplate(template);
        expect(result.warnings).toContain('Step step-1 depends on unknown step: unknown-step');
      });

      it('should validate valid step dependencies', () => {
        const template = createMockTemplate({
          operations: [
            createMockTemplateStep({ id: 'step-1' }),
            createMockTemplateStep({
              id: 'step-2',
              dependsOn: ['step-1'] // Valid dependency
            })
          ]
        });

        const result = validateTemplate(template);
        expect(result.warnings).toHaveLength(0);
      });

      it('should detect invalid regex patterns in component patterns', () => {
        const template = createMockTemplate({
          operations: [
            createMockTemplateStep({
              id: 'step-1',
              componentPattern: '[invalid-regex' // Invalid regex
            })
          ]
        });

        const result = validateTemplate(template);
        expect(result.valid).toBe(false);
        expect(result.issues.some(issue => issue.type === 'invalid_regex')).toBe(true);
        const regexIssue = result.issues.find(issue => issue.type === 'invalid_regex');
        expect(regexIssue?.stepId).toBe('step-1');
        expect(regexIssue?.field).toBe('componentPattern');
      });

      it('should suggest optimization for long timeouts', () => {
        const template = createMockTemplate({
          operations: [
            createMockTemplateStep({
              id: 'step-1',
              timeout: 400000 // 6.67 minutes - longer than 5 minutes
            })
          ]
        });

        const result = validateTemplate(template);
        expect(result.suggestions).toContain('Step step-1 has a very long timeout (400000ms). Consider breaking it into smaller steps.');
      });

      it('should validate rollback steps', () => {
        const template = createMockTemplate({
          rollbackSteps: [
            createMockTemplateStep({
              id: 'rollback-1',
              rollbackOperation: undefined // rollbackOperation missing
            })
          ]
        });

        const result = validateTemplate(template);
        expect(result.warnings).toContain('Rollback step rollback-1 should specify a rollback operation');
      });

      it('should handle template with no rollback steps', () => {
        const template = createMockTemplate({
          rollbackSteps: []
        });

        const result = validateTemplate(template);
        expect(result.valid).toBe(true);
      });

      it('should handle template with undefined rollback steps', () => {
        const template = createMockTemplate({
          rollbackSteps: undefined
        });

        const result = validateTemplate(template);
        expect(result.valid).toBe(true);
      });

      it('should handle validation errors gracefully', () => {
        // Create a template with invalid regex that will cause an error during validation
        const template = createMockTemplate({
          operations: [
            createMockTemplateStep({
              componentPattern: '[invalid-regex' // This will cause RegExp error
            })
          ]
        });

        // The validation should not throw but should return invalid result
        const result = validateTemplate(template);
        expect(result.valid).toBe(false);
        expect(result.issues.some(issue => issue.type === 'invalid_regex')).toBe(true);
      });
    });
  });

  describe('🎯 Resilient Timing Integration', () => {
    describe('Module-level timing infrastructure', () => {
      it('should successfully execute validation with timing infrastructure', () => {
        const template = createMockTemplate();

        // This test verifies that the function executes successfully with the mocked timing infrastructure
        const result = validateTemplate(template);

        // Check that validation runs without throwing errors (timing infrastructure works)
        expect(result).toBeDefined();
        expect(typeof result.valid).toBe('boolean');
      });

      it('should handle timing infrastructure integration without errors', () => {
        const template = createMockTemplate();

        // This test verifies that the timing infrastructure doesn't interfere with normal operation
        expect(() => validateTemplate(template)).not.toThrow();
      });

      it('should maintain functionality when timing infrastructure is mocked', () => {
        const template = createMockTemplate({ id: 'ab' }); // Invalid ID

        const result = validateTemplate(template);

        // Should maintain correct validation logic
        expect(result.valid).toBe(false);
        expect(result.issues.some(issue => issue.type === 'invalid_id')).toBe(true);
      });

      it('should handle error scenarios gracefully with timing infrastructure', () => {
        // Test with null template to trigger error handling
        expect(() => validateTemplate(null as any)).toThrow();
      });
    });

    describe('UtilityValidationEnhanced class', () => {
      let validationManager: UtilityValidationEnhanced;

      beforeEach(() => {
        validationManager = new UtilityValidationEnhanced();
      });

      it('should initialize with resilient timing infrastructure', () => {
        expect(validationManager).toBeDefined();

        const metrics = validationManager.getTimingMetrics();
        expect(metrics.timerAvailable).toBe(true);
        expect(metrics.metricsAvailable).toBe(true);
      });

      it('should handle doInitialize method', async () => {
        await expect(validationManager.doInitialize()).resolves.not.toThrow();

        const metrics = validationManager.getTimingMetrics();
        expect(metrics.initialized).toBe(true);
      });

      it('should handle doShutdown method', async () => {
        await expect(validationManager.doShutdown()).resolves.not.toThrow();

        const metrics = validationManager.getTimingMetrics();
        expect(metrics.initialized).toBe(false);
      });

      it('should validate template with timing measurement', async () => {
        const template = createMockTemplate();

        const result = await validationManager.validateTemplateEnhanced(template);

        // Check that enhanced validation runs without throwing errors
        expect(result).toBeDefined();
        expect(typeof result.valid).toBe('boolean');
      });

      it('should validate configuration with timing measurement', async () => {
        const config: Partial<IEnhancedCleanupConfig> = {
          templateValidationEnabled: true
        };

        const issues = await validationManager.validateConfigurationEnhanced(config);

        expect(issues).toHaveLength(0);
      });

      it('should handle template validation errors gracefully', async () => {
        // Test with null template to trigger error handling
        await expect(validationManager.validateTemplateEnhanced(null as any)).rejects.toThrow();
      });

      it('should handle configuration validation errors gracefully', async () => {
        // Test with null config to trigger error handling
        await expect(validationManager.validateConfigurationEnhanced(null as any)).rejects.toThrow();
      });

      it('should test private _initializeResilientTimingSync method via constructor', () => {
        // Test the private method indirectly through constructor
        const manager = new UtilityValidationEnhanced();
        const metrics = manager.getTimingMetrics();

        // Should have timing infrastructure available after construction
        expect(metrics.timerAvailable).toBe(true);
        expect(metrics.metricsAvailable).toBe(true);
      });

      it('should handle timing infrastructure failures in constructor', () => {
        // Mock ResilientTimer to throw error during construction
        const originalTimer = require('../../../utils/ResilientTiming').ResilientTimer;
        require('../../../utils/ResilientTiming').ResilientTimer = jest.fn().mockImplementation(() => {
          throw new Error('Timer construction failed');
        });

        // Should not throw error, should fallback gracefully
        expect(() => new UtilityValidationEnhanced()).not.toThrow();

        // Restore original
        require('../../../utils/ResilientTiming').ResilientTimer = originalTimer;
      });

      it('should handle timing infrastructure failures in doInitialize', async () => {
        const manager = new UtilityValidationEnhanced();

        // Mock ResilientTimer to throw error during reconfiguration
        const originalTimer = require('../../../utils/ResilientTiming').ResilientTimer;
        require('../../../utils/ResilientTiming').ResilientTimer = jest.fn().mockImplementation(() => {
          throw new Error('Timer reconfiguration failed');
        });

        // Should not throw error, should continue with fallback
        await expect(manager.doInitialize()).resolves.not.toThrow();

        // Restore original
        require('../../../utils/ResilientTiming').ResilientTimer = originalTimer;
      });

      it('should test doInitialize reconfiguration when not initialized', async () => {
        // Create manager and force _initialized to false to trigger reconfiguration
        const manager = new UtilityValidationEnhanced();

        // Access private property to force uninitialized state
        (manager as any)._initialized = false;

        // This should trigger the reconfiguration logic
        await manager.doInitialize();

        const metrics = manager.getTimingMetrics();
        expect(metrics.initialized).toBe(true);
      });

      it('should test doInitialize error handling with console.warn', async () => {
        const manager = new UtilityValidationEnhanced();

        // Force uninitialized state
        (manager as any)._initialized = false;

        // Mock console.warn to verify it's called
        const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation();

        // Mock ResilientTimer to throw error during reconfiguration
        const originalTimer = require('../../../utils/ResilientTiming').ResilientTimer;
        require('../../../utils/ResilientTiming').ResilientTimer = jest.fn().mockImplementation(() => {
          throw new Error('Reconfiguration failed');
        });

        // Should trigger error handling and console.warn
        await manager.doInitialize();

        expect(consoleWarnSpy).toHaveBeenCalledWith('Failed to reconfigure resilient timing infrastructure, using existing fallback');

        // Restore mocks
        require('../../../utils/ResilientTiming').ResilientTimer = originalTimer;
        consoleWarnSpy.mockRestore();
      });
    });
  });

  describe('📦 Validation Utility Collection', () => {
    describe('ValidationUtils', () => {
      it('should export all utility functions', () => {
        expect(ValidationUtils.validateTemplate).toBe(validateTemplate);
        expect(ValidationUtils.evaluateStepCondition).toBe(evaluateStepCondition);
        expect(ValidationUtils.validateConfigurationCompleteness).toBe(validateConfigurationCompleteness);
      });

      it('should provide access to all functions through the utility collection', () => {
        const template = createMockTemplate();
        const condition = createMockStepCondition();
        const context = createMockExecutionContext();
        const config: Partial<IEnhancedCleanupConfig> = { templateValidationEnabled: true };

        // Test all functions are accessible and functional through ValidationUtils
        expect(() => ValidationUtils.validateTemplate(template)).not.toThrow();
        expect(() => ValidationUtils.evaluateStepCondition(condition, context)).not.toThrow();
        expect(() => ValidationUtils.validateConfigurationCompleteness(config)).not.toThrow();
      });
    });
  });

  describe('🔍 Edge Cases and Error Handling', () => {
    describe('Complex validation scenarios', () => {
      it('should handle template with complex operation structure', () => {
        const template = createMockTemplate({
          operations: [
            createMockTemplateStep({
              id: 'complex-step',
              componentPattern: '^(test|prod)-[a-z0-9]+-service$',
              timeout: 10000,
              retryPolicy: {
                maxRetries: 5,
                retryDelay: 1000,
                backoffMultiplier: 2,
                maxRetryDelay: 10000,
                retryOnErrors: ['error']
              },
              dependsOn: []
            })
          ]
        });

        const result = validateTemplate(template);
        // Check that complex template validation runs without throwing errors
        expect(result).toBeDefined();
        expect(typeof result.valid).toBe('boolean');
      });

      it('should handle template with multiple validation issues', () => {
        const template = createMockTemplate({
          id: 'ab', // Too short
          name: 'a'.repeat(300), // Too long
          operations: [], // Empty operations
        });

        const result = validateTemplate(template);
        expect(result.valid).toBe(false);
        expect(result.issues).toHaveLength(3);
        expect(result.issues.some(issue => issue.type === 'invalid_id')).toBe(true);
        expect(result.issues.some(issue => issue.type === 'invalid_name')).toBe(true);
        expect(result.issues.some(issue => issue.type === 'no_operations')).toBe(true);
      });

      it('should handle step condition evaluation with complex context', () => {
        const condition = createMockStepCondition({ type: 'on_success' });
        const complexResults = new Map([
          ['step1', { success: true, result: { data: 'complex', nested: { value: 123 } } }],
          ['step2', { success: true, result: 'simple string' }],
          ['step3', { success: true, result: null }]
        ]);
        const context = createMockExecutionContext({ previousResults: complexResults });

        const result = evaluateStepCondition(condition, context);
        expect(result).toBe(true);
      });

      it('should handle configuration validation with edge cases', () => {
        const config: Partial<IEnhancedCleanupConfig> = {
          rollbackEnabled: false,
          maxCheckpoints: 0, // Edge case: zero checkpoints
          performanceMonitoringEnabled: false,
          cleanupIntervalMs: 0, // Edge case: zero interval
          templateValidationEnabled: false
        };

        const issues = validateConfigurationCompleteness(config);
        expect(issues).toHaveLength(0); // Should be valid even with zero values
      });

      it('should handle null and undefined values gracefully', () => {
        // Test with null template
        expect(() => validateTemplate(null as any)).toThrow();

        // Test with undefined condition - should return false (error case)
        const result = evaluateStepCondition(undefined as any, createMockExecutionContext());
        expect(result).toBe(false);

        // Test with null config (should not throw)
        expect(() => validateConfigurationCompleteness(null as any)).toThrow();
      });

      it('should handle memory usage edge cases', () => {
        const condition = createMockStepCondition({
          type: 'resource_available',
          resourceThreshold: 0 // Edge case: zero threshold
        });
        const context = createMockExecutionContext();

        const result = evaluateStepCondition(condition, context);
        expect(result).toBe(true); // Zero threshold should return true (no restriction)
      });

      it('should handle process.memoryUsage returning undefined heapUsed', () => {
        const condition = createMockStepCondition({
          type: 'resource_available',
          resourceThreshold: 100
        });
        const context = createMockExecutionContext();

        // Mock process.memoryUsage to return undefined heapUsed
        const originalMemoryUsage = process.memoryUsage;
        process.memoryUsage = jest.fn().mockReturnValue({
          rss: 0,
          heapTotal: 0,
          heapUsed: undefined,
          external: 0,
          arrayBuffers: 0
        }) as any;

        const result = evaluateStepCondition(condition, context);
        expect(result).toBe(true); // Should handle undefined gracefully (0 || 0 < threshold)

        // Restore original
        process.memoryUsage = originalMemoryUsage;
      });
    });
  });
});
