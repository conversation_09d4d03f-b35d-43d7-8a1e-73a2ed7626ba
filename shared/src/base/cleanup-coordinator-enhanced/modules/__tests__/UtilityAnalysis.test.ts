/**
 * @file UtilityAnalysis Comprehensive Test Suite
 * @description Comprehensive test coverage for UtilityAnalysis.ts using surgical precision testing methodologies
 * @coverage-target 100% lines, 95%+ branches, 100% functions
 * @testing-approach Surgical precision testing with direct method access and strategic error injection
 * @compliance MEM-SAFE-002, Anti-Simplification Policy, Enterprise-grade quality
 */

import {
  generateDependencyCacheKey,
  identifyOptimizationOpportunities,
  generateMitigationStrategies,
  generateContingencyPlans,
  AnalysisUtils
} from '../UtilityAnalysis';

import { CleanupPriority, ICleanupOperation } from '../../../CleanupCoordinatorEnhanced';
import { IOptimizationOpportunity, IRiskFactor } from '../../../types/CleanupTypes';

// Mock resilient timing infrastructure - must be done before importing the module
jest.mock('../../../utils/ResilientTiming', () => ({
  ResilientTimer: jest.fn().mockImplementation(() => ({
    start: jest.fn().mockReturnValue({
      end: jest.fn().mockReturnValue({
        duration: 100,
        reliable: true,
        method: 'performance'
      })
    })
  }))
}));

jest.mock('../../../utils/ResilientMetrics', () => ({
  ResilientMetricsCollector: jest.fn().mockImplementation(() => ({
    recordTiming: jest.fn()
  }))
}));

describe('UtilityAnalysis', () => {
  // Helper functions to access mocked instances
  const getMockTimer = () => require('../../../utils/ResilientTiming').ResilientTimer;
  const getMockMetrics = () => require('../../../utils/ResilientMetrics').ResilientMetricsCollector;

  // Test data setup
  const createMockOperation = (
    id: string,
    priority: CleanupPriority = CleanupPriority.NORMAL,
    dependencies: string[] = []
  ): ICleanupOperation => ({
    id,
    type: 'resource_cleanup' as any,
    priority,
    status: 'pending' as any,
    componentId: `component-${id}`,
    operation: async () => {},
    dependencies,
    timeout: 5000,
    retryCount: 0,
    maxRetries: 3,
    createdAt: new Date()
  });

  const createMockRiskFactor = (
    type: IRiskFactor['type'],
    severity: IRiskFactor['severity'] = 'medium'
  ): IRiskFactor => ({
    type,
    severity,
    description: `Test ${type} risk factor`,
    affectedOperations: ['op1', 'op2'],
    likelihood: 0.5,
    impact: 0.7
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('🔧 Dependency Cache Utilities', () => {
    describe('generateDependencyCacheKey', () => {
      it('should generate consistent cache keys for identical operation sets', () => {
        const operations1 = [
          createMockOperation('op1', CleanupPriority.HIGH, ['dep1', 'dep2']),
          createMockOperation('op2', CleanupPriority.NORMAL, ['dep3'])
        ];

        const operations2 = [
          createMockOperation('op2', CleanupPriority.NORMAL, ['dep3']),
          createMockOperation('op1', CleanupPriority.HIGH, ['dep2', 'dep1']) // Different order
        ];

        const key1 = generateDependencyCacheKey(operations1);
        const key2 = generateDependencyCacheKey(operations2);

        expect(key1).toBe(key2);
        expect(key1).toMatch(/^dep-analysis-\d+$/);
      });

      it('should generate different cache keys for different operation sets', () => {
        const operations1 = [createMockOperation('op1', CleanupPriority.HIGH, ['dep1'])];
        const operations2 = [createMockOperation('op2', CleanupPriority.LOW, ['dep2'])];

        const key1 = generateDependencyCacheKey(operations1);
        const key2 = generateDependencyCacheKey(operations2);

        expect(key1).not.toBe(key2);
        expect(key1).toMatch(/^dep-analysis-\d+$/);
        expect(key2).toMatch(/^dep-analysis-\d+$/);
      });

      it('should handle operations with no dependencies', () => {
        const operations = [
          createMockOperation('op1'),
          createMockOperation('op2')
        ];

        const key = generateDependencyCacheKey(operations);
        expect(key).toMatch(/^dep-analysis-\d+$/);
      });

      it('should handle operations with undefined dependencies (target line 106)', () => {
        // Create operations with explicitly undefined dependencies to target line 106
        const operationsWithUndefinedDeps = [
          {
            ...createMockOperation('op1'),
            dependencies: undefined // This will trigger the || [] fallback on line 106
          },
          {
            ...createMockOperation('op2'),
            dependencies: undefined
          }
        ];

        const key = generateDependencyCacheKey(operationsWithUndefinedDeps);
        expect(key).toMatch(/^dep-analysis-\d+$/);
      });

      it('should handle empty operations array', () => {
        const key = generateDependencyCacheKey([]);
        expect(key).toBe('dep-analysis-0');
      });

      it('should handle operations with complex dependency structures', () => {
        const operations = [
          createMockOperation('op1', CleanupPriority.EMERGENCY, ['dep1', 'dep2', 'dep3']),
          createMockOperation('op2', CleanupPriority.LOW, ['dep4', 'dep5']),
          createMockOperation('op3', CleanupPriority.HIGH, [])
        ];

        const key = generateDependencyCacheKey(operations);
        expect(key).toMatch(/^dep-analysis-\d+$/);
      });
    });
  });

  describe('🎯 Optimization Analysis Utilities', () => {
    describe('identifyOptimizationOpportunities', () => {
      it('should identify parallelization opportunities for groups with multiple operations', () => {
        const operations = [
          createMockOperation('op1'),
          createMockOperation('op2'),
          createMockOperation('op3'),
          createMockOperation('op4')
        ];

        const parallelGroups = [
          ['op1', 'op2', 'op3'], // 3 operations - should create opportunity
          ['op4'] // Single operation - should not create opportunity
        ];

        const opportunities = identifyOptimizationOpportunities(operations, parallelGroups);

        expect(opportunities).toHaveLength(1);
        expect(opportunities[0]).toEqual({
          type: 'parallelization',
          description: 'Parallel execution of 3 operations',
          estimatedImprovement: 30, // 3 * 10 = 30%
          implementationComplexity: 'medium',
          riskLevel: 'low',
          affectedOperations: ['op1', 'op2', 'op3']
        });
      });

      it('should cap parallelization improvement at 50%', () => {
        const operations = Array.from({ length: 10 }, (_, i) => 
          createMockOperation(`op${i + 1}`)
        );

        const parallelGroups = [
          Array.from({ length: 10 }, (_, i) => `op${i + 1}`) // 10 operations
        ];

        const opportunities = identifyOptimizationOpportunities(operations, parallelGroups);

        expect(opportunities).toHaveLength(1);
        expect(opportunities[0].estimatedImprovement).toBe(50); // Capped at 50%
      });

      it('should identify priority adjustment opportunities when high priority operations dominate', () => {
        const operations = [
          createMockOperation('op1', CleanupPriority.EMERGENCY),
          createMockOperation('op2', CleanupPriority.EMERGENCY),
          createMockOperation('op3', CleanupPriority.EMERGENCY),
          createMockOperation('op4', CleanupPriority.EMERGENCY),
          createMockOperation('op5', CleanupPriority.EMERGENCY),
          createMockOperation('op6', CleanupPriority.LOW) // Only 1 low priority vs 5 high priority
        ];

        const parallelGroups: string[][] = [];

        const opportunities = identifyOptimizationOpportunities(operations, parallelGroups);

        expect(opportunities).toHaveLength(1);
        expect(opportunities[0]).toEqual({
          type: 'priority_adjustment',
          description: 'Consider rebalancing operation priorities for better performance',
          estimatedImprovement: 15,
          implementationComplexity: 'low',
          riskLevel: 'low',
          affectedOperations: ['op1', 'op2', 'op3', 'op4', 'op5']
        });
      });

      it('should not identify priority adjustment when priorities are balanced', () => {
        const operations = [
          createMockOperation('op1', CleanupPriority.EMERGENCY),
          createMockOperation('op2', CleanupPriority.EMERGENCY),
          createMockOperation('op3', CleanupPriority.LOW),
          createMockOperation('op4', CleanupPriority.LOW),
          createMockOperation('op5', CleanupPriority.LOW) // 2 high vs 3 low - balanced
        ];

        const parallelGroups: string[][] = [];

        const opportunities = identifyOptimizationOpportunities(operations, parallelGroups);

        expect(opportunities).toHaveLength(0);
      });

      it('should combine multiple optimization opportunities', () => {
        const operations = [
          createMockOperation('op1', CleanupPriority.EMERGENCY),
          createMockOperation('op2', CleanupPriority.EMERGENCY),
          createMockOperation('op3', CleanupPriority.EMERGENCY),
          createMockOperation('op4', CleanupPriority.EMERGENCY),
          createMockOperation('op5', CleanupPriority.EMERGENCY),
          createMockOperation('op6', CleanupPriority.LOW),
          createMockOperation('op7'),
          createMockOperation('op8')
        ];

        const parallelGroups = [
          ['op7', 'op8'] // Parallelization opportunity
        ];

        const opportunities = identifyOptimizationOpportunities(operations, parallelGroups);

        expect(opportunities).toHaveLength(2);

        // Should have both parallelization and priority adjustment
        const types = opportunities.map(op => op.type);
        expect(types).toContain('parallelization');
        expect(types).toContain('priority_adjustment');
      });

      it('should handle empty operations and parallel groups', () => {
        const opportunities = identifyOptimizationOpportunities([], []);
        expect(opportunities).toHaveLength(0);
      });

      it('should handle operations with no parallel groups', () => {
        const operations = [
          createMockOperation('op1', CleanupPriority.NORMAL),
          createMockOperation('op2', CleanupPriority.NORMAL)
        ];

        const opportunities = identifyOptimizationOpportunities(operations, []);
        expect(opportunities).toHaveLength(0);
      });
    });
  });

  describe('🛡️ Risk Mitigation Utilities', () => {
    describe('generateMitigationStrategies', () => {
      it('should generate appropriate strategies for circular dependency risks', () => {
        const riskFactors = [
          createMockRiskFactor('circular_dependency', 'high')
        ];

        const strategies = generateMitigationStrategies(riskFactors);

        expect(strategies).toContain('Review and refactor operation dependencies to eliminate cycles');
        expect(strategies).toHaveLength(1);
      });

      it('should generate appropriate strategies for resource contention risks', () => {
        const riskFactors = [
          createMockRiskFactor('resource_contention', 'medium')
        ];

        const strategies = generateMitigationStrategies(riskFactors);

        expect(strategies).toContain('Consider implementing resource pooling or queue management');
        expect(strategies).toHaveLength(1);
      });

      it('should generate appropriate strategies for timing constraint risks', () => {
        const riskFactors = [
          createMockRiskFactor('timing_constraint', 'high')
        ];

        const strategies = generateMitigationStrategies(riskFactors);

        expect(strategies).toContain('Optimize operation timeouts and implement parallel execution');
        expect(strategies).toHaveLength(1);
      });

      it('should generate appropriate strategies for external dependency risks', () => {
        const riskFactors = [
          createMockRiskFactor('external_dependency', 'critical')
        ];

        const strategies = generateMitigationStrategies(riskFactors);

        expect(strategies).toContain('Implement fallback mechanisms and health check monitoring');
        expect(strategies).toHaveLength(1);
      });

      it('should handle multiple risk factors and remove duplicates', () => {
        const riskFactors = [
          createMockRiskFactor('circular_dependency', 'high'),
          createMockRiskFactor('resource_contention', 'medium'),
          createMockRiskFactor('circular_dependency', 'low'), // Duplicate type
          createMockRiskFactor('timing_constraint', 'high')
        ];

        const strategies = generateMitigationStrategies(riskFactors);

        expect(strategies).toHaveLength(3); // Should remove duplicates
        expect(strategies).toContain('Review and refactor operation dependencies to eliminate cycles');
        expect(strategies).toContain('Consider implementing resource pooling or queue management');
        expect(strategies).toContain('Optimize operation timeouts and implement parallel execution');
      });

      it('should handle empty risk factors array', () => {
        const strategies = generateMitigationStrategies([]);
        expect(strategies).toHaveLength(0);
      });
    });

    describe('generateContingencyPlans', () => {
      it('should generate emergency plans for critical risk factors', () => {
        const riskFactors = [
          createMockRiskFactor('circular_dependency', 'critical'),
          createMockRiskFactor('resource_contention', 'medium')
        ];

        const plans = generateContingencyPlans(riskFactors);

        expect(plans).toContain('Emergency rollback procedures should be prepared');
        expect(plans).toContain('Manual intervention procedures should be documented');
        expect(plans).toContain('Comprehensive logging and monitoring for failure analysis');
        expect(plans).toContain('Automated alert system for dependency analysis anomalies');
      });

      it('should generate resource allocation plans for resource contention risks', () => {
        const riskFactors = [
          createMockRiskFactor('resource_contention', 'high')
        ];

        const plans = generateContingencyPlans(riskFactors);

        expect(plans).toContain('Alternative resource allocation strategies should be available');
        expect(plans).toContain('Comprehensive logging and monitoring for failure analysis');
        expect(plans).toContain('Automated alert system for dependency analysis anomalies');
      });

      it('should generate timeout extension plans for timing constraint risks', () => {
        const riskFactors = [
          createMockRiskFactor('timing_constraint', 'medium')
        ];

        const plans = generateContingencyPlans(riskFactors);

        expect(plans).toContain('Timeout extension protocols should be established');
        expect(plans).toContain('Comprehensive logging and monitoring for failure analysis');
        expect(plans).toContain('Automated alert system for dependency analysis anomalies');
      });

      it('should always include default contingency plans', () => {
        const riskFactors = [
          createMockRiskFactor('external_dependency', 'low')
        ];

        const plans = generateContingencyPlans(riskFactors);

        expect(plans).toContain('Comprehensive logging and monitoring for failure analysis');
        expect(plans).toContain('Automated alert system for dependency analysis anomalies');
      });

      it('should handle multiple risk types and remove duplicates', () => {
        const riskFactors = [
          createMockRiskFactor('resource_contention', 'high'),
          createMockRiskFactor('timing_constraint', 'medium'),
          createMockRiskFactor('circular_dependency', 'critical')
        ];

        const plans = generateContingencyPlans(riskFactors);

        // Should include all specific plans plus defaults, with duplicates removed
        const uniquePlans = Array.from(new Set(plans));
        expect(plans).toEqual(uniquePlans); // No duplicates

        expect(plans).toContain('Emergency rollback procedures should be prepared');
        expect(plans).toContain('Manual intervention procedures should be documented');
        expect(plans).toContain('Alternative resource allocation strategies should be available');
        expect(plans).toContain('Timeout extension protocols should be established');
        expect(plans).toContain('Comprehensive logging and monitoring for failure analysis');
        expect(plans).toContain('Automated alert system for dependency analysis anomalies');
      });

      it('should handle empty risk factors array with default plans', () => {
        const plans = generateContingencyPlans([]);

        expect(plans).toHaveLength(2);
        expect(plans).toContain('Comprehensive logging and monitoring for failure analysis');
        expect(plans).toContain('Automated alert system for dependency analysis anomalies');
      });
    });
  });

  describe('📦 Analysis Utility Collection', () => {
    describe('AnalysisUtils', () => {
      it('should export all utility functions', () => {
        expect(AnalysisUtils.generateDependencyCacheKey).toBe(generateDependencyCacheKey);
        expect(AnalysisUtils.identifyOptimizationOpportunities).toBe(identifyOptimizationOpportunities);
        expect(AnalysisUtils.generateMitigationStrategies).toBe(generateMitigationStrategies);
        expect(AnalysisUtils.generateContingencyPlans).toBe(generateContingencyPlans);
      });

      it('should provide access to all functions through the utility collection', () => {
        const operations = [createMockOperation('test')];
        const parallelGroups = [['test']];
        const riskFactors = [createMockRiskFactor('circular_dependency')];

        // Test all functions are accessible and functional through AnalysisUtils
        expect(() => AnalysisUtils.generateDependencyCacheKey(operations)).not.toThrow();
        expect(() => AnalysisUtils.identifyOptimizationOpportunities(operations, parallelGroups)).not.toThrow();
        expect(() => AnalysisUtils.generateMitigationStrategies(riskFactors)).not.toThrow();
        expect(() => AnalysisUtils.generateContingencyPlans(riskFactors)).not.toThrow();
      });
    });
  });

  describe('🎯 Resilient Timing Integration', () => {
    describe('Module-level timing infrastructure', () => {
      it('should successfully execute optimization analysis with timing infrastructure', () => {
        const operations = [createMockOperation('op1'), createMockOperation('op2')];
        const parallelGroups = [['op1', 'op2']];

        // This test verifies that the function executes successfully with the mocked timing infrastructure
        const opportunities = identifyOptimizationOpportunities(operations, parallelGroups);

        expect(opportunities).toHaveLength(1);
        expect(opportunities[0].type).toBe('parallelization');
        expect(opportunities[0].affectedOperations).toEqual(['op1', 'op2']);
      });

      it('should handle timing infrastructure integration without errors', () => {
        const operations = [createMockOperation('op1')];
        const parallelGroups: string[][] = [];

        // This test verifies that the timing infrastructure doesn't interfere with normal operation
        expect(() => identifyOptimizationOpportunities(operations, parallelGroups)).not.toThrow();
      });

      it('should maintain functionality when timing infrastructure is mocked', () => {
        const operations = [
          createMockOperation('op1', CleanupPriority.EMERGENCY),
          createMockOperation('op2', CleanupPriority.EMERGENCY),
          createMockOperation('op3', CleanupPriority.EMERGENCY),
          createMockOperation('op4', CleanupPriority.LOW)
        ];
        const parallelGroups = [['op1', 'op2']];

        const opportunities = identifyOptimizationOpportunities(operations, parallelGroups);

        // Should identify both parallelization and priority adjustment opportunities
        expect(opportunities).toHaveLength(2);
        const types = opportunities.map(op => op.type);
        expect(types).toContain('parallelization');
        expect(types).toContain('priority_adjustment');
      });

      it('should handle error scenarios gracefully with timing infrastructure', () => {
        // Test with null operations to trigger error handling
        expect(() => identifyOptimizationOpportunities(null as any, [])).toThrow();
      });
    });
  });

  describe('🔍 Edge Cases and Error Handling', () => {
    describe('Hash collision handling in cache key generation', () => {
      it('should handle operations with identical signatures but different IDs', () => {
        const operations1 = [createMockOperation('op1', CleanupPriority.NORMAL, ['dep1'])];
        const operations2 = [createMockOperation('op2', CleanupPriority.NORMAL, ['dep1'])];

        const key1 = generateDependencyCacheKey(operations1);
        const key2 = generateDependencyCacheKey(operations2);

        expect(key1).not.toBe(key2);
      });

      it('should handle very long operation signatures', () => {
        const longDependencies = Array.from({ length: 100 }, (_, i) => `dep${i}`);
        const operations = [createMockOperation('op1', CleanupPriority.NORMAL, longDependencies)];

        const key = generateDependencyCacheKey(operations);
        expect(key).toMatch(/^dep-analysis-\d+$/);
      });

      it('should handle special characters in operation IDs and dependencies', () => {
        const operations = [
          createMockOperation('op-1_special@chars', CleanupPriority.NORMAL, ['dep:with:colons', 'dep|with|pipes'])
        ];

        const key = generateDependencyCacheKey(operations);
        expect(key).toMatch(/^dep-analysis-\d+$/);
      });
    });

    describe('Boundary conditions in optimization analysis', () => {
      it('should handle single operation in parallel group', () => {
        const operations = [createMockOperation('op1')];
        const parallelGroups = [['op1']];

        const opportunities = identifyOptimizationOpportunities(operations, parallelGroups);
        expect(opportunities).toHaveLength(0); // Single operation should not create parallelization opportunity
      });

      it('should handle operations with all same priority levels', () => {
        const operations = [
          createMockOperation('op1', CleanupPriority.HIGH),
          createMockOperation('op2', CleanupPriority.HIGH),
          createMockOperation('op3', CleanupPriority.HIGH)
        ];

        const opportunities = identifyOptimizationOpportunities(operations, []);
        expect(opportunities).toHaveLength(0); // No priority adjustment needed
      });

      it('should handle operations with no low priority operations', () => {
        const operations = [
          createMockOperation('op1', CleanupPriority.EMERGENCY),
          createMockOperation('op2', CleanupPriority.HIGH),
          createMockOperation('op3', CleanupPriority.NORMAL)
        ];

        const opportunities = identifyOptimizationOpportunities(operations, []);
        // Actually, this should create a priority adjustment opportunity because 1 EMERGENCY > 0 LOW * 2
        expect(opportunities).toHaveLength(1);
        expect(opportunities[0].type).toBe('priority_adjustment');
      });
    });

    describe('Risk factor edge cases', () => {
      it('should handle unknown risk factor types gracefully', () => {
        const riskFactors = [
          {
            type: 'unknown_risk_type' as any,
            severity: 'medium' as const,
            description: 'Unknown risk type',
            affectedOperations: ['op1'],
            likelihood: 0.3,
            impact: 0.4
          }
        ];

        const strategies = generateMitigationStrategies(riskFactors);
        expect(strategies).toHaveLength(0); // Should not crash, but no strategies for unknown types
      });

      it('should handle risk factors with missing properties', () => {
        const incompleteRiskFactor = {
          type: 'circular_dependency' as const,
          severity: 'high' as const
          // Missing other properties
        } as IRiskFactor;

        expect(() => generateMitigationStrategies([incompleteRiskFactor])).not.toThrow();
        expect(() => generateContingencyPlans([incompleteRiskFactor])).not.toThrow();
      });
    });
  });
});
