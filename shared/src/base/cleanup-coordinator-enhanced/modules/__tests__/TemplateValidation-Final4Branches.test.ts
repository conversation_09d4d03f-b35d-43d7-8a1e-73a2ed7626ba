/**
 * Final 4 Branch Coverage - Ultra-Precise Surgical Tests
 * Target: The exact 4 remaining uncovered branches to achieve 100% branch coverage
 * 
 * STRATEGY: Target the TRUE branches of instanceof Error ternary operators
 * and complex logical operators that are currently uncovered.
 */

import { jest } from '@jest/globals';
import { CleanupOperationType, CleanupPriority } from '../../../CleanupCoordinatorEnhanced';
import { ICleanupTemplate } from '../../../types/CleanupTypes';

// Test helper function
function createTestTemplate(overrides: any = {}): ICleanupTemplate {
  return {
    id: 'final-test-template',
    name: 'Final Test Template',
    description: 'Template for final 4 branch coverage testing',
    version: '1.0.0',
    operations: [{
      id: 'final-operation',
      type: CleanupOperationType.RESOURCE_CLEANUP,
      componentPattern: 'final-.*',
      operationName: 'final-operation',
      parameters: {},
      timeout: 5000,
      retryPolicy: {
        maxRetries: 3,
        retryDelay: 1000,
        backoffMultiplier: 2,
        maxRetryDelay: 10000,
        retryOnErrors: []
      },
      dependsOn: [],
      priority: CleanupPriority.NORMAL,
      estimatedDuration: 1000,
      description: 'Final operation for branch coverage'
    }],
    rollbackSteps: [],
    conditions: [],
    metadata: {},
    tags: [],
    createdAt: new Date(),
    modifiedAt: new Date(),
    author: 'final-test',
    validationRules: [],
    ...overrides
  };
}

describe('Final 4 Branch Coverage - Ultra-Precise Targeting', () => {
  beforeEach(() => {
    jest.resetModules();
    jest.resetAllMocks();
  });

  afterEach(() => {
    jest.dontMock('../../../utils/ResilientTiming');
    jest.dontMock('../../../utils/ResilientMetrics');
    jest.dontMock('../TemplateDependencies');
    jest.resetModules();
    jest.resetAllMocks();
  });

  // TARGET: Line 210 TRUE branch - error instanceof Error ? error.message : String(error)
  it('should cover Line 210: instanceof Error TRUE branch', async () => {
    // Mock ResilientTimer to throw actual Error object (not string/object)
    jest.doMock('../../../utils/ResilientTiming', () => ({
      ResilientTimer: jest.fn().mockImplementation(() => {
        throw new Error('Actual Error object for TRUE branch coverage'); // Error object triggers TRUE branch
      })
    }));

    jest.resetModules();

    try {
      const { TemplateValidator } = await import('../TemplateValidation');

      // Constructor will hit Line 210 with Error object (TRUE branch) - wrapped in try-catch
      const validator = new TemplateValidator();
      expect(validator).toBeDefined();

      await validator.initialize();
      await validator.shutdown();
    } catch (error) {
      // Expected - the Error object should be caught and handled, triggering TRUE branch
      expect(error).toBeInstanceOf(Error);
      expect((error as Error).message).toContain('Actual Error object for TRUE branch coverage');
    }
  });

  // TARGET: Line 296 TRUE branch - error instanceof Error ? error.message : String(error)
  it('should cover Line 296: instanceof Error TRUE branch', async () => {
    const { TemplateValidator } = await import('../TemplateValidation');
    const validator = new TemplateValidator();
    
    // Mock _resilientTimer to throw actual Error during reconfiguration
    (validator as any)._resilientTimer = {
      reconfigure: jest.fn().mockImplementation(() => {
        throw new Error('Actual Error object for Line 296 TRUE branch'); // Error object triggers TRUE branch
      }),
      start: () => ({ end: () => ({ duration: 0, reliable: true, startTime: Date.now(), endTime: Date.now() }) })
    };
    
    // Initialize will trigger reconfiguration and hit Line 296 TRUE branch
    await validator.initialize();
    await validator.shutdown();
  });

  // TARGET: Line 553 TRUE branch - error instanceof Error ? error.message : String(error)
  it('should cover Line 553: instanceof Error TRUE branch', async () => {
    // Mock TemplateDependencies to throw actual Error object
    jest.doMock('../TemplateDependencies', () => ({
      DependencyGraph: jest.fn().mockImplementation(function() {
        return {
          addNode: jest.fn(),
          addDependency: jest.fn().mockImplementation(() => {
            throw new Error('Actual Error object for Line 553 TRUE branch'); // Error object triggers TRUE branch
          }),
          validateDependencies: jest.fn(),
          getExecutionOrder: jest.fn().mockReturnValue([]),
          hasCycles: jest.fn().mockReturnValue(false),
          nodes: new Set(),
          edges: new Map()
        };
      }),
      createDependencyGraphFromOperations: jest.fn()
    }));

    jest.resetModules();
    const { TemplateValidator } = await import('../TemplateValidation');
    const validator = new TemplateValidator();
    await validator.initialize();
    
    // Template with dependencies will trigger Line 553 TRUE branch
    const template = createTestTemplate({
      operations: [{
        id: 'error-dep-op',
        type: CleanupOperationType.RESOURCE_CLEANUP,
        componentPattern: 'error-dep-.*',
        operationName: 'error-dependency-operation',
        parameters: {},
        timeout: 5000,
        retryPolicy: { maxRetries: 3, retryDelay: 1000, backoffMultiplier: 2, maxRetryDelay: 10000, retryOnErrors: [] },
        dependsOn: ['error-other-op'], // Triggers dependency validation with Error object
        priority: CleanupPriority.NORMAL,
        estimatedDuration: 1000,
        description: 'Error dependency operation'
      }]
    });
    
    const result = await validator.validateTemplate(template);
    expect(result).toBeDefined();
    
    await validator.shutdown();
  });

  // TARGET: Line 871 complex logical branches - template.operations?.filter(op => !op.id || op.id.trim().length === 0) || []
  it('should cover Line 871: complex logical operator branches', async () => {
    const { TemplateValidator } = await import('../TemplateValidation');
    const validator = new TemplateValidator();
    await validator.initialize();
    
    // Test multiple branch combinations in the complex logical expression
    
    // Branch 1: template.operations exists, op.id is falsy (!op.id TRUE)
    const templateFalsyId = createTestTemplate({
      operations: [{
        id: '', // Empty string - falsy, triggers !op.id TRUE branch
        type: CleanupOperationType.RESOURCE_CLEANUP,
        componentPattern: 'falsy-id-.*',
        operationName: 'falsy-id-operation',
        parameters: {},
        timeout: 5000,
        retryPolicy: { maxRetries: 3, retryDelay: 1000, backoffMultiplier: 2, maxRetryDelay: 10000, retryOnErrors: [] },
        dependsOn: [],
        priority: CleanupPriority.NORMAL,
        estimatedDuration: 1000,
        description: 'Falsy ID operation'
      }]
    });
    
    const result1 = await validator.validateTemplate(templateFalsyId);
    expect(result1).toBeDefined();
    
    // Branch 2: template.operations exists, op.id is truthy but trim().length === 0
    const templateWhitespaceId = createTestTemplate({
      operations: [{
        id: '   ', // Whitespace only - truthy but trim().length === 0, triggers second part of OR
        type: CleanupOperationType.RESOURCE_CLEANUP,
        componentPattern: 'whitespace-id-.*',
        operationName: 'whitespace-id-operation',
        parameters: {},
        timeout: 5000,
        retryPolicy: { maxRetries: 3, retryDelay: 1000, backoffMultiplier: 2, maxRetryDelay: 10000, retryOnErrors: [] },
        dependsOn: [],
        priority: CleanupPriority.NORMAL,
        estimatedDuration: 1000,
        description: 'Whitespace ID operation'
      }]
    });
    
    const result2 = await validator.validateTemplate(templateWhitespaceId);
    expect(result2).toBeDefined();
    
    // Branch 3: template.operations exists, op.id is valid (both conditions FALSE)
    const templateValidId = createTestTemplate({
      operations: [{
        id: 'valid-id', // Valid ID - both !op.id and op.id.trim().length === 0 are FALSE
        type: CleanupOperationType.RESOURCE_CLEANUP,
        componentPattern: 'valid-id-.*',
        operationName: 'valid-id-operation',
        parameters: {},
        timeout: 5000,
        retryPolicy: { maxRetries: 3, retryDelay: 1000, backoffMultiplier: 2, maxRetryDelay: 10000, retryOnErrors: [] },
        dependsOn: [],
        priority: CleanupPriority.NORMAL,
        estimatedDuration: 1000,
        description: 'Valid ID operation'
      }]
    });
    
    const result3 = await validator.validateTemplate(templateValidId);
    expect(result3).toBeDefined();
    
    await validator.shutdown();
  });
});
