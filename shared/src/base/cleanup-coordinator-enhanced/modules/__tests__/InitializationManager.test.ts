/**
 * @file InitializationManager Test Suite
 * @filepath shared/src/base/modules/cleanup/__tests__/InitializationManager.test.ts
 * @description Comprehensive test suite for InitializationManager
 */

import { InitializationManager, InitializationContext } from '../InitializationManager';
import { IEnhancedCleanupConfig, ICleanupCoordinatorConfig } from '../../../types/CleanupTypes';
import { ILoggingService } from '../../../LoggingMixin';
import { CleanupTemplateManager } from '../CleanupTemplateManager';
import { DependencyResolver } from '../DependencyResolver';
import { RollbackManager } from '../RollbackManager';
import { SystemOrchestrator } from '../SystemOrchestrator';

// Mock dependencies
jest.mock('../CleanupTemplateManager');
jest.mock('../DependencyResolver');
jest.mock('../RollbackManager');
jest.mock('../SystemOrchestrator');
jest.mock('../CleanupConfiguration', () => ({
  createDefaultComponentRegistry: jest.fn().mockReturnValue({
    findComponents: jest.fn().mockResolvedValue(['test-component-1', 'test-component-2']),
    getCleanupOperation: jest.fn().mockReturnValue(jest.fn()),
    registerOperation: jest.fn().mockReturnValue(true),
    hasOperation: jest.fn().mockReturnValue(true),
    listOperations: jest.fn().mockReturnValue(['operation1', 'operation2']),
    getOperationMetrics: jest.fn().mockReturnValue({
      totalOperations: 10,
      executionCount: 8,
      averageExecutionTime: 150,
      successRate: 0.8,
      lastExecution: new Date()
    })
  }),
  DEFAULT_ENHANCED_CLEANUP_CONFIG: {
    maxConcurrentOperations: 10,
    defaultTimeout: 30000,
    maxRetries: 3,
    conflictDetectionEnabled: true,
    metricsEnabled: true,
    cleanupIntervalMs: 300000,
    testMode: true,
    templateValidationEnabled: true,
    dependencyOptimizationEnabled: true,
    rollbackEnabled: true,
    maxCheckpoints: 100,
    checkpointRetentionDays: 7,
    phaseIntegrationEnabled: true,
    performanceMonitoringEnabled: true
  }
}));

describe('InitializationManager', () => {
  let initializationManager: InitializationManager;
  let mockLogger: jest.Mocked<ILoggingService>;
  let mockTemplateManager: jest.Mocked<CleanupTemplateManager>;
  let mockDependencyResolver: jest.Mocked<DependencyResolver>;
  let mockRollbackManager: jest.Mocked<RollbackManager>;
  let mockSystemOrchestrator: jest.Mocked<SystemOrchestrator>;
  let enhancedConfig: Required<IEnhancedCleanupConfig>;
  let baseConfig: Required<ICleanupCoordinatorConfig>;

  beforeEach(() => {
    // Setup mock logger
    mockLogger = {
      logInfo: jest.fn(),
      logError: jest.fn(),
      logWarning: jest.fn(),
      logDebug: jest.fn()
    };

    // Setup configurations
    enhancedConfig = {
      maxConcurrentOperations: 10,
      defaultTimeout: 30000,
      maxRetries: 3,
      conflictDetectionEnabled: true,
      metricsEnabled: true,
      cleanupIntervalMs: 300000,
      testMode: false,
      templateValidationEnabled: true,
      dependencyOptimizationEnabled: true,
      rollbackEnabled: true,
      maxCheckpoints: 100,
      checkpointRetentionDays: 7,
      phaseIntegrationEnabled: true,
      performanceMonitoringEnabled: true
    };

    baseConfig = {
      maxConcurrentOperations: 10,
      defaultTimeout: 30000,
      maxRetries: 3,
      conflictDetectionEnabled: true,
      metricsEnabled: true,
      cleanupIntervalMs: 300000,
      testMode: false
    };

    // Setup mock components
    mockTemplateManager = new CleanupTemplateManager(enhancedConfig) as jest.Mocked<CleanupTemplateManager>;
    mockDependencyResolver = new DependencyResolver(enhancedConfig) as jest.Mocked<DependencyResolver>;
    mockRollbackManager = new RollbackManager(enhancedConfig) as jest.Mocked<RollbackManager>;
    mockSystemOrchestrator = new SystemOrchestrator(enhancedConfig) as jest.Mocked<SystemOrchestrator>;

    // Mock initialize methods
    (mockTemplateManager as any).initialize = jest.fn().mockResolvedValue(undefined);
    (mockDependencyResolver as any).initialize = jest.fn().mockResolvedValue(undefined);
    (mockRollbackManager as any).initialize = jest.fn().mockResolvedValue(undefined);
    (mockSystemOrchestrator as any).initialize = jest.fn().mockResolvedValue(undefined);

    // Create InitializationManager instance
    initializationManager = new InitializationManager(enhancedConfig, baseConfig, mockLogger);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('initializeModularComponents', () => {
    it('should initialize all modular components successfully', async () => {
      await initializationManager.initializeModularComponents(
        mockTemplateManager,
        mockDependencyResolver,
        mockRollbackManager,
        mockSystemOrchestrator
      );

      expect(mockLogger.logInfo).toHaveBeenCalledWith('Initializing modular components', {
        templateValidationEnabled: true,
        rollbackEnabled: true,
        testMode: false
      });

      expect((mockTemplateManager as any).initialize).toHaveBeenCalled();
      expect((mockDependencyResolver as any).initialize).toHaveBeenCalled();
      expect((mockRollbackManager as any).initialize).toHaveBeenCalled();
      expect((mockSystemOrchestrator as any).initialize).toHaveBeenCalled();

      expect(mockLogger.logInfo).toHaveBeenCalledWith('All modular components initialized successfully');
    });

    it('should handle initialization errors and enhance error context', async () => {
      const initError = new Error('Template manager initialization failed');
      (mockTemplateManager as any).initialize.mockRejectedValue(initError);

      await expect(
        initializationManager.initializeModularComponents(
          mockTemplateManager,
          mockDependencyResolver,
          mockRollbackManager,
          mockSystemOrchestrator
        )
      ).rejects.toThrow();

      expect(mockLogger.logError).toHaveBeenCalledWith('Modular component initialization failed', initError);
    });
  });

  describe('initializeComponentRegistry', () => {
    it('should initialize component registry successfully', async () => {
      const registry = await initializationManager.initializeComponentRegistry();

      expect(registry).toBeDefined();
      expect(mockLogger.logInfo).toHaveBeenCalledWith('Component registry initialized successfully');
    });

    it('should handle registry initialization errors', async () => {
      // Create a new manager instance to test initialization failure
      const failingManager = new InitializationManager(enhancedConfig, baseConfig, mockLogger);

      // Mock the createDefaultComponentRegistry function to throw an error
      const mockError = new Error('Registry creation failed');
      const originalFunction = require('../CleanupConfiguration').createDefaultComponentRegistry;

      // Temporarily replace the function
      require('../CleanupConfiguration').createDefaultComponentRegistry = jest.fn().mockImplementation(() => {
        throw mockError;
      });

      try {
        await expect(failingManager.initializeComponentRegistry()).rejects.toThrow();
        expect(mockLogger.logError).toHaveBeenCalledWith('Component registry initialization failed', mockError);
      } finally {
        // Restore the original function
        require('../CleanupConfiguration').createDefaultComponentRegistry = originalFunction;
      }
    });
  });

  describe('setupEnhancedConfiguration', () => {
    it('should merge configuration with defaults', () => {
      const partialConfig = { testMode: true, maxRetries: 5 };
      const result = initializationManager.setupEnhancedConfiguration(partialConfig);

      expect(result.testMode).toBe(true);
      expect(result.maxRetries).toBe(5);
      expect(result.templateValidationEnabled).toBe(true); // from defaults
    });
  });

  describe('setupBaseConfiguration', () => {
    it('should setup base configuration with defaults', () => {
      const partialConfig = { testMode: true, maxRetries: 5 };
      const result = initializationManager.setupBaseConfiguration(partialConfig);

      expect(result.testMode).toBe(true);
      expect(result.maxRetries).toBe(5);
      expect(result.maxConcurrentOperations).toBe(10); // from defaults
    });

    it('should handle metricsEnabled mapping', () => {
      const partialConfig = { metricsEnabled: false };
      const result = initializationManager.setupBaseConfiguration(partialConfig);

      expect(result.metricsEnabled).toBe(false);
    });
  });

  describe('validateInitializationState', () => {
    it('should return true for valid configuration', () => {
      const result = initializationManager.validateInitializationState();
      expect(result).toBe(true);
    });

    it('should return false for invalid configuration', () => {
      // Create manager with incomplete config
      const incompleteConfig = { ...enhancedConfig };
      delete (incompleteConfig as any).maxConcurrentOperations;
      
      const invalidManager = new InitializationManager(incompleteConfig, baseConfig, mockLogger);
      const result = invalidManager.validateInitializationState();
      
      expect(result).toBe(false);
      expect(mockLogger.logError).toHaveBeenCalled();
    });
  });

  describe('enhanceErrorContext', () => {
    it('should enhance error with initialization context', () => {
      const originalError = new Error('Test error');
      const context: InitializationContext = {
        component: 'TestComponent',
        phase: 'test_phase',
        timestamp: '2025-08-07T16:00:00.000Z'
      };

      const enhancedError = initializationManager.enhanceErrorContext(originalError, context);

      expect(enhancedError.message).toBe('Test error');
      expect((enhancedError as any).initializationContext).toEqual(context);
      expect((enhancedError as any).component).toBe('InitializationManager');
    });
  });

  describe('getInitializationStatus', () => {
    it('should return initialization status', () => {
      const status = initializationManager.getInitializationStatus();

      expect(status).toEqual({
        configurationValid: true,
        enhancedConfigLoaded: true,
        baseConfigLoaded: true,
        registryReady: true
      });
    });
  });

  // ============================================================================
  // COMPREHENSIVE COVERAGE TESTS - TARGETING UNCOVERED LINES 138-172
  // ============================================================================

  describe('🎯 Main Initialize Method - Lines 138-172 Coverage', () => {
    let mockTimingInfrastructureManager: any;
    let mockResilientTimer: any;
    let mockResilientMetricsCollector: any;

    beforeEach(() => {
      // Setup mock timing infrastructure manager
      mockTimingInfrastructureManager = {
        initialize: jest.fn().mockResolvedValue(undefined)
      };

      // Setup mock resilient components
      mockResilientTimer = {
        start: jest.fn(),
        end: jest.fn(),
        isReliable: jest.fn().mockReturnValue(true)
      };

      mockResilientMetricsCollector = {
        recordMetric: jest.fn(),
        getMetrics: jest.fn().mockReturnValue({})
      };

      // Mock the require calls for ResilientTimer and ResilientMetricsCollector
      jest.doMock('../../../utils/ResilientTiming', () => ({
        ResilientTimer: jest.fn().mockImplementation(() => mockResilientTimer)
      }));

      jest.doMock('../../../utils/ResilientMetrics', () => ({
        ResilientMetricsCollector: jest.fn().mockImplementation(() => mockResilientMetricsCollector)
      }));
    });

    afterEach(() => {
      jest.clearAllMocks();
    });

    it('should execute complete initialization workflow successfully', async () => {
      // Test the main initialize method (lines 138-172)
      const resilientTimerConfig = {
        enableFallbacks: true,
        maxExpectedDuration: 30000,
        unreliableThreshold: 3,
        estimateBaseline: 50
      };

      const metricsCollectorConfig = {
        enableFallbacks: true,
        cacheUnreliableValues: false,
        maxMetricsAge: 300000,
        defaultEstimates: new Map([
          ['template_execution', 2000],
          ['checkpoint_creation', 500]
        ])
      };

      const result = await initializationManager.initialize(
        mockTimingInfrastructureManager,
        enhancedConfig,
        resilientTimerConfig,
        metricsCollectorConfig,
        mockTemplateManager,
        mockDependencyResolver,
        mockRollbackManager,
        mockSystemOrchestrator
      );

      // Verify timing infrastructure initialization (line 142)
      expect(mockTimingInfrastructureManager.initialize).toHaveBeenCalledWith(enhancedConfig);

      // Verify modular components initialization was called (lines 152-157)
      expect((mockTemplateManager as any).initialize).toHaveBeenCalled();
      expect((mockDependencyResolver as any).initialize).toHaveBeenCalled();
      expect((mockRollbackManager as any).initialize).toHaveBeenCalled();
      expect((mockSystemOrchestrator as any).initialize).toHaveBeenCalled();

      // Verify logging (lines 138, 159)
      expect(mockLogger.logInfo).toHaveBeenCalledWith('CleanupCoordinatorEnhanced initializing with extracted managers');
      expect(mockLogger.logInfo).toHaveBeenCalledWith('All modular components initialized successfully via InitializationManager');

      // Verify return value (line 161)
      expect(result).toEqual({
        resilientTimer: mockResilientTimer,
        metricsCollector: mockResilientMetricsCollector
      });
    });

    it('should handle timing infrastructure initialization errors', async () => {
      // Test error handling in timing infrastructure initialization (line 142)
      const initError = new Error('Timing infrastructure failed');
      mockTimingInfrastructureManager.initialize.mockRejectedValue(initError);

      const resilientTimerConfig = { enableFallbacks: true };
      const metricsCollectorConfig = { enableFallbacks: true };

      await expect(
        initializationManager.initialize(
          mockTimingInfrastructureManager,
          enhancedConfig,
          resilientTimerConfig,
          metricsCollectorConfig,
          mockTemplateManager,
          mockDependencyResolver,
          mockRollbackManager,
          mockSystemOrchestrator
        )
      ).rejects.toThrow();

      // Verify error enhancement was called (lines 164-172)
      expect(mockTimingInfrastructureManager.initialize).toHaveBeenCalledWith(enhancedConfig);
    });

    it('should handle modular component initialization errors with enhanced context', async () => {
      // Test error handling in modular component initialization (lines 152-157)
      const componentError = new Error('Template manager failed');
      (mockTemplateManager as any).initialize.mockRejectedValue(componentError);

      const resilientTimerConfig = { enableFallbacks: true };
      const metricsCollectorConfig = { enableFallbacks: true };

      await expect(
        initializationManager.initialize(
          mockTimingInfrastructureManager,
          enhancedConfig,
          resilientTimerConfig,
          metricsCollectorConfig,
          mockTemplateManager,
          mockDependencyResolver,
          mockRollbackManager,
          mockSystemOrchestrator
        )
      ).rejects.toThrow();

      // Verify timing infrastructure was initialized first
      expect(mockTimingInfrastructureManager.initialize).toHaveBeenCalledWith(enhancedConfig);
    });

    it('should handle non-Error objects in initialization catch block', async () => {
      // Test error conversion logic: error instanceof Error ? error : new Error(String(error))
      // This targets the ternary operator in the catch block (lines 165)
      const nonErrorObject = 'string-error-for-initialization';
      mockTimingInfrastructureManager.initialize.mockRejectedValue(nonErrorObject);

      const resilientTimerConfig = { enableFallbacks: true };
      const metricsCollectorConfig = { enableFallbacks: true };

      try {
        await initializationManager.initialize(
          mockTimingInfrastructureManager,
          enhancedConfig,
          resilientTimerConfig,
          metricsCollectorConfig,
          mockTemplateManager,
          mockDependencyResolver,
          mockRollbackManager,
          mockSystemOrchestrator
        );
        fail('Expected initialization to throw');
      } catch (error) {
        // Verify the error was converted to Error instance
        expect(error).toBeInstanceOf(Error);
        expect((error as Error).message).toBe('string-error-for-initialization');

        // Verify error enhancement was applied (lines 164-172)
        expect((error as any).initializationContext).toEqual({
          component: 'CleanupCoordinatorEnhanced',
          phase: 'initialization',
          timestamp: expect.any(String)
        });
      }
    });
  });

  // ============================================================================
  // SURGICAL PRECISION TESTS - TARGETING SPECIFIC BRANCHES
  // ============================================================================

  describe('🎯 Branch Coverage - Error Handling Edge Cases', () => {
    it('should handle non-Error objects in modular component initialization', async () => {
      // Test the error conversion in initializeModularComponents (line 202)
      const nonErrorObject = { customError: 'template-failure', code: 500 };
      (mockTemplateManager as any).initialize.mockRejectedValue(nonErrorObject);

      try {
        await initializationManager.initializeModularComponents(
          mockTemplateManager,
          mockDependencyResolver,
          mockRollbackManager,
          mockSystemOrchestrator
        );
        fail('Expected initializeModularComponents to throw');
      } catch (error) {
        // Verify the error was converted to Error instance (line 202)
        expect(error).toBeInstanceOf(Error);
        expect((error as any).initializationContext).toBeDefined();

        // Verify logging occurred
        expect(mockLogger.logError).toHaveBeenCalledWith(
          'Modular component initialization failed',
          expect.any(Error)
        );
      }
    });

    it('should handle non-Error objects in component registry initialization', async () => {
      // Test the error conversion in initializeComponentRegistry (line 222)
      const nonErrorObject = 'registry-creation-failed';

      // Mock the createDefaultComponentRegistry function to throw non-Error
      const originalFunction = require('../CleanupConfiguration').createDefaultComponentRegistry;
      require('../CleanupConfiguration').createDefaultComponentRegistry = jest.fn().mockImplementation(() => {
        throw nonErrorObject;
      });

      try {
        await initializationManager.initializeComponentRegistry();
        fail('Expected initializeComponentRegistry to throw');
      } catch (error) {
        // Verify the error was converted to Error instance (line 222)
        expect(error).toBeInstanceOf(Error);
        expect((error as any).initializationContext).toBeDefined();

        // Verify logging occurred
        expect(mockLogger.logError).toHaveBeenCalledWith(
          'Component registry initialization failed',
          expect.any(Error)
        );
      } finally {
        // Restore the original function
        require('../CleanupConfiguration').createDefaultComponentRegistry = originalFunction;
      }
    });
  });

  // ============================================================================
  // CONFIGURATION EDGE CASES - TARGETING SPECIFIC BRANCHES
  // ============================================================================

  describe('🎯 Configuration Edge Cases - Branch Coverage', () => {
    it('should handle undefined configuration values in setupBaseConfiguration', () => {
      // Test all the conditional branches in setupBaseConfiguration (lines 257-264)
      const partialConfig = {
        defaultTimeout: undefined,
        maxConcurrentOperations: undefined,
        conflictDetectionEnabled: undefined,
        metricsEnabled: undefined,
        testMode: undefined,
        maxRetries: undefined,
        cleanupIntervalMs: undefined
      };

      const result = initializationManager.setupBaseConfiguration(partialConfig);

      // Verify all defaults are applied when values are undefined
      expect(result.defaultTimeout).toBe(30000);
      expect(result.maxConcurrentOperations).toBe(10);
      expect(result.conflictDetectionEnabled).toBe(true);
      expect(result.metricsEnabled).toBe(true);
      expect(result.testMode).toBe(false);
      expect(result.maxRetries).toBe(3);
      expect(result.cleanupIntervalMs).toBe(300000);
    });

    it('should handle false boolean values in setupBaseConfiguration', () => {
      // Test the ?? operator branches for boolean values (lines 260-261)
      const partialConfig = {
        conflictDetectionEnabled: false,
        metricsEnabled: false,
        testMode: false
      };

      const result = initializationManager.setupBaseConfiguration(partialConfig);

      // Verify false values are preserved (not overridden by defaults)
      expect(result.conflictDetectionEnabled).toBe(false);
      expect(result.metricsEnabled).toBe(false);
      expect(result.testMode).toBe(false);
    });

    it('should handle zero values in setupBaseConfiguration', () => {
      // Test the || operator branches for numeric values
      const partialConfig = {
        defaultTimeout: 0,
        maxConcurrentOperations: 0,
        maxRetries: 0,
        cleanupIntervalMs: 0
      };

      const result = initializationManager.setupBaseConfiguration(partialConfig);

      // Verify zero values trigger defaults (|| operator behavior)
      expect(result.defaultTimeout).toBe(30000);
      expect(result.maxConcurrentOperations).toBe(10);
      expect(result.maxRetries).toBe(3);
      expect(result.cleanupIntervalMs).toBe(300000);
    });
  });

  // ============================================================================
  // VALIDATION LOGIC - COMPREHENSIVE BRANCH COVERAGE
  // ============================================================================

  describe('🎯 Validation Logic - Complete Branch Coverage', () => {
    it('should validate all required fields individually', () => {
      // Test each required field validation branch (lines 280-285)
      const requiredFields = [
        'maxConcurrentOperations',
        'defaultTimeout',
        'maxRetries',
        'templateValidationEnabled',
        'rollbackEnabled'
      ];

      requiredFields.forEach(field => {
        // Create config missing specific field
        const incompleteConfig = { ...enhancedConfig };
        delete (incompleteConfig as any)[field];

        const invalidManager = new InitializationManager(incompleteConfig, baseConfig, mockLogger);
        const result = invalidManager.validateInitializationState();

        expect(result).toBe(false);
        expect(mockLogger.logError).toHaveBeenCalledWith(
          `Missing required configuration field: ${field}`,
          expect.any(Error)
        );

        // Clear mocks for next iteration
        jest.clearAllMocks();
      });
    });

    it('should return true when all required fields are present', () => {
      // Test the successful validation path (line 287)
      const result = initializationManager.validateInitializationState();
      expect(result).toBe(true);

      // Verify no error logging occurred
      expect(mockLogger.logError).not.toHaveBeenCalled();
    });

    it('should handle configuration with extra fields', () => {
      // Test validation with additional fields that shouldn't affect validation
      const extendedConfig = {
        ...enhancedConfig,
        extraField: 'should-not-affect-validation',
        anotherField: 12345
      };

      const extendedManager = new InitializationManager(extendedConfig as any, baseConfig, mockLogger);
      const result = extendedManager.validateInitializationState();

      expect(result).toBe(true);
      expect(mockLogger.logError).not.toHaveBeenCalled();
    });
  });

  // ============================================================================
  // ERROR ENHANCEMENT - COMPREHENSIVE TESTING
  // ============================================================================

  describe('🎯 Error Enhancement - Complete Functionality', () => {
    it('should preserve original error properties', () => {
      const originalError = new Error('Original message');
      originalError.name = 'CustomError';
      originalError.stack = 'Original stack trace';

      const context: InitializationContext = {
        component: 'TestComponent',
        phase: 'test_phase',
        timestamp: '2025-08-07T16:00:00.000Z',
        customField: 'custom-value'
      };

      const enhancedError = initializationManager.enhanceErrorContext(originalError, context);

      // Verify original properties are preserved (lines 295-297)
      expect(enhancedError.message).toBe('Original message');
      expect(enhancedError.name).toBe('CustomError');
      expect(enhancedError.stack).toBe('Original stack trace');
    });

    it('should add initialization context and metadata', () => {
      const originalError = new Error('Test error');
      const context: InitializationContext = {
        component: 'TestComponent',
        phase: 'test_phase',
        timestamp: '2025-08-07T16:00:00.000Z',
        additionalData: { key: 'value' }
      };

      const enhancedError = initializationManager.enhanceErrorContext(originalError, context);

      // Verify context and metadata are added (lines 300-304)
      expect((enhancedError as any).initializationContext).toEqual(context);
      expect((enhancedError as any).timestamp).toBeDefined();
      expect((enhancedError as any).component).toBe('InitializationManager');
    });

    it('should handle context with dynamic properties', () => {
      const originalError = new Error('Dynamic context test');
      const context: InitializationContext = {
        component: 'DynamicComponent',
        phase: 'dynamic_phase',
        timestamp: '2025-08-07T16:00:00.000Z',
        // Test dynamic properties
        requestId: 'req-12345',
        userId: 'user-67890',
        operationId: 'op-abcdef'
      };

      const enhancedError = initializationManager.enhanceErrorContext(originalError, context);

      // Verify all dynamic properties are preserved
      expect((enhancedError as any).initializationContext).toEqual(context);
      expect((enhancedError as any).initializationContext.requestId).toBe('req-12345');
      expect((enhancedError as any).initializationContext.userId).toBe('user-67890');
      expect((enhancedError as any).initializationContext.operationId).toBe('op-abcdef');
    });
  });

  // ============================================================================
  // STATUS REPORTING - EDGE CASES
  // ============================================================================

  describe('🎯 Status Reporting - Edge Cases', () => {
    it('should handle status with invalid configuration', () => {
      // Create manager with invalid config to test status reporting
      const invalidConfig = { ...enhancedConfig };
      delete (invalidConfig as any).maxConcurrentOperations;

      const invalidManager = new InitializationManager(invalidConfig, baseConfig, mockLogger);
      const status = invalidManager.getInitializationStatus();

      expect(status).toEqual({
        configurationValid: false,
        enhancedConfigLoaded: true,
        baseConfigLoaded: true,
        registryReady: true
      });
    });

    it('should handle status with null configurations', () => {
      // Test edge case with null configurations - need to handle gracefully
      const nullConfigManager = new InitializationManager(null as any, null as any, mockLogger);

      // The validation will throw when accessing null config, so we need to catch it
      expect(() => {
        nullConfigManager.getInitializationStatus();
      }).toThrow();

      // Verify that the error is due to null configuration access
      expect(mockLogger.logError).not.toHaveBeenCalled(); // Error occurs before logging
    });
  });
});
