/**
 * @file UtilityExecution Comprehensive Test Suite
 * @description Comprehensive test coverage for UtilityExecution.ts using surgical precision testing methodologies
 * @coverage-target 100% lines, 100% branches, 100% functions
 * @testing-approach Surgical precision testing with direct method access and strategic error injection
 * @compliance MEM-SAFE-002, Anti-Simplification Policy, Enterprise-grade quality
 */

import {
  generateExecutionId,
  generateCheckpointId,
  findMatchingComponents,
  estimateOperationDuration,
  sortOperationsByDependencies,
  ExecutionUtils,
  UtilityExecutionEnhanced
} from '../UtilityExecution';

import { CleanupPriority, CleanupOperationType, ICleanupOperation } from '../../../CleanupCoordinatorEnhanced';

// Mock resilient timing infrastructure - must be done before importing the module
jest.mock('../../../utils/ResilientTiming', () => ({
  ResilientTimer: jest.fn().mockImplementation(() => ({
    start: jest.fn().mockReturnValue({
      end: jest.fn().mockReturnValue({
        duration: 100,
        reliable: true,
        method: 'performance'
      })
    })
  }))
}));

jest.mock('../../../utils/ResilientMetrics', () => ({
  ResilientMetricsCollector: jest.fn().mockImplementation(() => ({
    recordTiming: jest.fn()
  }))
}));

describe('UtilityExecution', () => {
  // Helper functions to access mocked instances
  const getMockTimer = () => require('../../../utils/ResilientTiming').ResilientTimer;
  const getMockMetrics = () => require('../../../utils/ResilientMetrics').ResilientMetricsCollector;

  // Test data setup
  const createMockOperation = (
    id: string,
    priority: CleanupPriority = CleanupPriority.NORMAL,
    type: CleanupOperationType = CleanupOperationType.RESOURCE_CLEANUP,
    dependencies: string[] = []
  ): ICleanupOperation => ({
    id,
    type,
    priority,
    status: 'pending' as any,
    componentId: `component-${id}`,
    operation: async () => {},
    dependencies,
    timeout: 5000,
    retryCount: 0,
    maxRetries: 3,
    createdAt: new Date()
  });

  beforeEach(() => {
    jest.clearAllMocks();
    // Reset Date.now and Math.random for consistent testing
    jest.spyOn(Date, 'now').mockReturnValue(1640995200000); // Fixed timestamp
    jest.spyOn(Math, 'random').mockReturnValue(0.123456789); // Fixed random value
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('🔧 ID Generation Utilities', () => {
    describe('generateExecutionId', () => {
      it('should generate consistent execution IDs with template ID', () => {
        const templateId = 'test-template';
        const executionId = generateExecutionId(templateId);

        // Test the pattern instead of exact value since random generation may vary
        expect(executionId).toMatch(/^template-exec-test-template-1640995200000-[a-z0-9]+$/);
        expect(executionId).toContain('template-exec-test-template-1640995200000');
      });

      it('should generate unique IDs for different template IDs', () => {
        const id1 = generateExecutionId('template1');
        const id2 = generateExecutionId('template2');

        expect(id1).not.toBe(id2);
        expect(id1).toContain('template1');
        expect(id2).toContain('template2');
      });

      it('should handle empty template ID', () => {
        const executionId = generateExecutionId('');
        expect(executionId).toMatch(/^template-exec--1640995200000-[a-z0-9]+$/);
      });

      it('should handle special characters in template ID', () => {
        const executionId = generateExecutionId('template@#$%^&*()');
        expect(executionId).toContain('template@#$%^&*()');
        expect(executionId).toMatch(/^template-exec-.*-\d+-[a-z0-9]+$/);
      });
    });

    describe('generateCheckpointId', () => {
      it('should generate consistent checkpoint IDs with operation ID', () => {
        const operationId = 'test-operation';
        const checkpointId = generateCheckpointId(operationId);

        expect(checkpointId).toMatch(/^checkpoint-test-operation-1640995200000-[a-z0-9]+$/);
        expect(checkpointId).toContain('checkpoint-test-operation-1640995200000');
      });

      it('should generate unique IDs for different operation IDs', () => {
        const id1 = generateCheckpointId('operation1');
        const id2 = generateCheckpointId('operation2');

        expect(id1).not.toBe(id2);
        expect(id1).toContain('operation1');
        expect(id2).toContain('operation2');
      });

      it('should handle empty operation ID', () => {
        const checkpointId = generateCheckpointId('');
        expect(checkpointId).toMatch(/^checkpoint--1640995200000-[a-z0-9]+$/);
      });

      it('should handle special characters in operation ID', () => {
        const checkpointId = generateCheckpointId('operation@#$%^&*()');
        expect(checkpointId).toContain('operation@#$%^&*()');
        expect(checkpointId).toMatch(/^checkpoint-.*-\d+-[a-z0-9]+$/);
      });
    });
  });

  describe('🎯 Component Matching Utilities', () => {
    describe('findMatchingComponents', () => {
      const testComponents = [
        'user-service',
        'auth-service',
        'data-processor',
        'cache-manager',
        'log-handler',
        'UserController',
        'AuthController'
      ];

      it('should find components matching regex pattern (case insensitive)', () => {
        const matches = findMatchingComponents('user', testComponents);
        expect(matches).toEqual(['user-service', 'UserController']);
      });

      it('should find components matching complex regex pattern', () => {
        const matches = findMatchingComponents('.*-service$', testComponents);
        expect(matches).toEqual(['user-service', 'auth-service']);
      });

      it('should handle case insensitive matching', () => {
        const matches = findMatchingComponents('AUTH', testComponents);
        expect(matches).toEqual(['auth-service', 'AuthController']);
      });

      it('should return empty array when no matches found', () => {
        const matches = findMatchingComponents('nonexistent', testComponents);
        expect(matches).toEqual([]);
      });

      it('should handle empty components array', () => {
        const matches = findMatchingComponents('test', []);
        expect(matches).toEqual([]);
      });

      it('should handle empty pattern', () => {
        const matches = findMatchingComponents('', testComponents);
        expect(matches).toEqual(testComponents); // Empty pattern matches all
      });

      it('should fallback to string matching when regex fails', () => {
        // Test with invalid regex pattern that should trigger fallback
        const invalidRegexPattern = '[invalid-regex';
        const matches = findMatchingComponents(invalidRegexPattern, testComponents);

        // Should fallback to string matching (case insensitive)
        expect(matches).toEqual([]);
      });

      it('should use string fallback matching correctly', () => {
        // Force regex error by using invalid pattern
        const pattern = '[invalid-regex';
        const components = ['test-invalid-component', 'valid-component'];
        const matches = findMatchingComponents(pattern, components);

        // Should use string matching fallback and find no matches since 'invalid-regex' is not in the strings
        expect(matches).toEqual([]);
      });
    });
  });

  describe('⏱️ Operation Estimation Utilities', () => {
    describe('estimateOperationDuration', () => {
      it('should return default duration for undefined operation', () => {
        const duration = estimateOperationDuration(undefined);
        expect(duration).toBe(1000);
      });

      it('should estimate duration for CLEANUP operations', () => {
        const operation = createMockOperation('test', CleanupPriority.NORMAL, CleanupOperationType.RESOURCE_CLEANUP);
        const duration = estimateOperationDuration(operation);
        expect(duration).toBe(2000); // Base time for operations containing 'CLEANUP' (uppercase check)
      });

      it('should estimate duration for EVENT_HANDLER operations', () => {
        const operation = createMockOperation('test', CleanupPriority.NORMAL, CleanupOperationType.EVENT_HANDLER_CLEANUP);
        const duration = estimateOperationDuration(operation);
        expect(duration).toBe(2000); // Base time for operations containing 'CLEANUP' (uppercase check)
      });

      it('should estimate duration for MEMORY operations', () => {
        const operation = createMockOperation('test', CleanupPriority.NORMAL, CleanupOperationType.MEMORY_CLEANUP);
        const duration = estimateOperationDuration(operation);
        expect(duration).toBe(2000); // Base time for operations containing 'CLEANUP' (uppercase check)
      });

      it('should apply emergency priority multiplier', () => {
        const operation = createMockOperation('test', CleanupPriority.EMERGENCY, CleanupOperationType.RESOURCE_CLEANUP);
        const duration = estimateOperationDuration(operation);
        expect(duration).toBe(1000); // 2000 * 0.5 = 1000
      });

      it('should apply normal priority multiplier', () => {
        const operation = createMockOperation('test', CleanupPriority.NORMAL, CleanupOperationType.RESOURCE_CLEANUP);
        const duration = estimateOperationDuration(operation);
        expect(duration).toBe(2000); // 2000 * 1.0 = 2000
      });

      it('should ensure minimum duration of 100ms', () => {
        // Create operation that would result in very low duration
        const operation = createMockOperation('test', CleanupPriority.EMERGENCY, CleanupOperationType.EVENT_HANDLER_CLEANUP);
        const duration = estimateOperationDuration(operation);
        expect(duration).toBe(1000); // 2000 * 0.5 = 1000 (EVENT_HANDLER_CLEANUP contains 'CLEANUP')
      });

      it('should handle unknown operation types with default base time', () => {
        const operation = createMockOperation('test', CleanupPriority.NORMAL, 'UNKNOWN_TYPE' as any);
        const duration = estimateOperationDuration(operation);
        expect(duration).toBe(1000); // Default base time
      });

      it('should estimate duration for AUDIT operations (target line 148)', () => {
        const operation = createMockOperation('test', CleanupPriority.NORMAL, 'AUDIT_TRAIL' as any);
        const duration = estimateOperationDuration(operation);
        expect(duration).toBe(500); // Base time for operations containing 'AUDIT'
      });

      it('should estimate duration for OPTIMIZATION operations (target line 149)', () => {
        const operation = createMockOperation('test', CleanupPriority.NORMAL, 'PERFORMANCE_OPTIMIZATION' as any);
        const duration = estimateOperationDuration(operation);
        expect(duration).toBe(3000); // Base time for operations containing 'OPTIMIZATION'
      });

      it('should handle case insensitive type checking', () => {
        const auditOperation = createMockOperation('test', CleanupPriority.NORMAL, 'audit_process' as any);
        const optimizationOperation = createMockOperation('test', CleanupPriority.NORMAL, 'optimization_task' as any);

        expect(estimateOperationDuration(auditOperation)).toBe(500);
        expect(estimateOperationDuration(optimizationOperation)).toBe(3000);
      });
    });
  });

  describe('🔄 Operation Sorting Utilities', () => {
    describe('sortOperationsByDependencies', () => {
      it('should sort operations with simple dependency chain', () => {
        const operations = [
          createMockOperation('op3', CleanupPriority.NORMAL, CleanupOperationType.RESOURCE_CLEANUP, ['op2']),
          createMockOperation('op1', CleanupPriority.NORMAL, CleanupOperationType.RESOURCE_CLEANUP, []),
          createMockOperation('op2', CleanupPriority.NORMAL, CleanupOperationType.RESOURCE_CLEANUP, ['op1'])
        ];

        const sorted = sortOperationsByDependencies(operations);

        expect(sorted.map(op => op.id)).toEqual(['op1', 'op2', 'op3']);
      });

      it('should handle operations with no dependencies', () => {
        const operations = [
          createMockOperation('op2'),
          createMockOperation('op1'),
          createMockOperation('op3')
        ];

        const sorted = sortOperationsByDependencies(operations);

        // Should maintain original order when no dependencies
        expect(sorted.map(op => op.id)).toEqual(['op2', 'op1', 'op3']);
      });

      it('should handle circular dependencies gracefully', () => {
        const operations = [
          createMockOperation('op1', CleanupPriority.NORMAL, CleanupOperationType.RESOURCE_CLEANUP, ['op2']),
          createMockOperation('op2', CleanupPriority.NORMAL, CleanupOperationType.RESOURCE_CLEANUP, ['op1'])
        ];

        // Should not throw error and handle circular dependency
        expect(() => sortOperationsByDependencies(operations)).not.toThrow();

        const sorted = sortOperationsByDependencies(operations);
        expect(sorted).toHaveLength(2);
      });

      it('should handle missing dependency references', () => {
        const operations = [
          createMockOperation('op1', CleanupPriority.NORMAL, CleanupOperationType.RESOURCE_CLEANUP, ['nonexistent']),
          createMockOperation('op2', CleanupPriority.NORMAL, CleanupOperationType.RESOURCE_CLEANUP, [])
        ];

        const sorted = sortOperationsByDependencies(operations);
        expect(sorted).toHaveLength(2);
        expect(sorted.map(op => op.id)).toContain('op1');
        expect(sorted.map(op => op.id)).toContain('op2');
      });

      it('should handle empty operations array', () => {
        const sorted = sortOperationsByDependencies([]);
        expect(sorted).toEqual([]);
      });

      it('should handle complex dependency graph', () => {
        const operations = [
          createMockOperation('op5', CleanupPriority.NORMAL, CleanupOperationType.RESOURCE_CLEANUP, ['op3', 'op4']),
          createMockOperation('op3', CleanupPriority.NORMAL, CleanupOperationType.RESOURCE_CLEANUP, ['op1']),
          createMockOperation('op1', CleanupPriority.NORMAL, CleanupOperationType.RESOURCE_CLEANUP, []),
          createMockOperation('op4', CleanupPriority.NORMAL, CleanupOperationType.RESOURCE_CLEANUP, ['op2']),
          createMockOperation('op2', CleanupPriority.NORMAL, CleanupOperationType.RESOURCE_CLEANUP, [])
        ];

        const sorted = sortOperationsByDependencies(operations);
        const sortedIds = sorted.map(op => op.id);

        // Verify dependency order is maintained
        expect(sortedIds.indexOf('op1')).toBeLessThan(sortedIds.indexOf('op3'));
        expect(sortedIds.indexOf('op2')).toBeLessThan(sortedIds.indexOf('op4'));
        expect(sortedIds.indexOf('op3')).toBeLessThan(sortedIds.indexOf('op5'));
        expect(sortedIds.indexOf('op4')).toBeLessThan(sortedIds.indexOf('op5'));
      });

      it('should handle operations with undefined dependencies', () => {
        const operations = [
          {
            ...createMockOperation('op1'),
            dependencies: undefined
          },
          createMockOperation('op2', CleanupPriority.NORMAL, CleanupOperationType.RESOURCE_CLEANUP, ['op1'])
        ];

        const sorted = sortOperationsByDependencies(operations);
        expect(sorted).toHaveLength(2);
        expect(sorted.map(op => op.id)).toEqual(['op1', 'op2']);
      });
    });
  });

  describe('🎯 Resilient Timing Integration', () => {
    describe('Module-level timing infrastructure', () => {
      it('should successfully execute sorting with timing infrastructure', () => {
        const operations = [
          createMockOperation('op1'),
          createMockOperation('op2', CleanupPriority.NORMAL, CleanupOperationType.RESOURCE_CLEANUP, ['op1'])
        ];

        // This test verifies that the function executes successfully with the mocked timing infrastructure
        const sorted = sortOperationsByDependencies(operations);

        expect(sorted).toHaveLength(2);
        expect(sorted.map(op => op.id)).toEqual(['op1', 'op2']);
      });

      it('should handle timing infrastructure integration without errors', () => {
        const operations = [createMockOperation('op1')];

        // This test verifies that the timing infrastructure doesn't interfere with normal operation
        expect(() => sortOperationsByDependencies(operations)).not.toThrow();
      });

      it('should maintain functionality when timing infrastructure is mocked', () => {
        const operations = [
          createMockOperation('op3', CleanupPriority.NORMAL, CleanupOperationType.RESOURCE_CLEANUP, ['op1', 'op2']),
          createMockOperation('op1'),
          createMockOperation('op2', CleanupPriority.NORMAL, CleanupOperationType.RESOURCE_CLEANUP, ['op1'])
        ];

        const sorted = sortOperationsByDependencies(operations);

        // Should maintain correct dependency order
        expect(sorted).toHaveLength(3);
        const sortedIds = sorted.map(op => op.id);
        expect(sortedIds.indexOf('op1')).toBeLessThan(sortedIds.indexOf('op2'));
        expect(sortedIds.indexOf('op2')).toBeLessThan(sortedIds.indexOf('op3'));
      });

      it('should handle error scenarios gracefully with timing infrastructure', () => {
        // Test with null operations to trigger error handling
        expect(() => sortOperationsByDependencies(null as any)).toThrow();
      });
    });

    describe('UtilityExecutionEnhanced class', () => {
      let executionManager: UtilityExecutionEnhanced;

      beforeEach(() => {
        executionManager = new UtilityExecutionEnhanced();
      });

      it('should initialize with resilient timing infrastructure', () => {
        expect(executionManager).toBeDefined();

        const metrics = executionManager.getTimingMetrics();
        expect(metrics.timerAvailable).toBe(true);
        expect(metrics.metricsAvailable).toBe(true);
      });

      it('should handle doInitialize method', async () => {
        await expect(executionManager.doInitialize()).resolves.not.toThrow();

        const metrics = executionManager.getTimingMetrics();
        expect(metrics.initialized).toBe(true);
      });

      it('should handle doShutdown method', async () => {
        await expect(executionManager.doShutdown()).resolves.not.toThrow();

        const metrics = executionManager.getTimingMetrics();
        expect(metrics.initialized).toBe(false);
      });

      it('should plan execution with timing measurement', async () => {
        const operations = [
          createMockOperation('op1'),
          createMockOperation('op2', CleanupPriority.NORMAL, CleanupOperationType.RESOURCE_CLEANUP, ['op1'])
        ];

        const planned = await executionManager.planExecution(operations);

        expect(planned).toHaveLength(2);
        expect(planned.map(op => op.id)).toEqual(['op1', 'op2']);
      });

      it('should handle planning errors gracefully', async () => {
        // Test with null operations to trigger error handling
        await expect(executionManager.planExecution(null as any)).rejects.toThrow();
      });

      it('should test private _initializeResilientTimingSync method via constructor', () => {
        // Test the private method indirectly through constructor
        const manager = new UtilityExecutionEnhanced();
        const metrics = manager.getTimingMetrics();

        // Should have timing infrastructure available after construction
        expect(metrics.timerAvailable).toBe(true);
        expect(metrics.metricsAvailable).toBe(true);
      });

      it('should handle timing infrastructure failures in constructor', () => {
        // Mock ResilientTimer to throw error during construction
        const originalTimer = require('../../../utils/ResilientTiming').ResilientTimer;
        require('../../../utils/ResilientTiming').ResilientTimer = jest.fn().mockImplementation(() => {
          throw new Error('Timer construction failed');
        });

        // Should not throw error, should fallback gracefully
        expect(() => new UtilityExecutionEnhanced()).not.toThrow();

        // Restore original
        require('../../../utils/ResilientTiming').ResilientTimer = originalTimer;
      });

      it('should handle timing infrastructure failures in doInitialize', async () => {
        const manager = new UtilityExecutionEnhanced();

        // Mock ResilientTimer to throw error during reconfiguration
        const originalTimer = require('../../../utils/ResilientTiming').ResilientTimer;
        require('../../../utils/ResilientTiming').ResilientTimer = jest.fn().mockImplementation(() => {
          throw new Error('Timer reconfiguration failed');
        });

        // Should not throw error, should continue with fallback
        await expect(manager.doInitialize()).resolves.not.toThrow();

        // Restore original
        require('../../../utils/ResilientTiming').ResilientTimer = originalTimer;
      });

      it('should test doInitialize reconfiguration when not initialized', async () => {
        // Create manager and force _initialized to false to trigger reconfiguration
        const manager = new UtilityExecutionEnhanced();

        // Access private property to force uninitialized state
        (manager as any)._initialized = false;

        // This should trigger the reconfiguration logic (lines 278-303)
        await manager.doInitialize();

        const metrics = manager.getTimingMetrics();
        expect(metrics.initialized).toBe(true);
      });

      it('should test doInitialize error handling with console.warn', async () => {
        const manager = new UtilityExecutionEnhanced();

        // Force uninitialized state
        (manager as any)._initialized = false;

        // Mock console.warn to verify it's called
        const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation();

        // Mock ResilientTimer to throw error during reconfiguration
        const originalTimer = require('../../../utils/ResilientTiming').ResilientTimer;
        require('../../../utils/ResilientTiming').ResilientTimer = jest.fn().mockImplementation(() => {
          throw new Error('Reconfiguration failed');
        });

        // Should trigger error handling and console.warn
        await manager.doInitialize();

        expect(consoleWarnSpy).toHaveBeenCalledWith('Failed to reconfigure resilient timing infrastructure, using existing fallback');

        // Restore mocks
        require('../../../utils/ResilientTiming').ResilientTimer = originalTimer;
        consoleWarnSpy.mockRestore();
      });
    });
  });

  describe('📦 Execution Utility Collection', () => {
    describe('ExecutionUtils', () => {
      it('should export all utility functions', () => {
        expect(ExecutionUtils.generateExecutionId).toBe(generateExecutionId);
        expect(ExecutionUtils.generateCheckpointId).toBe(generateCheckpointId);
        expect(ExecutionUtils.findMatchingComponents).toBe(findMatchingComponents);
        expect(ExecutionUtils.estimateOperationDuration).toBe(estimateOperationDuration);
        expect(ExecutionUtils.sortOperationsByDependencies).toBe(sortOperationsByDependencies);
      });

      it('should provide access to all functions through the utility collection', () => {
        const operations = [createMockOperation('test')];
        const components = ['test-component'];

        // Test all functions are accessible and functional through ExecutionUtils
        expect(() => ExecutionUtils.generateExecutionId('test')).not.toThrow();
        expect(() => ExecutionUtils.generateCheckpointId('test')).not.toThrow();
        expect(() => ExecutionUtils.findMatchingComponents('test', components)).not.toThrow();
        expect(() => ExecutionUtils.estimateOperationDuration(operations[0])).not.toThrow();
        expect(() => ExecutionUtils.sortOperationsByDependencies(operations)).not.toThrow();
      });
    });
  });

  describe('🔍 Edge Cases and Error Handling', () => {
    describe('Regex error handling in component matching', () => {
      it('should handle invalid regex patterns gracefully', () => {
        const invalidPatterns = [
          '[unclosed-bracket',
          '*invalid*quantifier',
          '(?invalid-group',
          '+invalid+start'
        ];

        const components = ['test-component', 'another-component'];

        invalidPatterns.forEach(pattern => {
          expect(() => findMatchingComponents(pattern, components)).not.toThrow();
        });
      });

      it('should fallback to string matching for complex invalid regex', () => {
        const pattern = '[[[invalid';
        const components = ['invalid-test', 'valid-test', 'another'];

        const matches = findMatchingComponents(pattern, components);
        // Should use string fallback and find no matches since '[[[invalid' is not in the strings
        expect(matches).toEqual([]);
      });
    });

    describe('Boundary conditions in operation estimation', () => {
      it('should handle null operation gracefully', () => {
        const duration = estimateOperationDuration(null as any);
        expect(duration).toBe(1000);
      });

      it('should handle operation with missing properties', () => {
        const incompleteOperation = {
          id: 'test',
          type: CleanupOperationType.RESOURCE_CLEANUP
          // Missing other properties
        } as ICleanupOperation;

        expect(() => estimateOperationDuration(incompleteOperation)).not.toThrow();
      });

      it('should handle operation with undefined type', () => {
        const operation = {
          ...createMockOperation('test'),
          type: undefined as any
        };

        const duration = estimateOperationDuration(operation);
        expect(duration).toBe(1000); // Should use default base time
      });
    });

    describe('Complex dependency scenarios', () => {
      it('should handle self-referencing dependencies', () => {
        const operations = [
          createMockOperation('op1', CleanupPriority.NORMAL, CleanupOperationType.RESOURCE_CLEANUP, ['op1']) // Self-reference
        ];

        expect(() => sortOperationsByDependencies(operations)).not.toThrow();
        const sorted = sortOperationsByDependencies(operations);
        expect(sorted).toHaveLength(1);
      });

      it('should handle very deep dependency chains', () => {
        const operations = Array.from({ length: 10 }, (_, i) =>
          createMockOperation(
            `op${i}`,
            CleanupPriority.NORMAL,
            CleanupOperationType.RESOURCE_CLEANUP,
            i > 0 ? [`op${i-1}`] : []
          )
        );

        const sorted = sortOperationsByDependencies(operations);
        expect(sorted).toHaveLength(10);

        // Verify correct order
        for (let i = 0; i < 9; i++) {
          const currentIndex = sorted.findIndex(op => op.id === `op${i}`);
          const nextIndex = sorted.findIndex(op => op.id === `op${i+1}`);
          expect(currentIndex).toBeLessThan(nextIndex);
        }
      });
    });
  });
});
