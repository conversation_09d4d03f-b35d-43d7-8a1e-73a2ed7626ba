/**
 * TemplateValidation Final Coverage Test
 * Target: 100% coverage for remaining uncovered lines: 210-296,324,436,553,871,891
 * Pattern-based approach using proven templates
 */

import { jest } from '@jest/globals';
import { CleanupOperationType, CleanupPriority } from '../../../CleanupCoordinatorEnhanced';
import { ICleanupTemplate } from '../../../types/CleanupTypes';

// Test utilities
function createTestTemplate(overrides: any = {}): ICleanupTemplate {
  return {
    id: 'test-template',
    name: 'Test Template',
    description: 'Test template for coverage',
    version: '1.0.0',
    operations: [],
    rollbackSteps: [],
    conditions: [],
    metadata: {},
    tags: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    author: 'test-author',
    ...overrides
  };
}

describe('TemplateValidation Final Coverage - Pattern-Based Implementation', () => {
  afterEach(() => {
    jest.resetModules();
    jest.resetAllMocks();
  });

  // PATTERN 1: Constructor Failure (Lines 210-234)
  describe('Constructor Failure Coverage (Lines 210-234)', () => {
    it('should hit Lines 210-234: _initializeResilientTimingSync catch block', async () => {
      // Step 1: Use spy approach to avoid import chain issues
      const { TemplateValidator } = await import('../TemplateValidation');

      // Step 2: Import the timing modules
      const ResilientTimingModule = await import('../../../utils/ResilientTiming');
      const ResilientMetricsModule = await import('../../../utils/ResilientMetrics');

      // Step 3: Spy on constructors to fail
      const timerSpy = jest.spyOn(ResilientTimingModule.ResilientTimer.prototype, 'constructor' as any)
        .mockImplementation(() => {
          throw new Error('Forced constructor failure for lines 210-234');
        });

      const metricsSpy = jest.spyOn(ResilientMetricsModule.ResilientMetricsCollector.prototype, 'constructor' as any)
        .mockImplementation(() => {
          throw new Error('Forced constructor failure for lines 210-234');
        });

      // Step 4: Create instance - triggers catch block (lines 210-234)
      const validator = new TemplateValidator();
      expect(validator).toBeDefined();

      // Step 5: Test fallback behavior works
      await validator.initialize();
      const template = createTestTemplate({
        operations: [{
          id: 'test-op',
          type: CleanupOperationType.RESOURCE_CLEANUP,
          componentPattern: 'test-.*',
          operationName: 'test-operation',
          parameters: {},
          timeout: 5000,
          retryPolicy: { maxRetries: 3, retryDelay: 1000, backoffMultiplier: 2, maxRetryDelay: 10000, retryOnErrors: [] },
          dependsOn: [],
          priority: CleanupPriority.NORMAL,
          estimatedDuration: 1000,
          description: 'Test operation'
        }]
      });
      const result = await validator.validateTemplate(template);
      expect(result.performanceMetrics.validationTime).toBeGreaterThanOrEqual(0);

      // Step 6: Cleanup
      await validator.shutdown();
      timerSpy.mockRestore();
      metricsSpy.mockRestore();
    });
  });

  // PATTERN 2: Reconfiguration Failure (Lines 294-296)
  describe('Reconfiguration Failure Coverage (Lines 294-296)', () => {
    it('should hit Lines 294-296: doInitialize reconfiguration catch block', async () => {
      let timerCallCount = 0;
      let metricsCallCount = 0;

      // Step 1: Mock with call-count logic for reconfiguration failure
      jest.doMock('../../../utils/ResilientTiming', () => ({
        ResilientTimer: jest.fn().mockImplementation(function(_config: any) {
          timerCallCount++;
          if (timerCallCount === 1) {
            // First call (constructor) succeeds
            return {
              start: () => ({
                end: () => ({
                  duration: 100,
                  reliable: true,
                  startTime: Date.now(),
                  endTime: Date.now()
                })
              })
            };
          } else {
            // Second call (doInitialize reconfiguration) fails - triggers lines 294-296
            throw new Error('Reconfiguration failure for lines 294-296');
          }
        })
      }));

      jest.doMock('../../../utils/ResilientMetrics', () => ({
        ResilientMetricsCollector: jest.fn().mockImplementation(function(_config: any) {
          metricsCallCount++;
          if (metricsCallCount === 1) {
            // First call succeeds
            return {
              recordTiming: jest.fn(),
              reset: jest.fn(),
              createSnapshot: () => ({ metrics: new Map(), reliable: true, warnings: [] })
            };
          } else {
            // Second call fails - triggers lines 294-296
            throw new Error('Reconfiguration failure for lines 294-296');
          }
        })
      }));

      // Step 2: Clean module state and import
      jest.resetModules();
      const { TemplateValidator } = await import('../TemplateValidation');

      // Step 3: Create instance and trigger reconfiguration
      const validator = new TemplateValidator();
      expect(validator).toBeDefined();
      
      // Step 4: Trigger doInitialize - this should cause reconfiguration and hit lines 294-296
      await validator.initialize();
      
      // Step 5: Test functionality with fallback infrastructure
      const template = createTestTemplate({
        operations: [{
          id: 'reconfig-test',
          type: CleanupOperationType.RESOURCE_CLEANUP,
          componentPattern: 'reconfig-.*',
          operationName: 'reconfiguration-operation',
          parameters: {},
          timeout: 5000,
          retryPolicy: { maxRetries: 3, retryDelay: 1000, backoffMultiplier: 2, maxRetryDelay: 10000, retryOnErrors: [] },
          dependsOn: [],
          priority: CleanupPriority.NORMAL,
          estimatedDuration: 1000,
          description: 'Reconfiguration test'
        }]
      });
      const result = await validator.validateTemplate(template);
      
      // Step 6: Verify fallback produces valid results
      expect(result).toBeDefined();
      expect(result.performanceMetrics.validationTime).toBeGreaterThanOrEqual(0);
      expect(timerCallCount).toBeGreaterThanOrEqual(2); // Verify reconfiguration was attempted

      // Step 7: Cleanup
      await validator.shutdown();
      jest.dontMock('../../../utils/ResilientTiming');
      jest.dontMock('../../../utils/ResilientMetrics');
      jest.resetModules();
    });
  });

  // PATTERN 3: Shutdown Error (Line 324)
  describe('Shutdown Error Coverage (Line 324)', () => {
    it('should hit Line 324: doShutdown timing infrastructure error', async () => {
      // Step 1: Create validator with working infrastructure
      const { TemplateValidator } = await import('../TemplateValidation');
      const validator = new TemplateValidator();
      await validator.initialize();

      // Step 2: Replace the actual metrics collector after initialization
      const shutdownMethod = jest.fn().mockImplementation(() => {
        throw new Error('Timing infrastructure shutdown failure for line 324');
      });

      // Try multiple possible method names that might be called during shutdown
      const mockMetricsCollector = {
        recordTiming: jest.fn(),
        reset: shutdownMethod,
        shutdown: shutdownMethod,
        cleanup: shutdownMethod,
        destroy: shutdownMethod,
        stop: shutdownMethod,
        createSnapshot: jest.fn().mockReturnValue({
          metrics: new Map(),
          reliable: true,
          warnings: []
        })
      };

      // Direct replacement to ensure our mock is used
      (validator as any)._metricsCollector = mockMetricsCollector;

      // Step 3: Trigger shutdown and catch any errors
      try {
        await validator.shutdown();
      } catch (error) {
        // Expected if shutdown throws
      }

      // Step 4: Verify at least one shutdown method was called
      const wasCalled = shutdownMethod.mock.calls.length > 0;
      expect(wasCalled).toBe(true);
    });
  });

  // PATTERN 4: Validation Error (Line 436)
  describe('Validation Error Coverage (Line 436)', () => {
    it('should hit Line 436: validateTemplate error enhancement', async () => {
      // Step 1: Import validator
      const { TemplateValidator } = await import('../TemplateValidation');
      const validator = new TemplateValidator();
      await validator.initialize();

      // Step 2: Create template that will cause validation error
      const invalidTemplate = createTestTemplate({
        operations: [{
          id: 'invalid-op',
          type: 'INVALID_TYPE' as any, // Invalid operation type
          componentPattern: '[invalid-regex', // Invalid regex pattern
          operationName: 'invalid-operation',
          parameters: {},
          timeout: -1, // Invalid timeout
          retryPolicy: { maxRetries: -1, retryDelay: -1, backoffMultiplier: -1, maxRetryDelay: -1, retryOnErrors: [] },
          dependsOn: ['non-existent-operation'], // Invalid dependency
          priority: 'INVALID_PRIORITY' as any,
          estimatedDuration: -1,
          description: 'Invalid operation'
        }]
      });

      // Step 3: Trigger validation - should hit line 436 in catch block
      const result = await validator.validateTemplate(invalidTemplate);

      // Step 4: Verify error was handled and enhanced (line 436 executed)
      expect(result).toBeDefined();
      expect(result.issues.length).toBeGreaterThan(0);

      // Step 5: Cleanup
      await validator.shutdown();
    });
  });

  // PATTERN 5: Dependency Error (Line 553)
  describe('Dependency Error Coverage (Line 553)', () => {
    it('should hit Line 553: _validateDependencies catch block', async () => {
      // Step 1: Mock DependencyGraph BEFORE importing TemplateValidation
      jest.doMock('../TemplateDependencies', () => {
        return {
          DependencyGraph: jest.fn().mockImplementation(function() {
            return {
              addNode: jest.fn(),
              addDependency: jest.fn().mockImplementation(() => {
                // This should trigger the catch block on line 553
                throw new Error('Forced dependency validation failure for line 553');
              }),
              validateDependencies: jest.fn(),
              getExecutionOrder: jest.fn().mockReturnValue([]),
              hasCycles: jest.fn().mockReturnValue(false),
              nodes: new Set(),
              edges: new Map()
            };
          }),
          createDependencyGraphFromOperations: jest.fn()
        };
      });

      // Step 2: Reset modules and import fresh
      jest.resetModules();
      const { TemplateValidator } = await import('../TemplateValidation');

      // Step 3: Create validator
      const validator = new TemplateValidator();
      await validator.initialize();

      // Step 4: Create template with dependencies to trigger validation
      const templateWithDeps = createTestTemplate({
        operations: [{
          id: 'dep-op-1',
          type: CleanupOperationType.RESOURCE_CLEANUP,
          componentPattern: 'dep-.*',
          operationName: 'dependency-operation-1',
          parameters: {},
          timeout: 5000,
          retryPolicy: { maxRetries: 3, retryDelay: 1000, backoffMultiplier: 2, maxRetryDelay: 10000, retryOnErrors: [] },
          dependsOn: ['dep-op-2'], // This triggers dependency validation
          priority: CleanupPriority.NORMAL,
          estimatedDuration: 1000,
          description: 'Dependency operation 1'
        }, {
          id: 'dep-op-2',
          type: CleanupOperationType.RESOURCE_CLEANUP,
          componentPattern: 'dep-.*',
          operationName: 'dependency-operation-2',
          parameters: {},
          timeout: 5000,
          retryPolicy: { maxRetries: 3, retryDelay: 1000, backoffMultiplier: 2, maxRetryDelay: 10000, retryOnErrors: [] },
          dependsOn: [],
          priority: CleanupPriority.NORMAL,
          estimatedDuration: 1000,
          description: 'Dependency operation 2'
        }]
      });

      // Step 5: Trigger validation
      const result = await validator.validateTemplate(templateWithDeps);

      // Step 6: Check for various possible error types (be more flexible)
      expect(result).toBeDefined();
      const hasValidationError = result.issues.some(issue =>
        issue.type.includes('validation') ||
        issue.type.includes('dependency') ||
        issue.type.includes('error')
      );
      expect(hasValidationError).toBe(true);

      // Alternative: Check that some error was recorded
      expect(result.issues.length).toBeGreaterThan(0);

      // Step 7: Cleanup
      await validator.shutdown();
      jest.dontMock('../TemplateDependencies');
      jest.resetModules();
    });
  });

  // PATTERN 6: Runtime Conditions (Lines 871, 891)
  describe('Runtime Condition Coverage (Lines 871, 891)', () => {
    it('should hit Line 871: quality score calculation with missing operation IDs', async () => {
      // Step 1: Import validator
      const { TemplateValidator } = await import('../TemplateValidation');
      const validator = new TemplateValidator();
      await validator.initialize();

      // Step 2: Create template with operations missing IDs (triggers line 871)
      const templateWithMissingIds = createTestTemplate({
        operations: [
          {
            id: '', // Empty ID - triggers line 871
            type: CleanupOperationType.RESOURCE_CLEANUP,
            componentPattern: 'missing-.*',
            operationName: 'missing-id-operation-1',
            parameters: {},
            timeout: 5000,
            retryPolicy: { maxRetries: 3, retryDelay: 1000, backoffMultiplier: 2, maxRetryDelay: 10000, retryOnErrors: [] },
            dependsOn: [],
            priority: CleanupPriority.NORMAL,
            estimatedDuration: 1000,
            description: 'Operation with missing ID'
          },
          {
            id: '   ', // Whitespace-only ID - triggers line 871
            type: CleanupOperationType.RESOURCE_CLEANUP,
            componentPattern: 'missing-.*',
            operationName: 'missing-id-operation-2',
            parameters: {},
            timeout: 5000,
            retryPolicy: { maxRetries: 3, retryDelay: 1000, backoffMultiplier: 2, maxRetryDelay: 10000, retryOnErrors: [] },
            dependsOn: [],
            priority: CleanupPriority.NORMAL,
            estimatedDuration: 1000,
            description: 'Operation with whitespace-only ID'
          }
        ]
      });

      // Step 3: Trigger validation - should hit line 871
      const result = await validator.validateTemplate(templateWithMissingIds);

      // Step 4: Verify quality score calculation handled missing IDs (line 871 executed)
      expect(result).toBeDefined();
      expect(result.qualityScore).toBeLessThan(100); // Score should be penalized

      // Step 5: Cleanup
      await validator.shutdown();
    });

    it('should hit Line 891: error context enhancement', async () => {
      // Step 1: Import validator
      const { TemplateValidator } = await import('../TemplateValidation');
      const validator = new TemplateValidator();
      await validator.initialize();

      // Step 2: Create template with operations that will cause validation error (line 891)
      const errorTemplate = createTestTemplate({
        operations: [{
          id: 'error-op',
          type: CleanupOperationType.RESOURCE_CLEANUP,
          componentPattern: '[invalid-regex', // Invalid regex will cause error
          operationName: 'error-operation',
          parameters: {},
          timeout: 5000,
          retryPolicy: { maxRetries: 3, retryDelay: 1000, backoffMultiplier: 2, maxRetryDelay: 10000, retryOnErrors: [] },
          dependsOn: [],
          priority: CleanupPriority.NORMAL,
          estimatedDuration: 1000,
          description: 'Error operation'
        }]
      });

      // Step 3: Trigger validation - should hit line 891 for error enhancement
      const result = await validator.validateTemplate(errorTemplate);

      // Step 4: Verify error context enhancement was applied (line 891 executed)
      expect(result).toBeDefined();
      // The error should be handled and result should still be valid
      expect(result.performanceMetrics).toBeDefined();

      // Step 5: Cleanup
      await validator.shutdown();
    });
  });
});
