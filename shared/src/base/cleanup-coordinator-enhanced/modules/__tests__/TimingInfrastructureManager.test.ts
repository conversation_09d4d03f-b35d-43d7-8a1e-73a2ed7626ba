/**
 * @file TimingInfrastructureManager Test Suite
 * @filepath shared/src/base/modules/cleanup/__tests__/TimingInfrastructureManager.test.ts
 * @description Comprehensive test suite for TimingInfrastructureManager
 */

import { TimingInfrastructureManager, TimingMetrics, TimingReliabilityMetrics } from '../TimingInfrastructureManager';
import { IEnhancedCleanupConfig } from '../../../types/CleanupTypes';
import { ILoggingService } from '../../../LoggingMixin';

// Mock dependencies
jest.mock('../../../utils/ResilientTiming', () => ({
  ResilientTimer: jest.fn().mockImplementation(() => ({
    start: jest.fn().mockReturnValue({
      start: jest.fn(),
      end: jest.fn().mockReturnValue({
        duration: 100,
        reliable: true,
        fallbackUsed: false,
        timestamp: Date.now()
      })
    })
  }))
}));

jest.mock('../../../utils/ResilientMetrics', () => ({
  ResilientMetricsCollector: jest.fn().mockImplementation(() => ({
    createSnapshot: jest.fn().mockReturnValue({
      metrics: new Map([
        ['executeOperation', { value: 50 }],
        ['processQueue', { value: 30 }]
      ])
    }),
    recordTiming: jest.fn(),
    reset: jest.fn()
  }))
}));

describe('TimingInfrastructureManager', () => {
  let timingManager: TimingInfrastructureManager;
  let mockLogger: jest.Mocked<ILoggingService>;
  let enhancedConfig: Required<IEnhancedCleanupConfig>;

  beforeEach(() => {
    // Setup mock logger
    mockLogger = {
      logInfo: jest.fn(),
      logError: jest.fn(),
      logWarning: jest.fn(),
      logDebug: jest.fn()
    };

    // Setup configuration
    enhancedConfig = {
      maxConcurrentOperations: 10,
      defaultTimeout: 30000,
      maxRetries: 3,
      conflictDetectionEnabled: true,
      metricsEnabled: true,
      cleanupIntervalMs: 300000,
      testMode: false,
      templateValidationEnabled: true,
      dependencyOptimizationEnabled: true,
      rollbackEnabled: true,
      maxCheckpoints: 100,
      checkpointRetentionDays: 7,
      phaseIntegrationEnabled: true,
      performanceMonitoringEnabled: true
    };

    // Create TimingInfrastructureManager instance
    timingManager = new TimingInfrastructureManager(mockLogger);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('initialize', () => {
    it('should initialize timing infrastructure successfully', async () => {
      await timingManager.initialize(enhancedConfig);

      expect(mockLogger.logInfo).toHaveBeenCalledWith(
        'Resilient timing infrastructure initialized successfully',
        {
          timerFallbacksEnabled: true,
          metricsCollectionEnabled: true,
          performanceTarget: 'enterprise'
        }
      );

      expect(timingManager.isInitialized()).toBe(true);
    });

    it('should handle initialization errors', async () => {
      // Create a new manager instance to test initialization failure
      const failingManager = new TimingInfrastructureManager(mockLogger);

      // Mock the ResilientTimer constructor to throw an error
      const mockError = new Error('Timer initialization failed');
      const originalResilientTimer = require('../../../utils/ResilientTiming').ResilientTimer;

      // Temporarily replace the constructor
      const mockConstructor = jest.fn().mockImplementation(() => {
        throw mockError;
      });
      require('../../../utils/ResilientTiming').ResilientTimer = mockConstructor;

      try {
        await expect(failingManager.initialize(enhancedConfig)).rejects.toThrow(mockError);
        expect(mockLogger.logError).toHaveBeenCalledWith(
          'Failed to initialize timing infrastructure',
          mockError
        );
      } finally {
        // Restore the original constructor
        require('../../../utils/ResilientTiming').ResilientTimer = originalResilientTimer;
      }
    });
  });

  describe('createTimingContext', () => {
    it('should create timing context when initialized', async () => {
      await timingManager.initialize(enhancedConfig);

      const context = timingManager.createTimingContext();
      expect(context).toBeDefined();
      expect(context).toHaveProperty('start');
      expect(context).toHaveProperty('end');
    });

    it('should throw error when not initialized', () => {
      expect(() => timingManager.createTimingContext()).toThrow(
        'TimingInfrastructureManager not initialized'
      );
    });
  });

  describe('recordTiming', () => {
    it('should record timing when initialized', async () => {
      await timingManager.initialize(enhancedConfig);
      
      const timingResult = {
        duration: 100,
        reliable: true,
        fallbackUsed: false,
        timestamp: Date.now(),
        method: 'performance' as const
      };

      timingManager.recordTiming('test-operation', timingResult);
      // Should not throw and should log nothing for successful recording
    });

    it('should log warning when not initialized', () => {
      const timingResult = {
        duration: 100,
        reliable: true,
        fallbackUsed: false,
        timestamp: Date.now(),
        method: 'performance' as const
      };

      timingManager.recordTiming('test-operation', timingResult);
      
      expect(mockLogger.logWarning).toHaveBeenCalledWith(
        'Attempted to record timing before initialization',
        { operation: 'test-operation' }
      );
    });
  });

  describe('getTimingMetrics', () => {
    it('should return timing metrics when initialized', async () => {
      await timingManager.initialize(enhancedConfig);
      
      const metrics = await timingManager.getTimingMetrics();
      
      expect(metrics).toEqual({
        operationCount: 2, // Two operations in mock
        totalDuration: 80, // 50 + 30 from mock data
        averageDuration: 40, // 80 / 2
        coordinationOverhead: 30 // processQueue value from mock
      });
    });

    it('should return default metrics when not initialized', async () => {
      const metrics = await timingManager.getTimingMetrics();
      
      expect(metrics).toEqual({
        operationCount: 0,
        totalDuration: 0,
        averageDuration: 0,
        coordinationOverhead: 0
      });
    });

    it('should calculate metrics with recorded operations', async () => {
      await timingManager.initialize(enhancedConfig);
      
      // Mock metrics collector to return sample data
      const mockSnapshot = {
        metrics: new Map([
          ['executeOperation_test', { value: 50, timestamp: Date.now(), reliable: true, source: 'measured' }],
          ['scheduleCleanup', { value: 10, timestamp: Date.now(), reliable: true, source: 'measured' }]
        ]),
        timestamp: Date.now(),
        reliable: true,
        warnings: []
      };

      // Access private _metricsCollector and mock its createSnapshot method
      (timingManager as any)._metricsCollector = {
        createSnapshot: jest.fn().mockReturnValue(mockSnapshot)
      };

      const metrics = await timingManager.getTimingMetrics();
      
      expect(metrics.operationCount).toBe(2);
      expect(metrics.totalDuration).toBeGreaterThan(0);
      expect(metrics.coordinationOverhead).toBeGreaterThan(0);
    });
  });

  describe('getTimingReliabilityMetrics', () => {
    it('should return reliability metrics when initialized', async () => {
      await timingManager.initialize(enhancedConfig);
      
      const metrics = await timingManager.getTimingReliabilityMetrics();
      
      expect(metrics).toEqual({
        fallbacksUsed: 0,
        reliabilityScore: 0.95,
        unreliableOperations: 0
      });
    });

    it('should return default metrics when not initialized', async () => {
      const metrics = await timingManager.getTimingReliabilityMetrics();
      
      expect(metrics).toEqual({
        fallbacksUsed: 0,
        reliabilityScore: 0.90,
        unreliableOperations: 0
      });
    });

    it('should handle errors gracefully', async () => {
      await timingManager.initialize(enhancedConfig);
      
      // Mock metrics collector to throw error
      (timingManager as any)._metricsCollector = {
        createSnapshot: jest.fn().mockImplementation(() => {
          throw new Error('Metrics collection failed');
        })
      };

      const metrics = await timingManager.getTimingReliabilityMetrics();
      
      expect(metrics).toEqual({
        fallbacksUsed: 0,
        reliabilityScore: 0.90,
        unreliableOperations: 0
      });

      expect(mockLogger.logWarning).toHaveBeenCalledWith(
        'Timing reliability metrics collection failed, using defaults',
        { error: 'Metrics collection failed' }
      );
    });
  });

  describe('clearTimingMetrics', () => {
    it('should clear metrics when initialized', async () => {
      await timingManager.initialize(enhancedConfig);
      
      timingManager.clearTimingMetrics();
      // Should not throw
    });

    it('should handle clear when not initialized', () => {
      timingManager.clearTimingMetrics();
      // Should not throw
    });
  });

  describe('shutdown', () => {
    it('should shutdown timing infrastructure when initialized', async () => {
      await timingManager.initialize(enhancedConfig);
      
      // Mock metrics collector
      const mockSnapshot = {
        metrics: new Map([['test', { value: 1, timestamp: Date.now(), reliable: true, source: 'measured' }]]),
        timestamp: Date.now(),
        reliable: true,
        warnings: []
      };

      (timingManager as any)._metricsCollector = {
        createSnapshot: jest.fn().mockReturnValue(mockSnapshot),
        reset: jest.fn()
      };

      timingManager.shutdown();

      expect(mockLogger.logInfo).toHaveBeenCalledWith(
        'Final resilient metrics snapshot',
        {
          totalMetrics: 1,
          reliable: true,
          warnings: 0
        }
      );

      expect(mockLogger.logInfo).toHaveBeenCalledWith(
        'Resilient timing infrastructure shutdown completed successfully'
      );

      expect(timingManager.isInitialized()).toBe(false);
    });

    it('should handle shutdown when not initialized', () => {
      timingManager.shutdown();
      // Should not throw
    });

    it('should handle shutdown errors', async () => {
      await timingManager.initialize(enhancedConfig);
      
      // Mock metrics collector to throw error
      (timingManager as any)._metricsCollector = {
        createSnapshot: jest.fn().mockImplementation(() => {
          throw new Error('Shutdown error');
        })
      };

      timingManager.shutdown();

      expect(mockLogger.logError).toHaveBeenCalledWith(
        'Error during resilient timing infrastructure shutdown',
        expect.any(Error)
      );
    });
  });

  describe('getMetricsSnapshot', () => {
    it('should return metrics snapshot when initialized', async () => {
      await timingManager.initialize(enhancedConfig);
      
      const mockSnapshot = {
        metrics: new Map(),
        timestamp: Date.now(),
        reliable: true,
        warnings: []
      };

      (timingManager as any)._metricsCollector = {
        createSnapshot: jest.fn().mockReturnValue(mockSnapshot)
      };

      const snapshot = timingManager.getMetricsSnapshot();
      expect(snapshot).toBe(mockSnapshot);
    });

    it('should return null when not initialized', () => {
      const snapshot = timingManager.getMetricsSnapshot();
      expect(snapshot).toBeNull();
    });
  });

  // ============================================================================
  // 🎯 ADVANCED BRANCH COVERAGE RESOLUTION - TARGET: 95%+
  // ============================================================================
  // Following lesson-15-branch-coverage-resolution-mastery.md techniques
  // ============================================================================

  describe('🎯 Advanced Branch Coverage Resolution - Target: 95%+', () => {

    describe('🔬 Error Type Testing Patterns', () => {
      it('should handle Error instanceof - TRUE branch in initialize', async () => {
        // Test the TRUE branch of: error instanceof Error ? error : new Error(String(error))
        const realError = new TypeError('Real initialization error');

        // Mock ResilientTimer constructor to throw Error instance
        const originalResilientTimer = require('../../../utils/ResilientTiming').ResilientTimer;
        require('../../../utils/ResilientTiming').ResilientTimer = jest.fn().mockImplementation(() => {
          throw realError; // Error instance
        });

        try {
          await expect(timingManager.initialize(enhancedConfig)).rejects.toThrow(realError);

          // Verify the Error instance was passed directly (TRUE branch)
          expect(mockLogger.logError).toHaveBeenCalledWith(
            'Failed to initialize timing infrastructure',
            realError // Should be the original Error instance
          );
        } finally {
          require('../../../utils/ResilientTiming').ResilientTimer = originalResilientTimer;
        }
      });

      it('should handle Error instanceof - FALSE branch in initialize', async () => {
        // Test the FALSE branch of: error instanceof Error ? error : new Error(String(error))
        const nonErrorObject = { code: 'INIT_FAILED', message: 'Non-error object' };

        // Mock ResilientTimer constructor to throw non-Error object
        const originalResilientTimer = require('../../../utils/ResilientTiming').ResilientTimer;
        require('../../../utils/ResilientTiming').ResilientTimer = jest.fn().mockImplementation(() => {
          throw nonErrorObject; // Non-Error object
        });

        try {
          // The non-Error object will be re-thrown as-is, but logged as Error
          await expect(timingManager.initialize(enhancedConfig)).rejects.toBe(nonErrorObject);

          // Verify the non-Error was converted to Error for logging (FALSE branch)
          expect(mockLogger.logError).toHaveBeenCalledWith(
            'Failed to initialize timing infrastructure',
            expect.any(Error) // Should be converted to Error instance
          );

          // Verify the Error message contains the stringified non-Error
          const loggedError = mockLogger.logError.mock.calls[0][1] as Error;
          expect(loggedError.message).toContain('[object Object]');
        } finally {
          require('../../../utils/ResilientTiming').ResilientTimer = originalResilientTimer;
        }
      });

      it('should handle Error instanceof - TRUE branch in shutdown', async () => {
        await timingManager.initialize(enhancedConfig);

        // Mock metrics collector to throw Error instance
        const realError = new Error('Shutdown metrics error');
        (timingManager as any)._metricsCollector = {
          createSnapshot: jest.fn().mockImplementation(() => {
            throw realError; // Error instance
          })
        };

        timingManager.shutdown();

        // Verify the Error instance was passed directly (TRUE branch)
        expect(mockLogger.logError).toHaveBeenCalledWith(
          'Error during resilient timing infrastructure shutdown',
          realError // Should be the original Error instance
        );
      });

      it('should handle Error instanceof - FALSE branch in shutdown', async () => {
        await timingManager.initialize(enhancedConfig);

        // Mock metrics collector to throw non-Error object
        const nonErrorObject = 'string error in shutdown';
        (timingManager as any)._metricsCollector = {
          createSnapshot: jest.fn().mockImplementation(() => {
            throw nonErrorObject; // Non-Error object
          })
        };

        timingManager.shutdown();

        // Verify the non-Error was converted to Error (FALSE branch)
        expect(mockLogger.logError).toHaveBeenCalledWith(
          'Error during resilient timing infrastructure shutdown',
          expect.any(Error) // Should be converted to Error instance
        );

        // Verify the Error message contains the stringified non-Error
        const loggedError = mockLogger.logError.mock.calls[0][1] as Error;
        expect(loggedError.message).toBe('string error in shutdown');
      });

      it('should handle Error instanceof - TRUE branch in getTimingReliabilityMetrics', async () => {
        await timingManager.initialize(enhancedConfig);

        // Mock metrics collector to throw Error instance
        const realError = new Error('Reliability metrics error');
        (timingManager as any)._metricsCollector = {
          createSnapshot: jest.fn().mockImplementation(() => {
            throw realError; // Error instance
          })
        };

        const metrics = await timingManager.getTimingReliabilityMetrics();

        // Verify the Error instance message was used directly (TRUE branch)
        expect(mockLogger.logWarning).toHaveBeenCalledWith(
          'Timing reliability metrics collection failed, using defaults',
          { error: realError.message } // Should be the original Error message
        );

        expect(metrics).toEqual({
          fallbacksUsed: 0,
          reliabilityScore: 0.90,
          unreliableOperations: 0
        });
      });

      it('should handle Error instanceof - FALSE branch in getTimingReliabilityMetrics', async () => {
        await timingManager.initialize(enhancedConfig);

        // Mock metrics collector to throw non-Error object
        const nonErrorObject = { type: 'METRICS_FAILURE', details: 'Complex object error' };
        (timingManager as any)._metricsCollector = {
          createSnapshot: jest.fn().mockImplementation(() => {
            throw nonErrorObject; // Non-Error object
          })
        };

        const metrics = await timingManager.getTimingReliabilityMetrics();

        // Verify the non-Error was converted to string (FALSE branch)
        expect(mockLogger.logWarning).toHaveBeenCalledWith(
          'Timing reliability metrics collection failed, using defaults',
          { error: '[object Object]' } // Should be stringified non-Error
        );

        expect(metrics).toEqual({
          fallbacksUsed: 0,
          reliabilityScore: 0.90,
          unreliableOperations: 0
        });
      });
    });

    describe('🎯 Surgical Precision Testing - Target Uncovered Lines', () => {
      it('should cover Line 236: totalDuration fallback branch (TRUE && TRUE)', async () => {
        await timingManager.initialize(enhancedConfig);

        // Create scenario where totalDuration === 0 AND operationCount > 0
        const mockSnapshot = {
          metrics: new Map([
            ['non-matching-operation', { value: 50, timestamp: Date.now(), reliable: true, source: 'measured' }]
          ]),
          timestamp: Date.now(),
          reliable: true,
          warnings: []
        };

        (timingManager as any)._metricsCollector = {
          createSnapshot: jest.fn().mockReturnValue(mockSnapshot)
        };

        const metrics = await timingManager.getTimingMetrics();

        // Verify Line 236 was executed: totalDuration = operationCount * 0.1
        expect(metrics.operationCount).toBe(1);
        expect(metrics.totalDuration).toBe(0.1); // Should be 1 * 0.1 = 0.1
      });

      it('should cover Line 240: coordinationOverhead fallback branch (TRUE && TRUE)', async () => {
        await timingManager.initialize(enhancedConfig);

        // Create scenario where coordinationOverhead === 0 AND operationCount > 0
        const mockSnapshot = {
          metrics: new Map([
            ['executeOperation_test', { value: 50, timestamp: Date.now(), reliable: true, source: 'measured' }]
          ]),
          timestamp: Date.now(),
          reliable: true,
          warnings: []
        };

        (timingManager as any)._metricsCollector = {
          createSnapshot: jest.fn().mockReturnValue(mockSnapshot)
        };

        const metrics = await timingManager.getTimingMetrics();

        // Verify Line 240 was executed: coordinationOverhead = Math.max(0.1, operationCount * 0.05)
        expect(metrics.operationCount).toBe(1);
        expect(metrics.coordinationOverhead).toBe(0.1); // Should be Math.max(0.1, 1 * 0.05) = 0.1
      });

      it('should test FALSE branches for fallback conditions', async () => {
        await timingManager.initialize(enhancedConfig);

        // Create scenario where both conditions are FALSE
        const mockSnapshot = {
          metrics: new Map([
            ['scheduleCleanup_test', { value: 25, timestamp: Date.now(), reliable: true, source: 'measured' }],
            ['processQueue_test', { value: 15, timestamp: Date.now(), reliable: true, source: 'measured' }]
          ]),
          timestamp: Date.now(),
          reliable: true,
          warnings: []
        };

        (timingManager as any)._metricsCollector = {
          createSnapshot: jest.fn().mockReturnValue(mockSnapshot)
        };

        const metrics = await timingManager.getTimingMetrics();

        // Verify fallback branches are NOT executed (totalDuration > 0, coordinationOverhead > 0)
        expect(metrics.operationCount).toBe(2);
        expect(metrics.totalDuration).toBeGreaterThan(0.1); // Should be actual calculated value
        expect(metrics.coordinationOverhead).toBeGreaterThan(0.1); // Should be actual calculated value
      });
    });

    describe('🎯 Systematic Branch Combination Testing', () => {
      it('should test all combinations of logical operators in getTimingReliabilityMetrics', async () => {
        await timingManager.initialize(enhancedConfig);

        // Test: snapshot && snapshot.metrics ? snapshot.metrics.size : 1
        const testCombinations = [
          {
            desc: 'snapshot=null, metrics=null -> FALSE && FALSE = FALSE',
            snapshot: null,
            expectedSize: 1
          },
          {
            desc: 'snapshot=valid, metrics=null -> TRUE && FALSE = FALSE',
            snapshot: { metrics: null, timestamp: Date.now(), reliable: true, warnings: [] },
            expectedSize: 1
          },
          {
            desc: 'snapshot=valid, metrics=empty -> TRUE && TRUE = TRUE',
            snapshot: {
              metrics: new Map(),
              timestamp: Date.now(),
              reliable: true,
              warnings: []
            },
            expectedSize: 0
          },
          {
            desc: 'snapshot=valid, metrics=populated -> TRUE && TRUE = TRUE',
            snapshot: {
              metrics: new Map([['test', { value: 1 }]]),
              timestamp: Date.now(),
              reliable: true,
              warnings: []
            },
            expectedSize: 1
          }
        ];

        for (const { desc, snapshot, expectedSize } of testCombinations) {
          // Mock the metrics collector for each scenario
          (timingManager as any)._metricsCollector = {
            createSnapshot: jest.fn().mockReturnValue(snapshot)
          };

          const metrics = await timingManager.getTimingReliabilityMetrics();

          // Verify the logical operator evaluation worked correctly
          expect(metrics).toBeDefined();
          expect(typeof metrics.reliabilityScore).toBe('number');

          // The operationCount calculation should reflect the logical operator result
          const expectedOperationCount = Math.max(1, expectedSize);
          expect(metrics.fallbacksUsed).toBe(Math.max(0, Math.floor(expectedOperationCount * 0.02)));
        }
      });

      it('should test logical operators in getTimingMetrics snapshot check', async () => {
        await timingManager.initialize(enhancedConfig);

        // Test: if (snapshot && snapshot.metrics)
        const testCombinations = [
          {
            desc: 'snapshot=null -> FALSE && ? = FALSE',
            snapshot: null,
            shouldEnterLoop: false
          },
          {
            desc: 'snapshot=valid, metrics=null -> TRUE && FALSE = FALSE',
            snapshot: { metrics: null, timestamp: Date.now(), reliable: true, warnings: [] },
            shouldEnterLoop: false
          },
          {
            desc: 'snapshot=valid, metrics=empty -> TRUE && TRUE = TRUE',
            snapshot: {
              metrics: new Map(),
              timestamp: Date.now(),
              reliable: true,
              warnings: []
            },
            shouldEnterLoop: true
          },
          {
            desc: 'snapshot=valid, metrics=populated -> TRUE && TRUE = TRUE',
            snapshot: {
              metrics: new Map([
                ['executeOperation_test', { value: 10 }],
                ['scheduleCleanup_test', { value: 5 }]
              ]),
              timestamp: Date.now(),
              reliable: true,
              warnings: []
            },
            shouldEnterLoop: true
          }
        ];

        for (const { desc, snapshot, shouldEnterLoop } of testCombinations) {
          // Mock the metrics collector for each scenario
          (timingManager as any)._metricsCollector = {
            createSnapshot: jest.fn().mockReturnValue(snapshot)
          };

          const metrics = await timingManager.getTimingMetrics();

          if (shouldEnterLoop) {
            // Should have processed the metrics
            expect(metrics.operationCount).toBeGreaterThanOrEqual(0);
          } else {
            // Should have skipped the metrics processing loop
            expect(metrics.operationCount).toBe(0);
          }
        }
      });
    });

    describe('🎯 Advanced Conditional Branch Testing', () => {
      it('should test OR operator branches in metric name filtering', async () => {
        await timingManager.initialize(enhancedConfig);

        // Test all combinations of OR conditions in metric name filtering
        const testMetrics = [
          // Test executeOperation OR processQueue OR scheduleCleanup (Line 225)
          { name: 'executeOperation_test', value: 10, shouldMatch: true },
          { name: 'processQueue_test', value: 20, shouldMatch: true },
          { name: 'scheduleCleanup_test', value: 30, shouldMatch: true },
          { name: 'other_operation', value: 40, shouldMatch: false },

          // Test scheduleCleanup OR processQueue (Line 228)
          { name: 'scheduleCleanup_coordination', value: 5, shouldMatchCoordination: true },
          { name: 'processQueue_coordination', value: 15, shouldMatchCoordination: true },
          { name: 'executeOperation_coordination', value: 25, shouldMatchCoordination: false }
        ];

        for (const metric of testMetrics) {
          const mockSnapshot = {
            metrics: new Map([[metric.name, { value: metric.value, timestamp: Date.now(), reliable: true, source: 'measured' }]]),
            timestamp: Date.now(),
            reliable: true,
            warnings: []
          };

          (timingManager as any)._metricsCollector = {
            createSnapshot: jest.fn().mockReturnValue(mockSnapshot)
          };

          const metrics = await timingManager.getTimingMetrics();

          if (metric.shouldMatch) {
            // Should be included in totalDuration
            expect(metrics.totalDuration).toBeGreaterThan(0.1);
          }

          if (metric.shouldMatchCoordination) {
            // Should be included in coordinationOverhead
            expect(metrics.coordinationOverhead).toBeGreaterThan(0.1);
          }
        }
      });

      it('should test ternary operator in averageDuration calculation', async () => {
        await timingManager.initialize(enhancedConfig);

        // Test: operationCount > 0 ? totalDuration / operationCount : 0
        const testScenarios = [
          {
            desc: 'operationCount = 0 -> FALSE branch',
            metrics: new Map(),
            expectedAverage: 0
          },
          {
            desc: 'operationCount > 0 -> TRUE branch',
            metrics: new Map([
              ['executeOperation_test', { value: 100, timestamp: Date.now(), reliable: true, source: 'measured' }],
              ['processQueue_test', { value: 50, timestamp: Date.now(), reliable: true, source: 'measured' }]
            ]),
            expectedOperationCount: 2,
            expectedTotalDuration: 150
          }
        ];

        for (const scenario of testScenarios) {
          const mockSnapshot = {
            metrics: scenario.metrics,
            timestamp: Date.now(),
            reliable: true,
            warnings: []
          };

          (timingManager as any)._metricsCollector = {
            createSnapshot: jest.fn().mockReturnValue(mockSnapshot)
          };

          const metrics = await timingManager.getTimingMetrics();

          if (scenario.expectedAverage !== undefined) {
            // Test FALSE branch: operationCount = 0
            expect(metrics.averageDuration).toBe(0.1); // Math.max ensures minimum 0.1
          } else {
            // Test TRUE branch: operationCount > 0
            expect(metrics.operationCount).toBe(scenario.expectedOperationCount);
            const expectedAverage = scenario.expectedTotalDuration! / scenario.expectedOperationCount!;
            expect(metrics.averageDuration).toBeCloseTo(Math.max(expectedAverage, 0.1), 1);
          }
        }
      });

      it('should test Math.max branches in return values', async () => {
        await timingManager.initialize(enhancedConfig);

        // Test Math.max ensuring non-zero values
        const mockSnapshot = {
          metrics: new Map([
            ['tiny_operation', { value: 0.001, timestamp: Date.now(), reliable: true, source: 'measured' }]
          ]),
          timestamp: Date.now(),
          reliable: true,
          warnings: []
        };

        (timingManager as any)._metricsCollector = {
          createSnapshot: jest.fn().mockReturnValue(mockSnapshot)
        };

        const metrics = await timingManager.getTimingMetrics();

        // All values should be at least 0.1 due to Math.max
        expect(metrics.totalDuration).toBeGreaterThanOrEqual(0.1);
        expect(metrics.averageDuration).toBeGreaterThanOrEqual(0.1);
        expect(metrics.coordinationOverhead).toBeGreaterThanOrEqual(0.1);
      });
    });

    describe('🎯 Edge Case and Error Boundary Testing', () => {
      it('should handle null/undefined metrics gracefully', async () => {
        await timingManager.initialize(enhancedConfig);

        // Test edge cases with null/undefined values
        const edgeCases = [
          { snapshot: null, desc: 'null snapshot' },
          { snapshot: undefined, desc: 'undefined snapshot' },
          { snapshot: { metrics: null }, desc: 'null metrics' },
          { snapshot: { metrics: undefined }, desc: 'undefined metrics' },
          { snapshot: { metrics: new Map() }, desc: 'empty metrics' }
        ];

        for (const edgeCase of edgeCases) {
          (timingManager as any)._metricsCollector = {
            createSnapshot: jest.fn().mockReturnValue(edgeCase.snapshot)
          };

          const metrics = await timingManager.getTimingMetrics();

          // Should return valid metrics structure without throwing
          expect(metrics).toBeDefined();
          expect(typeof metrics.operationCount).toBe('number');
          expect(typeof metrics.totalDuration).toBe('number');
          expect(typeof metrics.averageDuration).toBe('number');
          expect(typeof metrics.coordinationOverhead).toBe('number');
        }
      });

      it('should test _metricsCollector null check in shutdown', async () => {
        await timingManager.initialize(enhancedConfig);

        // Test the if (this._metricsCollector) branch
        // First test with valid metrics collector (TRUE branch)
        expect(() => timingManager.shutdown()).not.toThrow();
        expect(timingManager.isInitialized()).toBe(false);

        // Reset for next test
        await timingManager.initialize(enhancedConfig);

        // Test with null metrics collector (FALSE branch)
        (timingManager as any)._metricsCollector = null;

        // The key test: should handle null metrics collector without throwing
        expect(() => timingManager.shutdown()).not.toThrow();
        expect(timingManager.isInitialized()).toBe(false);

        // This test ensures both branches of if (this._metricsCollector) are covered
        // TRUE branch: normal shutdown with metrics collector
        // FALSE branch: shutdown with null metrics collector
      });

      it('should test complex error scenarios in shutdown', async () => {
        await timingManager.initialize(enhancedConfig);

        // Test various error types in shutdown
        const errorTypes = [
          new Error('Standard error'),
          new TypeError('Type error'),
          'String error',
          42,
          null,
          undefined,
          { custom: 'error object' }
        ];

        for (const errorType of errorTypes) {
          // Reset for each test
          await timingManager.initialize(enhancedConfig);

          (timingManager as any)._metricsCollector = {
            createSnapshot: jest.fn().mockImplementation(() => {
              throw errorType;
            })
          };

          // Should handle all error types gracefully
          expect(() => timingManager.shutdown()).not.toThrow();
          expect(mockLogger.logError).toHaveBeenCalled();
        }
      });
    });

    describe('🎯 Performance and Stress Testing', () => {
      it('should handle large metric collections efficiently', async () => {
        await timingManager.initialize(enhancedConfig);

        // Create large metrics collection to test performance
        const largeMetrics = new Map();
        for (let i = 0; i < 1000; i++) {
          largeMetrics.set(`executeOperation_${i}`, {
            value: Math.random() * 100,
            timestamp: Date.now(),
            reliable: true,
            source: 'measured'
          });
          largeMetrics.set(`scheduleCleanup_${i}`, {
            value: Math.random() * 50,
            timestamp: Date.now(),
            reliable: true,
            source: 'measured'
          });
        }

        const mockSnapshot = {
          metrics: largeMetrics,
          timestamp: Date.now(),
          reliable: true,
          warnings: []
        };

        (timingManager as any)._metricsCollector = {
          createSnapshot: jest.fn().mockReturnValue(mockSnapshot)
        };

        const startTime = Date.now();
        const metrics = await timingManager.getTimingMetrics();
        const endTime = Date.now();

        // Should complete within reasonable time (< 100ms)
        expect(endTime - startTime).toBeLessThan(100);

        // Should handle large dataset correctly
        expect(metrics.operationCount).toBe(2000);
        expect(metrics.totalDuration).toBeGreaterThan(0);
        expect(metrics.coordinationOverhead).toBeGreaterThan(0);
      });
    });
  });
});
